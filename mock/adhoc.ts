export default {
  'POST /omnipilot/api/base/info/get/data-types行行行': {
    code: 200,
    success: true,
    data: [
      {
        dataTypeId: 'dv1',
        dataTypeName: '交易数据',
        fields: [
          {
            id: 'f1',
            titleName: 'platform',
            type: 'dimension',
            alias: '平台',
            dataType: 'STRING',
          },
          {
            id: 'f3',
            titleName: 'date',
            type: 'dimension',
            alias: '日期',
            dataType: 'DATE',
          },
          {
            id: 'f4',
            titleName: 'province',
            type: 'dimension',
            alias: '省份',
            dataType: 'STRING',
          },
          {
            id: 'f5',
            titleName: 'city',
            type: 'dimension',
            alias: '城市',
            dataType: 'STRING',
          },
          {
            id: 'f6',
            titleName: 'region',
            type: 'dimension',
            alias: '大区',
            dataType: 'STRING',
          },
          {
            id: 'f7',
            titleName: 'category_l1',
            type: 'dimension',
            alias: '一级分类',
            dataType: 'STRING',
          },
          {
            id: 'f8',
            titleName: 'category_l2',
            type: 'dimension',
            alias: '二级分类',
            dataType: 'STRING',
          },
          {
            id: 'f9',
            titleName: 'sub_brand',
            type: 'dimension',
            alias: '子品牌',
            dataType: 'STRING',
          },
          {
            id: 'f10',
            titleName: 'upc',
            type: 'dimension',
            alias: 'upc',
            dataType: 'STRING',
          },
          {
            id: 'f11',
            titleName: 'product_name',
            type: 'dimension',
            alias: '商品名称',
            dataType: 'STRING',
          },
          {
            id: 'f12',
            titleName: 'retailer',
            type: 'dimension',
            alias: '零售商',
            dataType: 'STRING',
          },
          {
            id: 'f13',
            titleName: 'standard_retailer',
            type: 'dimension',
            alias: '标准零售商',
            dataType: 'STRING',
          },
          {
            id: 'f14',
            titleName: 'channel_type',
            type: 'dimension',
            alias: '渠道类型',
            dataType: 'STRING',
          },
          {
            id: 'f15',
            titleName: 'gmv',
            type: 'metric',
            alias: 'GMV',
            dataType: 'NUMBER',
          },
          {
            id: 'f16',
            titleName: 'sales_qty',
            type: 'metric',
            alias: '销量',
            dataType: 'NUMBER',
          },
        ],
      },
    ],
  },
  'POST /omnipilot/api/self-analysis/executexxx': {
    code: 200,
    success: true,
    data: {
      rows: [
        {
          platform: '美团',
          date: '2024-03-01',
          province: '广东',
          city: '深圳',
          region: '华南',
          category_l1: '食品',
          category_l2: '零食',
          sub_brand: '乐事',
          upc: '6901234567890',
          product_name: '乐事薯片原味',
          retailer: '便利蜂',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 1250.5,
          sales_qty: 125,
        },
        {
          platform: '饿了么',
          date: '2024-03-01',
          province: '北京',
          city: '北京',
          region: '华北',
          category_l1: '饮料',
          category_l2: '碳酸饮料',
          sub_brand: '可口可乐',
          upc: '6901234567891',
          product_name: '可口可乐330ml',
          retailer: '全家',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 980.0,
          sales_qty: 196,
        },
        {
          platform: '美团',
          date: '2024-03-02',
          province: '上海',
          city: '上海',
          region: '华东',
          category_l1: '酒水',
          category_l2: '啤酒',
          sub_brand: '青岛啤酒',
          upc: '6901234567892',
          product_name: '青岛啤酒经典',
          retailer: '罗森',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 2100.0,
          sales_qty: 300,
        },
        {
          platform: '饿了么',
          date: '2024-03-02',
          province: '浙江',
          city: '杭州',
          region: '华东',
          category_l1: '食品',
          category_l2: '方便面',
          sub_brand: '康师傅',
          upc: '6901234567893',
          product_name: '红烧牛肉面',
          retailer: '7-11',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 1500.0,
          sales_qty: 300,
        },
        {
          platform: '美团',
          date: '2024-03-03',
          province: '四川',
          city: '成都',
          region: '西南',
          category_l1: '饮料',
          category_l2: '茶饮料',
          sub_brand: '康师傅',
          upc: '6901234567894',
          product_name: '冰红茶',
          retailer: '全家',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 850.0,
          sales_qty: 170,
        },
        {
          platform: '饿了么',
          date: '2024-03-03',
          province: '湖北',
          city: '武汉',
          region: '华中',
          category_l1: '食品',
          category_l2: '饼干',
          sub_brand: '奥利奥',
          upc: '6901234567895',
          product_name: '奥利奥原味',
          retailer: '罗森',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 1680.0,
          sales_qty: 240,
        },
        {
          platform: '美团',
          date: '2024-03-04',
          province: '广东',
          city: '广州',
          region: '华南',
          category_l1: '酒水',
          category_l2: '白酒',
          sub_brand: '茅台',
          upc: '6901234567896',
          product_name: '飞天茅台',
          retailer: '便利蜂',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 5600.0,
          sales_qty: 28,
        },
        {
          platform: '饿了么',
          date: '2024-03-04',
          province: '江苏',
          city: '南京',
          region: '华东',
          category_l1: '食品',
          category_l2: '糖果',
          sub_brand: '徐福记',
          upc: '6901234567897',
          product_name: '奶糖',
          retailer: '7-11',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 920.0,
          sales_qty: 184,
        },
        {
          platform: '美团',
          date: '2024-03-05',
          province: '辽宁',
          city: '沈阳',
          region: '东北',
          category_l1: '饮料',
          category_l2: '果汁',
          sub_brand: '汇源',
          upc: '6901234567898',
          product_name: '橙汁',
          retailer: '全家',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 750.0,
          sales_qty: 150,
        },
        {
          platform: '饿了么',
          date: '2024-03-05',
          province: '陕西',
          city: '西安',
          region: '西北',
          category_l1: '食品',
          category_l2: '膨化食品',
          sub_brand: '上好佳',
          upc: '6901234567899',
          product_name: '虾条',
          retailer: '罗森',
          standard_retailer: '便利店',
          channel_type: '线下零售',
          gmv: 480.0,
          sales_qty: 96,
        },
      ],
    },
  },
  'POST /omnipilot/api/base/info/getxxx': {
    code: 200,
    success: true,
    data: {
      values: [
        {
          value: '1',
          label: '1',
        },
        {
          value: '2',
          label: '2',
        },
        {
          value: '3',
          label: '3',
        },
        {
          value: '4',
          label: '4',
        },
      ],
    },
  },
};
