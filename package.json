{"name": "omnipilot-web", "version": "1.0.1", "private": true, "description": "易数中心化中后台", "workspaces": ["packages/**"], "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "build:production": "cross-env UMI_ENV=production max build", "dev": "npm run start:dev", "genapi": "mps-genapi", "lint": "npm run lint:js && npm run lint:prettier", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:prod": "cross-env REACT_APP_ENV=prod MOCK=none UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^2.1.2", "@ant-design/icons": "^4.8.1", "@ant-design/plots": "^2.2.6", "@ant-design/pro-components": "^2.7.9", "@antv/g-svg": "^2.0.10", "@antv/g2": "^5.2.2", "@antv/larkmap": "^1.5.0", "@antv/s2": "^2.0.0", "@antv/s2-react": "^2.0.1", "@fit-screen/react": "^1.0.1", "@formkit/auto-animate": "^0.8.2", "@jiaminghi/charts": "^0.2.18", "@jiaminghi/data-view-react": "^1.2.5", "@sentry/react": "8.38.0", "ahooks": "^3.7.11", "animate.css": "^4.1.1", "antd": "^5.12.7", "antd-style": "^3.6.1", "classnames": "^2.5.1", "d3-scale-chromatic": "^3.1.0", "dagre": "^0.8.5", "dayjs": "^1.11.10", "dnd-core": "^16.0.1", "exceljs": "^4.4.0", "immutability-helper": "^3.1.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.461.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "qs": "^6.12.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-lineto": "^3.3.0", "react-xarrows": "^2.0.2", "recordrtc": "^5.6.2", "sa-sdk-javascript": "1.26.18", "ua-parser-js": "^1.0.39", "uuid": "^10.0.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zustand": "^4.5.2"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@mps/eslint-config-react": "^1.0.0", "@mps/prettier-config-react": "^1.0.0", "@mps/sentry-react": "^1.1.4", "@mps/swagger-cli": "workspace:*", "@mps/types": "^1.0.1", "@testing-library/react": "^13.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/d3-scale-chromatic": "^3.0.3", "@types/dagre": "^0.7.52", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/lodash.debounce": "^4.0.9", "@types/nprogress": "^0.2.3", "@types/numeral": "^2.0.5", "@types/qs": "^6.9.15", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@types/recordrtc": "^5.6.14", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "6", "@typescript-eslint/parser": "6", "@umijs/max": "^4.1.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "express": "^4.18.2", "husky": "^9.0.11", "lint-staged": "^15.2.5", "postcss-pxtorem": "^6.1.0", "prettier": "3.2.5", "prettier-plugin-packagejson": "^2.5.0", "tailwindcss": "^3", "typescript": "^4.9.5", "umi-presets-pro": "^2.0.3"}}