/**
 * 定制化 Pont
 * https://github.com/alibaba/pont/blob/master/docs/customizedPont.md
 */
import type { Interface, Mod } from 'pont-engine';
import {
  CodeGenerator as PontCodeGenerator,
  FileStructures as PontFileStructures,
} from 'pont-engine';
import { Surrounding, reviseModName } from 'pont-engine/lib/utils';

/**
 * 代码生成器
 * https://github.com/alibaba/pont/blob/master/docs/customizedPont.md#codegenerator
 */
export default class CodeGenerator extends PontCodeGenerator {
  /** 站点内容 */
  getIndex() {
    const { name } = this.dataSource;

    return `
      import type { api } from './api';
      import * as mods from './mods';

      export const ${name}: typeof api = mods as any;
    `;
  }

  /** 模块入口 */
  getModsIndex() {
    const { mods } = this.dataSource;

    return mods
      .map((mod) => {
        const name = reviseModName(mod.name);
        return `export * as ${name} from './${name}';`;
      })
      .join('\n');
  }

  /** 模块内容 */
  getModIndex(mod: Mod) {
    return mod.interfaces
      .map((inter) => {
        return `export * from './${inter.name}';`;
      })
      .join('\n');
  }

  /** 接口内容 */
  getInterfaceContent(inter: Interface) {
    return `
      /**
       * @desc ${inter.description}
       */

      import { request } from '@umijs/max';

      export function ${inter.name} (params: any = {}, options?: any) {
        const {
          method = '${inter.method}',
          ...rest,
        } = options ?? {};

        const url = '/${this.dataSource.name}/api${inter.path}'.replace(/{([^}]+)}/g, (s, s1) => params[s1]);

        return request(url, {
          ...rest,
          method,
          ${inter.parameters.find((param) => param.in === 'body') ? 'data' : 'params'}: params,
        });
      }
    `;
  }

  /** 汇总类型 */
  getDeclaration() {
    const { name, mods, baseClasses } = this.dataSource;

    return (
      `
      import type { RequestOptions } from '@umijs/max';

      type RequestOptions = RequestOptions;

      export namespace api {
        ${mods
          .map((mod) => {
            return `
              /**
               * ${mod.description}
               */
              export namespace ${reviseModName(mod.name)} {
                ${mod.interfaces
                  .map((inter) => this.getInterfaceContentInDeclaration(inter))
                  .join('')}
              }
            `;
          })
          .join('')}
      }

      export namespace defs {
        ${baseClasses
          .map((base) => {
            return `
              export ${this.getBaseClassInDeclaration(base)}
            `;
          })
          .join('')}
      }
    `
        .replaceAll('ObjectMap', 'Record')
        .replaceAll(`defs.${name}`, 'defs')
        // 不知道怎么改, 先这样吧
        .replaceAll('Record<any, Array<defs.DimProductVo>>', 'T0')
    );
  }

  /** 接口类型 */
  getInterfaceContentInDeclaration(inter: Interface) {
    const getParamsCode = (type: 'path' | 'query' | 'body') => {
      const params = inter.parameters.filter((param) => param.in === type);

      /** 后端 swagger 定义约定 */
      if (type === 'body' && params.length === 1) {
        let code: string | undefined;
        params[0]
          .toPropertyCode(Surrounding.typeScript, true)
          .replace(/ ([^ ]+);/, (s: string, s1: string) => {
            code = s1;
          });
        return code;
      }

      if (params.length) {
        return `
          {
            ${params.map((param) => param.toPropertyCode(Surrounding.typeScript, true)).join('')}
          }
        `;
      }
    };

    const params = {
      path: getParamsCode('path'),
      query: getParamsCode('query'),
      body: getParamsCode('body'),
    };

    const hasParams = inter.parameters.find((param) =>
      Object.keys(params).includes(param.in),
    );

    const requestParams = hasParams
      ? `params: ${Object.values(params).filter(Boolean).join(' & ')}`
      : `params?: {}`;

    function extractInnerLayer(inputString: string) {
      const regex = /<([^<>]+)>/;
      const match = inputString.match(regex);
      return match ? match[1] : 'any';
    }
    return `
      /**
        * ${inter.description}
        * ${inter.path}
        */
      export function ${inter.name}(${requestParams}, options?: RequestOptions): Promise<${inter.responseType}>;
    `;
  }
}

/**
 * 文件结构生成
 * https://github.com/alibaba/pont/blob/master/docs/customizedPont.md#filestructures
 */
export class FileStructures extends PontFileStructures {
  getDataSourcesTs() {
    const names = this.getMultipleOriginsDataSourceName();

    return `
      export * as defs from './api.d';

      ${names
        .map((name) => {
          return `import { ${name} } from './${name}';`;
        })
        .join('\n')}

      export const api = { ${names.join(',\n')} };
      export default api
    `;
  }

  getDataSourcesDeclarationTs() {
    const names = this.getMultipleOriginsDataSourceName();

    return `
      ${names
        .map((name) => {
          return `export type { defs as ${name} } from './${name}/api';`;
        })
        .join('\n')}
    `;
  }
}
