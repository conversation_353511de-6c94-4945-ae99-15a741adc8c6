#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Config, Manager } = require('pont-engine');

main();

async function main() {
  const json = JSON.parse(
    fs.readFileSync(path.resolve(process.cwd(), '.swaggerrc'), 'utf8'),
  );

  const config = new Config({
    outDir: 'src/services',
    templatePath: path.relative(process.cwd(), __dirname + '/template'),
    templateType: 'fetch',
    usingMultipleOrigins: true,
    usingOperationId: false,
    origins: [],
    originType: 'SwaggerV2',
    ...json,
  });

  // 兼容 node v12
  if (fs.rmSync) {
    fs.rmSync(path.resolve(config.outDir), { recursive: true, force: true });
  } else {
    fs.rmdirSync(path.resolve(config.outDir), { recursive: true });
  }

  const manager = new Manager(process.cwd(), config);
  manager.allLocalDataSources = await Promise.all(
    manager.allConfigs.map(async (config) => {
      const dataSource = await manager.readRemoteDataSource(config);

      dataSource.mods = dataSource.mods.filter((mod) => {
        if (Array.isArray(config.include)) {
          const exist = config.include.includes(mod.description);

          if (!exist) {
            // console.error(`[${mod.name}@${dataSource.name}] ${mod.description}: 未命中白名单`);
            return false;
          }
        }

        const valid = /^[A-Za-z0-9_-]+$/.test(mod.name);

        if (!valid) {
          // console.error(`[${mod.name}@${dataSource.name}] ${mod.description}: 命名不合法`);
          return false;
        }

        console.log(
          `[${mod.name}@${dataSource.name}] ${mod.description}: 创建成功`,
        );

        return true;
      });

      return dataSource;
    }),
  );

  const files = manager.getGeneratedFiles();
  // manager.fileManager.formatFile = (code) => code;
  await manager.fileManager.regenerate(files);
}
