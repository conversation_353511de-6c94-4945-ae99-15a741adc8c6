import { defineConfig } from '@umijs/max';

import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV = 'dev' } = process.env;
const config: ReturnType<typeof defineConfig> = defineConfig({
  hash: true,
  favicons: ['/favicon.png'],
  tailwindcss: {},
  routes,
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    'root-entry-name': 'variable',
  },
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  // https: {
  //   http2: false,
  // },
  fastRefresh: true,
  model: {},
  initialState: {},
  title: '易数',
  layout: {
    locale: false,
  },
  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration'],
  },
  antd: {},
  request: {
    dataField: 'data',
  },
  access: {},
  headScripts: [
    // 解决首次加载时白屏的问题
    // {
    //   src: '/scripts/loading.js',
    //   defer: true,
    // },
  ],
  //================ pro 插件配置 =================
  presets: ['umi-presets-pro'],
  mock: {
    include: ['mock/**/*', 'src/pages/**/_mock.ts'],
  },
  mfsu: {
    strategy: 'normal',
  },
  esbuildMinifyIIFE: true,
  requestRecord: {},
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  extraPostCSSPlugins: [
    // 使用 postcss 插件只针对 rem.less 文件
    require('postcss-pxtorem')({
      rootValue: 100, // 根据设计稿宽度 1920/10 设置
      propList: ['*'], // 需要转换的属性，['*'] 表示所有属性都转换
      selectorBlackList: [], // 不进行转换的选择器
      replace: true,
      mediaQuery: false,
      minPixelValue: 0, // 最小的转换单位
      exclude: (file) => !file.endsWith('.rem.less'),
    }),
  ],
});
export default config;
