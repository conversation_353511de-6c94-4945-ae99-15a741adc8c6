/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
const proxyTargetTest = 'http://*************:8089';
// const proxyTargetTest = 'http://*************:8087';

// const proxyTargetProd = 'https://omnipilot.mpsmedia.com.cn';
const proxyTargetProd = 'http://*************:8086';

// const proxyTargetLocal = 'http://*************:5009';
// const proxyTargetLocal = 'http://***************:5009';
const proxyTargetLocal = 'http://localhost:5009';
export default {
  // 开发环境
  dev: {
    '/bims/': {
      target: proxyTargetLocal,
      changeOrigin: true,
      pathRewrite: { '^/bims': '' },
      secure: false,
    },
    // '/bims/static/': {
    //   target: proxyTargetTest,
    //   changeOrigin: true,
    //   pathRewrite: { '^/bims': '' },
    //   secure: false,
    // },
    '/omnipilot/api': {
      // target: proxyTargetTest,
      target: proxyTargetTest,
      changeOrigin: true,
      secure: false,
    },
    '/auth/api/v1/open': {
      target: proxyTargetProd,
      // target: proxyTargetTest,
      changeOrigin: true,
      secure: false,
      // pathRewrite: { '^/omnipilot': '' },
    },
    '/analyst/api': {
      target: 'https://analyst.mpso2o.com',
      changeOrigin: true,
      secure: false,
      pathRewrite: { '^/analyst/api': '' },
    },
    '/analyst/v2': {
      target: 'https://assistant.yidigi.com',
      changeOrigin: true,
      secure: false,
      pathRewrite: { '^/analyst/v2': '' },
    },
    '/auth/api/v1/open/tracking': {
      // target: 'http://*************:8085',
      target: proxyTargetTest,
      changeOrigin: true,
      secure: false,
    },
  },
  // 测试环境
  test: {},
  // 预发布环境
  pre: {},
  // 生产环境
  prod: {},
};
