import { defineConfig } from '@umijs/max';

type Routes = Parameters<typeof defineConfig>[0]['routes'];
const routes: Routes = [
  {
    path: '/account',
    layout: false,
    routes: [
      {
        path: '/account/login',
        layout: false,
        name: '登录',
        component: './user/login',
      },
      { path: '/account', redirect: '/account/login' },
      { component: '404', path: '/account/*' },
    ],
  },
  {
    path: '/welcome',
    name: 'loading...',
    layout: false,
    component: '@/pages/welcome/index',
  },
  {
    path: '/fullAmountBI',
    name: '全量看板',
    component: '@/layouts/embed.layout',
    access: 'pageAccess',
    routes: [
      {
        name: '经营概览',
        icon: 'DotChartOutlined',
        path: '/fullAmountBI/dataOverview',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      {
        name: '销售看板',
        icon: 'BarChartOutlined',
        path: '/fullAmountBI/salesDashboard',
        access: 'pageAccess',
        routes: [
          {
            name: '品牌报告',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/salesDashboard/brandReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '地域报告',
            icon: 'HeatMapOutlined',
            path: '/fullAmountBI/salesDashboard/regionalReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '渠道报告',
            icon: 'AreaChartOutlined',
            path: '/fullAmountBI/salesDashboard/channelReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '日报报告',
            icon: 'AreaChartOutlined',
            path: '/fullAmountBI/salesDashboard/dailyReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '数据下载',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/salesDashboard/dataDownload',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '活动看板',
        icon: 'LineChartOutlined',
        path: '/fullAmountBI/actData',
        access: 'pageAccess',
        routes: [
          {
            name: '活动报告',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/actData/actReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '数据下载',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/actData/actDataDownload',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '供给看板',
        icon: 'FundOutlined',
        path: '/fullAmountBI/supplyMarket',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      {
        name: 'RTB看板',
        icon: 'SlidersOutlined',
        path: '/fullAmountBI/marketing',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      {
        name: '自动化报告',
        icon: 'RedoOutlined',
        path: '/fullAmountBI/automated-reports',
        access: 'pageAccess',
        routes: [
          {
            name: '经营日报',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/automated-reports/daily',
            component: '@/pages/AutomatedReports/AutomatedReportsDaily',
            access: 'pageAccess',
          },
          {
            name: '经营周报',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/automated-reports/weekly',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '活动日报',
            icon: 'LineChartOutlined',
            path: '/fullAmountBI/automated-reports/activity-daily',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '整体投资表现',
        icon: 'AreaChartOutlined',
        path: '/fullAmountBI/InvestmentPerformance',
        access: 'pageAccess',
        routes: [
          {
            name: '平台维度',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/InvestmentPerformance/platform',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '客户维度',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/InvestmentPerformance/customer',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '品牌维度',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/InvestmentPerformance/brand',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '上翻报告',
        icon: 'FundViewOutlined',
        path: '/fullAmountBI/o2oreport',
        access: 'pageAccess',
        routes: [
          {
            path: '/fullAmountBI/o2oreport',
            redirect: '/fullAmountBI/o2oreport/meituan',
          },
          {
            name: '美团',
            path: '/fullAmountBI/o2oreport/meituan',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '饿了么',
            path: '/fullAmountBI/o2oreport/eleme',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '京东到家',
            path: '/fullAmountBI/o2oreport/jd',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '基础看板',
        icon: 'BoxPlotOutlined',
        path: '/fullAmountBI/basicVersion',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      {
        name: '医药看板',
        icon: 'AreaChartOutlined',
        path: '/fullAmountBI/MedicalDashboard',
        access: 'pageAccess',
        routes: [
          {
            name: '交易概览',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/MedicalDashboard/TransactionOverview',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '连锁分析',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/MedicalDashboard/ChainAnalysis',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '品规数据',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/MedicalDashboard/ProductAnalytics',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '供给分析',
            icon: 'DotChartOutlined',
            path: '/fullAmountBI/MedicalDashboard/SupplyAnalysis',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '日报报告',
            icon: 'AreaChartOutlined',
            path: '/fullAmountBI/MedicalDashboard/dailyReport',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
          {
            name: '数据下载',
            icon: 'VerticalAlignBottomOutlined',
            path: '/fullAmountBI/MedicalDashboard/DataDownload',
            component: '@/components/EmbedIframe',
            access: 'pageAccess',
          },
        ],
      },
      {
        name: '财务看板',
        icon: 'MoneyCollectOutlined',
        path: '/fullAmountBI/financialDashboard',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      {
        name: '数据下载',
        icon: 'VerticalAlignBottomOutlined',
        path: '/fullAmountBI/dataDownload',
        component: '@/components/EmbedIframe',
        access: 'pageAccess',
      },
      { component: '404', path: '/fullAmountBI/*' },
    ],
  },
  {
    name: '自助分析',
    path: '/adhoc',
    component: '@/layouts/page.layout',
    access: 'pageAccess',
    routes: [
      {
        path: '/adhoc/analysis',
        component: '@/pages/adhoc/analysis',
        name: '自助分析',
        icon: 'DotChartOutlined',
        access: 'pageAccess',
      },
      {
        path: '/adhoc/list',
        component: '@/pages/adhoc/list',
        name: '已创建列表',
        icon: 'DotChartOutlined',
        access: 'pageAccess',
      },
    ],
  },
  {
    path: '/dashboard',
    name: '归因诊断',
    component: '@/layouts/embed.layout',
    access: 'pageAccess',
    routes: [
      { component: '404', path: '/dashboard/*' },
      {
        name: 'GMV-诊断模型',
        icon: 'CarOutlined',
        path: '/dashboard/GMV',
        component: './dashboard/gmv/index',
        access: 'pageAccess',
      },
      {
        name: '指标归因可视化',
        icon: 'LineChartOutlined',
        path: '/dashboard/GMV-wave',
        component: './dashboard/gmv-wave/index',
        access: 'pageAccess',
      },
    ],
  },
  {
    path: '/AI',
    name: 'AI分析师',
    component: '@/layouts/embed.layout',
    access: 'pageAccess',
    routes: [
      {
        name: 'AI分析师',
        icon: 'DesktopOutlined',
        path: '/AI/aiAnalyst',
        component: '@/pages/AI/aiAnalyst/index',
        access: 'pageAccess',
      },
      { component: '404', path: '/AI/*' },
    ],
  },
  {
    path: '/getDataTools',
    name: '取数工具',
    component: '@/layouts/embed.layout',
    access: 'pageAccess',
    routes: [
      {
        name: '美团平台取数工具',
        icon: 'DesktopOutlined',
        path: '/getDataTools/Meituan',
        access: 'pageAccess',
        routes: [
          {
            name: '主菜单',
            icon: 'AppstoreOutlined',
            path: '/getDataTools/Meituan/main',
            component: '@/pages/GetDataTools/Meituan/main/index',
            access: 'pageAccess',
          },
          {
            name: '销售投资表现',
            icon: 'FundOutlined',
            path: '/getDataTools/Meituan/salesInvestment',
            access: 'pageAccess',
            routes: [
              {
                name: '行业指数',
                path: '/getDataTools/Meituan/salesInvestment/industryIndex',
                component:
                  '@/pages/GetDataTools/Meituan/salesInvestment/industryIndex/index',
                access: 'pageAccess',
              },
              {
                name: '市占率变化情况',
                path: '/getDataTools/Meituan/salesInvestment/marketShareChange',
                component:
                  '@/pages/GetDataTools/Meituan/salesInvestment/marketShareChange/index',
                access: 'pageAccess',
              },
              {
                name: '全量+活动数据',
                path: '/getDataTools/Meituan/salesInvestment/fullActivityData',
                component:
                  '@/pages/GetDataTools/Meituan/salesInvestment/fullActivityData/index',
                access: 'pageAccess',
              },
              {
                name: '城市订单数据',
                path: '/getDataTools/Meituan/salesInvestment/cityOrderData',
                component:
                  '@/pages/GetDataTools/Meituan/salesInvestment/cityOrderData/index',
                access: 'pageAccess',
              },
              {
                name: 'RTB商品数据',
                path: '/getDataTools/Meituan/salesInvestment/rtbProductData',
                component:
                  '@/pages/GetDataTools/Meituan/salesInvestment/rtbProductData/index',
                access: 'pageAccess',
              },
            ],
          },
          {
            name: '交易用户表现',
            icon: 'UserOutlined',
            path: '/getDataTools/Meituan/transactionUsers',
            access: 'pageAccess',
            routes: [
              {
                name: '品牌交易用户数',
                path: '/getDataTools/Meituan/transactionUsers/brandTransactionUsers',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/brandTransactionUsers/index',
                access: 'pageAccess',
              },
              {
                name: '品牌省份交易用户数',
                path: '/getDataTools/Meituan/transactionUsers/brandProvinceUsers',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/brandProvinceUsers/index',
                access: 'pageAccess',
              },
              {
                name: '新老客商品偏好类',
                path: '/getDataTools/Meituan/transactionUsers/customerProductPreference',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/customerProductPreference/index',
                access: 'pageAccess',
              },
              {
                name: 'Better人群分布',
                path: '/getDataTools/Meituan/transactionUsers/betterUserDistribution',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/betterUserDistribution/index',
                access: 'pageAccess',
              },
              {
                name: 'Better流转分析',
                path: '/getDataTools/Meituan/transactionUsers/betterFlowAnalysis',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/betterFlowAnalysis/index',
                access: 'pageAccess',
              },
              {
                name: 'TA洞察新老客(日)',
                path: '/getDataTools/Meituan/transactionUsers/taInsightCustomersDaily',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/taInsightCustomersDaily/index',
                access: 'pageAccess',
              },
              {
                name: 'TA洞察新老客(月)',
                path: '/getDataTools/Meituan/transactionUsers/taInsightCustomersMonthly',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/taInsightCustomersMonthly/index',
                access: 'pageAccess',
              },
              {
                name: '复购人群交易频次(E+R)',
                path: '/getDataTools/Meituan/transactionUsers/repurchaseFrequency',
                component:
                  '@/pages/GetDataTools/Meituan/transactionUsers/repurchaseFrequency/index',
                access: 'pageAccess',
              },
            ],
          },
          {
            name: '人群表现',
            icon: 'TeamOutlined',
            path: '/getDataTools/Meituan/userGroups',
            access: 'pageAccess',
            routes: [
              {
                name: '十大人群',
                path: '/getDataTools/Meituan/userGroups/topTenGroups',
                component:
                  '@/pages/GetDataTools/Meituan/userGroups/topTenGroups/index',
                access: 'pageAccess',
              },
              {
                name: '十大人群规模占比/增速/行业渗透榜',
                path: '/getDataTools/Meituan/userGroups/topTenGroupsMetrics',
                component:
                  '@/pages/GetDataTools/Meituan/userGroups/topTenGroupsMetrics/index',
                access: 'pageAccess',
              },
              {
                name: '十大人群X新老客',
                path: '/getDataTools/Meituan/userGroups/topTenGroupsCustomers',
                component:
                  '@/pages/GetDataTools/Meituan/userGroups/topTenGroupsCustomers/index',
                access: 'pageAccess',
              },
            ],
          },
          {
            name: '场景表现',
            icon: 'AppstoreOutlined',
            path: '/getDataTools/Meituan/scenarios',
            access: 'pageAccess',
            routes: [
              {
                name: '十六大场景',
                path: '/getDataTools/Meituan/scenarios/sixteenScenarios',
                component:
                  '@/pages/GetDataTools/Meituan/scenarios/sixteenScenarios/index',
                access: 'pageAccess',
              },
              {
                name: '十六大场景规模占比/增速/行业渗透榜',
                path: '/getDataTools/Meituan/scenarios/sixteenScenariosMetrics',
                component:
                  '@/pages/GetDataTools/Meituan/scenarios/sixteenScenariosMetrics/index',
                access: 'pageAccess',
              },
              {
                name: '十六大场景X新老客',
                path: '/getDataTools/Meituan/scenarios/sixteenScenariosCustomers',
                component:
                  '@/pages/GetDataTools/Meituan/scenarios/sixteenScenariosCustomers/index',
                access: 'pageAccess',
              },
              {
                name: '十大人群X十六大场景',
                path: '/getDataTools/Meituan/scenarios/topTenGroupsSixteenScenarios',
                component:
                  '@/pages/GetDataTools/Meituan/scenarios/topTenGroupsSixteenScenarios/index',
                access: 'pageAccess',
              },
            ],
          },
          {
            name: '供给表现',
            icon: 'ShoppingOutlined',
            path: '/getDataTools/Meituan/supply',
            access: 'pageAccess',
            routes: [
              {
                name: 'UPCX零售商X城市',
                path: '/getDataTools/Meituan/supply/upcRetailerCity',
                component:
                  '@/pages/GetDataTools/Meituan/supply/upcRetailerCity/index',
                access: 'pageAccess',
              },
              {
                name: '子品牌X零售商X城市',
                path: '/getDataTools/Meituan/supply/subBrandRetailerCity',
                component:
                  '@/pages/GetDataTools/Meituan/supply/subBrandRetailerCity/index',
                access: 'pageAccess',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: '/strategic',
    name: '战略大屏',
    component: '@/layouts/page.layout',
    access: 'pageAccess',
    routes: [
      {
        name: '战略大屏',
        icon: 'DesktopOutlined',
        path: '/strategic/strategicScreen',
        component: './strategic/strategicScreen',
        access: 'pageAccess',
      },
      { component: '404', path: '/strategic/*' },
    ],
  },
  {
    path: '/baseInfo',
    name: '基础信息管理',
    component: '@/layouts/page.layout',
    access: 'pageAccess',
    routes: [
      {
        name: '商品管理',
        icon: 'ShoppingOutlined',
        path: '/baseInfo/goods',
        component: '@/pages/baseInfo/goods',
        access: 'pageAccess',
      },
      {
        name: '地域管理',
        icon: 'EnvironmentOutlined',
        path: '/baseInfo/region',
        component: '@/pages/baseInfo/region',
        access: 'pageAccess',
      },
      {
        name: '零售商管理',
        icon: 'ShopOutlined',
        path: '/baseInfo/retailer',
        component: '@/pages/baseInfo/retailer',
        access: 'pageAccess',
      },
      {
        name: '投放计划管理',
        icon: 'FundProjectionScreenOutlined',
        path: '/baseInfo/campaignPlan',
        component: '@/pages/baseInfo/campaignPlan',
        access: 'pageAccess',
      },
      {
        name: '全量数据上传',
        icon: 'CloudUploadOutlined',
        path: '/baseInfo/fullDataUpload',
        component: '@/pages/baseInfo/fullDataUpload',
        access: 'pageAccess',
      },
      {
        name: '活动账单数据上传',
        icon: 'FileExcelOutlined',
        path: '/baseInfo/campaignBillUpload',
        component: '@/pages/baseInfo/campaignBillUpload',
        access: 'pageAccess',
      },
      {
        name: '上传记录',
        icon: 'UploadOutlined',
        path: '/baseInfo/uploadRecord',
        component: '@/pages/baseInfo/uploadRecord',
        access: 'pageAccess',
      },
      { component: '404', path: '/baseInfo/*' },
    ],
  },
  {
    path: '/system',
    name: '账号统计',
    component: '@/layouts/embed.layout',
    access: 'pageAccess',
    routes: [
      {
        name: '用户使用统计',
        icon: 'FundOutlined',
        path: '/system/trace',
        component: '@/pages/system/trace',
        access: 'pageAccess',
      },
      { component: '404', path: '/system/*' },
    ],
  },
  { path: '/', redirect: '/welcome' },
  { component: '404', path: '/*' },
];

export default routes;
