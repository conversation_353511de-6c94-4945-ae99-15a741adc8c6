export const AUTHORIZATUION_TOKEN_NAME = 'Authorization_omniplot_Token';

export const USER_INFO_STORAGE_KEY = 'omniplot_user_info';

export const USER_ID_STORAGE_KEY = 'omniplot_user_id';

export const BRAND_INFO_STORAGE_KEY = 'omniplot_brand_info';

export const LOGIN_PAGE_PATH = '/account/login';

export const BASE_URL = '';

export const DOMAIN = '';

export const IFRAME_SRC_MAP_DEMO = [
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '自动化报告-活动日报',
    portal_path: '/fullAmountBI/automated-reports/activity-daily',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/bc1dd47f2387430ba9aa7e08e0d33911?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    name: '自动化报告-活动日报',
    portal_path: '/fullAmountBI/automated-reports/activity-daily',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c393f3adbb304291ad5b38453fa9a13e?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '嘉顿',
    name: '全量看板-经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/48b2e09db24c4de5a82105d30c3014ce?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '嘉顿',
    name: '全量看板-销售看板-品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/10b53408605e45b59a7be0ecaf9bc95c?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '嘉顿',
    name: '全量看板-销售看板-地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/6a48569c7490459899ed4361d217b88b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '嘉顿',
    name: '全量看板-销售看板-渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/41e69cc055734b9a873e80783b72e53d?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '嘉顿',
    name: '全量看板-销售看板-数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7e1bb76cdb344e53994a862990cd180c?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '全量看板-经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/838053d7b5714a629b1c4c42c374e4df?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '销售看板-品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/bb0a18d89ebb406eac515e52e3786f13?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '销售看板-地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c0f74e07a3f94ce38013a9734fdff186?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '销售看板-渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b6e1a954f8914868b66fd77af64abced?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '销售看板-数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a7aace29ed3f413aae9b92e4819d3b22?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    name: '销售看板-日报报告',
    portal_path: '/fullAmountBI/salesDashboard/dailyReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/cdc11f4ed3324e368639f790918a3999?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '全量看板-经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/838053d7b5714a629b1c4c42c374e4df?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '纽仕兰',
    name: '全量看板-销售看板-数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/04b78a704f7941e69571c16d00822a6f?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '雀巢',
    name: '全量看板-经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/67a89368761646b2a1abef8417dcab34?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '雀巢',
    name: '全量看板-销售看板-数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/23972ba619384e5298d6e09bbb024ac3?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '可口可乐',
    name: '全量看板-财务看板',
    portal_path: '/fullAmountBI/financialDashboard',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c55e50ac9c4647ffb334dfeb08ae050b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '全量看板-经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/d284097263744ffbb93a5ca005238f9d?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f7ff031488e244e3be7c13f3ae897f3c?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/341a399969c8472e9a1d2c56b4280d20?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a80194e9bf54475f900aef18b06fe7d2?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/e5504102bfd8431faa63aa0bcfd3d8a1?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/54edac8c40e84a80b892f4c39bc68a60?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '圣农',
    name: '活动数据下载',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/55a6184d41624e9694f42cdd3838b84b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '销售看板-日报报告',
    portal_path: '/fullAmountBI/salesDashboard/dailyReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ea6accce47b44395af902afe7fe5b98b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '急尼优',
    name: '医药看板-日报报告',
    portal_path: '/fullAmountBI/MedicalDashboard/dailyReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5104aa017b9240ee8f17bb3dd5a0bf15?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '急尼优',
    name: '医药看板-数据下载',
    portal_path: '/fullAmountBI/MedicalDashboard/DataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7993bee851f14af4ae1470d63f7e6017?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '赫力昂',
    name: '医药看板-交易概览',
    portal_path: '/fullAmountBI/MedicalDashboard/TransactionOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/393029b5048a4660a8949ccc65011fc7?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '赫力昂',
    name: '医药看板-连锁分析',
    portal_path: '/fullAmountBI/MedicalDashboard/ChainAnalysis',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f7bc52ac686b41cf972e9b887d5ce054?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '赫力昂',
    name: '医药看板-品规数据',
    portal_path: '/fullAmountBI/MedicalDashboard/ProductAnalytics',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/65827cedc5fd48d6b7f40e9fb27391a2?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '赫力昂',
    name: '医药看板-供给分析',
    portal_path: '/fullAmountBI/MedicalDashboard/SupplyAnalysis',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f72f0939f25e4841be8c69c3da0ee913?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '赫力昂',
    name: '医药看板-数据下载',
    portal_path: '/fullAmountBI/MedicalDashboard/DataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/61037d8bc22c4503bbba0c9f5230a7a6?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '百事食品',
    name: '活动看板-数据下载',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ad7a288c4b3a40d681ec437a7b175298?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '活动看板-活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/4e9ea7dd0c46414e8a5ff47751268bfc?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '活动看板-数据下载',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/938ccbcbef1741d083fe6859d05d0365?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    name: '活动看板-活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/405b6d8d12b84e59954bd573c8983a84?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    name: '活动看板-数据下载',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c8c3abcfaf06492c9cbe011644aa59d3?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    name: '活动看板-活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/34f9b06219a049449f051dff19c048fa?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    name: '活动看板-数据下载',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ab133e2cbe624d55bcbbcf27b2a394e4?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7869d68a064841c8b8079ec49293aec3?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/3e681c06d7a944559d781aebbcf28766?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c3302f53a18345c1b404a2709bf422d9?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0f812831f65845f3ad8fc0b780deb584?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5bdf282410ce430aa70c1297ef406ec9?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'xx品牌',
    name: '活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/73f369c8b9d44bc7b1e0f730d9ed0572?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '经营概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/60d138f5ac414f44be04ecfd6f92cfda?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/d1b23861ee9f4df69959281bf981d7cf?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c48658bb891b49d1be5bfe9638ad2fea?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ebaafffc82294003abd913c93b38587b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/d634285d3af04e3cbe8d018511492867?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳能',
    name: '活动报告',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0109f25caff248d99311f6c0ac04d8d0?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '优诺',
    name: '数据概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b102d2cf0457480d840c16ab7499a3c0?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '优诺',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7e6a7ecc95f744a3b06d7ef5f0f06979?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '优诺',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ba12b517599344f4b03e76da790e1dae?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '优诺',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b0f6fb56519d4d7db70bc79c99edd50d?type=NONE',
  },

  {
    sys_name: 'omnipilot',
    brand_name: '雪花啤酒',
    name: '数据概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f8b6502650cc497bbfd0c2e276632ead?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '雪花啤酒',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/50d4818a825d491d9e1e342870755d58?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '雪花啤酒',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/cfe102dc652d40378dcf6db13f0ea976?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '雪花啤酒',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7e9d08b037b74f9894845614604d131b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    name: '上翻报告-美团',
    portal_path: '/fullAmountBI/o2oreport/meituan',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/483dde046ba34037906c827a7919c765?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    name: '上翻报告-饿了么',
    portal_path: '/fullAmountBI/o2oreport/eleme',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/76ce0cb0adbc461da6511bbc5e14032a?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    name: '上翻报告-京东到家',
    portal_path: '/fullAmountBI/o2oreport/jd',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c5fbe20f0841409786f98db30390caa6?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    name: '数据概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/9e636754fc8b4bbfa8c8b2ed11a72566?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/1e570e7142bd47dc8e4bb53a528e875d?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0304ecc2500b4b5a8fca275bb3ec4b17?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '亿滋',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5b7dbb16211a4165a77953874322fbb3?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '通用磨坊',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/40b4facca13547d5b066935872eaf1a1?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '通用磨坊',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/1183442b9fc245128d637c9f072cd17a?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '通用磨坊',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/4fb1f8a3b97349e4a78d86ae5448cd83?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '通用磨坊',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/4b5c1c6a370f46338acab69b0d0d1bda?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '上好佳',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/188f917146b04102ae089a96d904015a?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '上好佳',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href: '',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '上好佳',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/bab79fc950d54705bdac35875a5470f7?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '上好佳',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0efd432c53994d28a3f9b14170056d6b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '保乐力加',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f74cc87291d44598af195c422d81e829?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '保乐力加',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/44097863abbe43a8b92f3cde55cf3b2d?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '保乐力加',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ed6c7cbe5363479f91c3b9f937074184?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '保乐力加',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/971330d56c6640d0af9baac848da3ca7?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/fded7ef3dca64fe2b869bba36136a222?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/d200403884094f05a267ed78996b791c?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/dc87f5f4f3ef4de2a2f11048ac905e85?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '好来',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ad6245ddfbfa4c9b883fbbbba468a7d9?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ad80c750b9bf4beda616891e108b0228?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/dcb85326fa0c4a5693bbc607e9ddd4a9?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5ca3497768644ce2b8f10bd656a3cdeb?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '海天',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/6a2d211849ca419783d84b461d57bbb7?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/1ad065e507274e339128dc776afff44f?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/3bf3e8bb749f4d4785d4e4754b0358d4?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/aab03e926b4b44bebf566525024c53ac?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/93d1335135b548ca8c10ba3c6094fbdf?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/33ab65edba0f42d7bd649e745e0d389a?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b463135a01f6409d82a75318f20529e5?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/8b841fc891f9473f8c33e9e94fcaf574?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/35ea56176ef54943aea889e73ac8d38a?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '太太乐',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/03f218ae1c354a428ad2e2b4f8957903?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '太太乐',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c10ab3bc37d24c1a82716c76b210ec79?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '太太乐',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/80554cbc39144d118db2eba79d8c9a29?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '太太乐',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/af86083f94f34d8ebf204d2afe749f1b?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '费列罗',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f18df264636644d8899237bf58b69837?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '费列罗',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/9faef8169e6f4d788f78802838530dc8?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '费列罗',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f155e7ab925d40c4936fd63dc98f1584?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '费列罗',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a35ad06cda02472b9f324fc1c566d358?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '科赴',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a52561a374ee4ec2b5d0e3448de4192c?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '科赴',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/1d8ce37d89694669b7c3cbfcbc433614?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '科赴',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a2400244e70a491e93861684463ab1b8?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '科赴',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/2532df61d6e74050a3d88558ef3c25ec?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5be0a1676b3e4c7595b5e233bc357f20?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ecef20af2edf46819bf530c3fa123585?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ed77c7b0ecb54009b1d08ff5092868f4?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/cb967ed7687a41aebb231f69b6c0d5e4?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/system/users',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7364fb78ebbd4156b8a8d79dc6644501?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/supplyMarket',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/a72133a82d1d40cebe86888a39882592?type=NONE',
  },
  {
    brand_name: 'demo',
    portal_path: '/fullAmountBI/marketing',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/660c9f401ec646e18d5ca9497d7f85a3?type=NONE',
  },
  {
    brand_name: '优诺',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/34ba48bab8bc4f72956d0e791e0c3b97?type=NONE',
  },
  {
    brand_name: '亿滋',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/7e3fcdd08e86439bb87b94ae724a1718?type=NONE',
  },
  {
    brand_name: '太太乐',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b63821b5b77c402cab278aa96fd24c7c?type=NONE',
  },
  {
    brand_name: '科赴',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/bea6814bc18d404b86ef4b5ee35bde8f?type=NONE',
  },
  {
    brand_name: '费列罗',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0b51eff7b5904ef2b14243cd59511e8f?type=NONE',
  },
  {
    brand_name: '通用磨坊',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/faa3288b05624acd85369a9794aeb5f4?type=NONE',
  },
  {
    brand_name: '保乐力加',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/e183642e935c402f9007f14f75350164?type=NONE',
  },
  {
    brand_name: '好来',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/52600a5e490048e3a3433894bf18fcc2?type=NONE',
  },
  {
    brand_name: '上好佳',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/6151132f4e6f47aa9d46e6b6aa073b43?type=NONE',
  },
  {
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/e3f43347d9094c8ba16a4867860ca00f?type=NONE',
  },
  {
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/ce03a469bc454a32b9d09d169f79f9d6?type=NONE',
  },
  {
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/105a80c65a0347508afb13a6fac5e5ed?type=NONE',
  },
  {
    brand_name: '百事食品',
    portal_path: '/fullAmountBI/actData/actReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/df28401550e34f6cb484f5cdb81d4321?type=NONE',
  },
  {
    brand_name: '可口可乐',
    portal_path: '/fullAmountBI/actData/actDataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b86425cb10854e8f90e648424c7aebb6?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '养乐多',
    name: '基础看板',
    portal_path: '/fullAmountBI/basicVersion',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/3555a3652dd34a62832e7733f30183d9?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: '佳沛',
    name: '基础看板',
    portal_path: '/fullAmountBI/basicVersion',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/1c7ad3dd8bb84a929cfef7ebae3a8bb4?type=NONE',
  },
  {
    sys_name: 'omnipilot',
    brand_name: 'demo',
    name: '基础看板',
    portal_path: '/fullAmountBI/basicVersion',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/b63e156706c947498c00a0b9ec9e1843?type=NONE',
  },
  {
    brand_name: '科赴',
    portal_path: '/fullAmountBI/InvestmentPerformance/platform',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/c7c32d175624411da4cf7fc7d0b8bf47?type=NONE',
  },
  {
    brand_name: '科赴',
    portal_path: '/fullAmountBI/InvestmentPerformance/customer',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/9f02491d9cd14c2aaa64525f3608fc5b?type=NONE',
  },
  {
    brand_name: '科赴',
    portal_path: '/fullAmountBI/InvestmentPerformance/brand',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/6a853941e4b749ea8e2addfd3b0dd960?type=NONE',
  },
  {
    brand_name: '水井坊',
    name: '数据概览',
    portal_path: '/fullAmountBI/dataOverview',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/3a1c32cc5e4d4a1ea0307d0a0c3b2865?type=NONE',
  },
  {
    brand_name: '水井坊',
    name: '品牌报告',
    portal_path: '/fullAmountBI/salesDashboard/brandReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/cd2ed46cdf6841ad9e0e649e545c72e0?type=NONE',
  },
  {
    brand_name: '水井坊',
    name: '地域报告',
    portal_path: '/fullAmountBI/salesDashboard/regionalReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/5cb3e22b31e547e29f921a992e8b75b9?type=NONE',
  },
  {
    brand_name: '水井坊',
    name: '渠道报告',
    portal_path: '/fullAmountBI/salesDashboard/channelReport',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/f587945a9d224f89b2689e55491beacb?type=NONE',
  },
  {
    brand_name: '水井坊',
    name: '数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/8851b99eabc044b4ac7831a7f3a49903?type=NONE',
  },
  {
    brand_name: '海天',
    name: '数据下载',
    portal_path: '/fullAmountBI/salesDashboard/dataDownload',
    datart_share_href:
      'https://data.mpsmedia.com.cn/shareDashboard/0c0db5818d5c48179e6404e8b3a95c4e?type=NONE',
  },
];
