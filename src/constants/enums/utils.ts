import api from '@/services';

import { PLAN_STATUS, PLAN_TYPE, USER_STATUS } from './index';

type TStatus = 'Success' | 'Error' | 'Processing' | 'Warning' | 'Default';
type ValueType = string | number;
type TOptions<T = React.ReactNode, U = ValueType> = {
  value: U;
  label: T | (string & {});
  status?: TStatus;
  color?: string;
  disabled?: boolean;
}[];

function enum2Options<T>(obj: Object) {
  const numberLabels = Object.values(obj).filter(
    (value) => typeof value === 'number',
  );
  const options = Object.entries(obj).filter(
    (item) => !numberLabels.includes(Number(item[0])),
  );
  return new Options(
    options?.map((item) => {
      return {
        label: item[0],
        value: item[1],
      };
    }),
  ) as T;
}

/**
 * @example
 * enum USER_STATUS {
 *   '启用',
 *   '禁用',
 * }
 * const userStatusOptions = new Options<keyof typeof USER_STATUS, USER_STATUS>([
 *   {
 *     label: '启用',
 *     value: USER_STATUS.启用,
 *   },
 * ]);
 * console.log('pro table valueEnum', userStatusOptions.valueEnum);
 * console.log('antd options', userStatusOptions.options);
 * console.log('取label', userStatusOptions.label.get(0)); // 启用
 * console.log('取value', userStatusOptions.value.get('启用')); // 0
 */
class Options<T = React.ReactNode, U = ValueType> {
  constructor(options: TOptions<T, U>) {
    this.data = options;
  }

  private data: TOptions<T, U>;

  public get options() {
    return this.data;
  }

  public get valueEnum() {
    const mapData = this.data.map((item) => {
      const { value, label, ...rest } = item;
      return [
        value,
        {
          text: label,
          ...rest,
        },
      ] as [
        U,
        {
          text: T;
          status?: TStatus;
          color?: string;
          disabled?: boolean;
        },
      ];
    });
    return new Map(mapData);
  }

  public get value() {
    const mapData = this.data.map((item) => {
      const { value, label } = item;
      return [label, value] as [T, U];
    });
    return new Map(mapData);
  }

  public get label() {
    const mapData = this.data.map((item) => {
      const { value, label } = item;
      return [value, label] as [U, T];
    });
    return new Map(mapData);
  }
}

/**
 * @example
 * options.enums.用户状态.label.get(0)
 * options.addEnum('计划状态', [{label: 'xx', value: '1'}]) // value这里有ts类型提示
 * options.enums.计划状态.label.get(1) // ts类型报错 上面定义了number 这里用了string
 */
class Enums<T = Record<string, Options>> {
  constructor(enums: T) {
    this.enums = enums;
  }
  enums: T;

  public addEnum<K extends keyof T>(
    key: K,
    option: TOptions<
      T[K] extends Options<infer R, any> ? R : never,
      T[K] extends Options<any, infer R> ? R : never
    >,
  ) {
    this.enums[key] = new Options(option) as T[K];
  }
}

type EnumsTypes = {
  用户状态: Options<keyof typeof USER_STATUS, USER_STATUS>;
  计划状态: Options<keyof typeof PLAN_STATUS, PLAN_STATUS>;
  计划类型: Options<keyof typeof PLAN_TYPE, PLAN_TYPE>; // 字符串的枚举套上`${}`不然options.enums.用户状态.label.get('0')这种写法报错
  // 配上类型，避免传错数据类型
  主品牌: Options<string, string>;
};

export const options = new Enums<EnumsTypes>({
  用户状态: new Options([
    {
      label: '启用',
      value: USER_STATUS.启用,
      status: 'Success',
    },
    {
      label: '禁用',
      value: USER_STATUS.禁用,
      status: 'Error',
    },
  ]),
  计划状态: enum2Options<EnumsTypes['计划状态']>(PLAN_STATUS),
  计划类型: enum2Options<EnumsTypes['计划类型']>(PLAN_TYPE),
  // 动态add的先弄个空的在这，不然ts没有类型提示
  主品牌: new Options([]),
});

export function getOnlineEnums() {
  // 主品牌列表
  api.aimofang.brand.getGetAllMain().then(({ data }) => {
    if (!data) {
      return;
    }
    options.addEnum(
      '主品牌',
      data?.map((item) => ({
        label: item.brandName!,
        value: item.brandId!,
      })),
    );
  });
}
