// -----prolayout 菜单样式覆盖 begin------
.ant-menu-title-content {
  transition: none !important;
  a {
    transition: none !important;
  }
}

.ant-pro-layout .ant-pro-sider-logo {
  border: none;
}

.ant-menu-light .ant-menu-submenu-title {
  &:hover {
    color: #0095ff !important;
  }
}

.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #ffffff !important;
  &:hover {
    color: #0095ff !important;
  }
}

.ant-layout-sider-collapsed
  .ant-menu-light
  .ant-menu-submenu-selected
  > .ant-menu-submenu-title {
  color: #0095ff !important;
  &:hover {
    color: #0095ff !important;
  }
}
.ant-menu-light.ant-menu-submenu-popup > .ant-menu {
  background-color: #0095ff;
}

.ant-layout-sider-children div {
  &::-webkit-scrollbar {
    display: none;
  }
}

.ant-pro-base-menu-horizontal-item-text {
  font-size: 18px !important;
}

.ant-pro-layout .ant-layout-header.ant-pro-layout-header {
  border: none !important;
}

.ant-pro-layout .ant-layout-header.ant-pro-layout-header-fixed-header {
  // width: calc(100% - 240px) !important;
  .menuHeader_aa {
    display: none !important;
  }
}
.ant-pro-global-header-right-content {
  height: 80px !important;
}
.ant-pro-top-nav-header-main {
  padding: 0 !important;
}
.ant-pro-layout .ant-pro-sider.ant-pro-sider-fixed-mix {
  height: 100% !important;
  inset-block-start: 0 !important;
  z-index: 1000;
}
// -----prolayout 菜单样式覆盖 end------

textarea::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

// .ant-pro-layout-content {
//   padding: 0 !important;
// }

// .colorWeak {
//   filter: invert(80%);
// }

// .ant-layout {
//   min-height: 100vh;
// }
// .ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
//   left: unset;
// }

// canvas {
//   display: block;
// }

// body {
//   text-rendering: optimizeLegibility;
//   -webkit-font-smoothing: antialiased;
//   -moz-osx-font-smoothing: grayscale;
// }

// ul,
// ol {
//   list-style: none;
// }

// body .ant-pro-field-radio-error span {
//   color: black !important;
// }

// @media (max-width: 768px) {
//   .ant-table {
//     width: 100%;
//     overflow-x: auto;
//     &-thead > tr,
//     &-tbody > tr {
//       > th,
//       > td {
//         white-space: pre;
//         > span {
//           display: block;
//         }
//       }
//     }
//   }
// }

// .ant-table-wrapper {
//   .ant-table-thead {
//     > tr > th {
//       background-color: #f0f2f5 !important;
//       color: rgba(0, 0, 0, 0.85);
//       font-weight: 500;
//     }
//   }
// }

// // 调整消息提示框位置
// .ant-message {
//   top: 100px !important;

//   .ant-message-notice-content {
//     padding: 12px 16px;
//   }
// }
