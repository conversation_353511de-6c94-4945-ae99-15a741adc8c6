﻿import { AUTHORIZATUION_TOKEN_NAME } from '@/constants';
import { logout } from '@/utils/auth';
import type { RequestOptions } from '@@/plugin-request/request';
import { type RequestConfig } from '@umijs/max';
import { message } from 'antd';
import Cookies from 'js-cookie';

// 与后端约定的响应数据格式
interface ResponseStructure {
  success?: boolean;
  data?: any;
  code?: number;
  message?: string;
}

// http200时触发顺序 requestInterceptors -> responseInterceptors -> errorThrower -> errorHandler
// http非200时触发顺序 requestInterceptors -> errorHandler
export const errorConfig: RequestConfig = {
  errorConfig: {
    // 错误抛出
    // errorThrower 是利用 responseInterceptors 实现的，它的触发条件是: 当 data.success === false 时。
    errorThrower: (res) => {
      const { success, data, code, message } =
        res as unknown as ResponseStructure;
      if (!success) {
        const error: any = new Error(message);
        error.name = 'BizError';
        error.info = { code, message, data };
        error.message = message;
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.name === 'BizError') {
        const errorInfo: ResponseStructure | undefined = error.info;
        if (errorInfo?.code === 1001) {
          logout({
            beforeLogout: () => message.error('系统未授权, 即将返回登录'),
          });
          return;
        }
        if (errorInfo) {
          const { code, message: errorMessage } = errorInfo;
          message.error(`${errorMessage || '未知错误'}`);
        }
      } else if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        if (error.response.status == 401) {
          logout({
            beforeLogout: () => message.error('登录失效, 请重新登录'),
          });
          return;
        }
        const { data } = error.response;
        message.error(`网络错误: ${data?.message || error.response.status}`);
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('网络没有响应, 请稍候再试');
      } else {
        // 发送请求时出了点问题
        message.error('网络错误, 请稍候再试');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      // 拦截请求配置，进行个性化处理。
      return {
        ...config,
        headers: {
          ...config.headers,
          Authorization: Cookies.get(AUTHORIZATUION_TOKEN_NAME),
        },
      };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // 拦截响应数据，进行个性化处理
      const { data } = response as unknown as ResponseStructure;

      return {
        ...response,
        data: { ...data, success: data?.success === true },
      };
    },
  ],
};
