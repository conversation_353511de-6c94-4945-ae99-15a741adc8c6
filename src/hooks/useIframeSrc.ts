import { useMemo } from 'react';

import { IFRAME_SRC_MAP_DEMO, SYSTEMS } from '@/constants';
import { useLocation, useModel } from '@umijs/max';

export const useIframeSrc = (staticSrc?: string) => {
  const { initialState } = useModel('@@initialState');
  const { pathname } = useLocation();
  const iframSrc = useMemo(() => {
    const src =
      staticSrc ||
      IFRAME_SRC_MAP_DEMO.filter(
        (item) =>
          item.brand_name === initialState?.currentBrandInfo?.brandNameCn,
      )?.find((item) => item.portal_path === pathname)?.datart_share_href;

    if (src) {
      const url = new URL(src);

      url.searchParams.set('brandId', `${initialState?.currentBrandInfo?.id}`);
      url.searchParams.set('sysId', SYSTEMS.全域驾驶舱.toString());
      url.searchParams.set('userId', `${initialState?.userInfo?.id}`);
      url.searchParams.set(
        'themeColor',
        `${initialState?.currentBrandInfo?.theme}`,
      );
      return url.href;
    } else {
      return '';
    }
  }, [
    staticSrc,
    initialState?.currentBrandInfo?.brandNameCn,
    pathname,
    initialState?.userInfo?.id,
    initialState?.currentBrandInfo?.id,
    initialState?.currentBrandInfo?.theme,
  ]);

  return iframSrc;
};
