import type { ProLayoutProps } from '@ant-design/pro-components';

const colorPrimary = '#0095FF';
const colorSelect = '#FFFFFF';

export const themeSettings: (themeColor: string) => ProLayoutProps = (
  themeColor,
) => ({
  colorPrimary,
  title: '全域驾驶舱',
  token: {
    bgLayout: '#DBE7EE',
    header: {
      colorBgHeader: '#fff',
      colorTextMenuSelected: themeColor || colorPrimary,
      heightLayoutHeader: 80,
    },
    sider: {
      // 菜单背景色
      colorMenuBackground: themeColor || colorPrimary,
      // 选中背景颜色
      colorBgMenuItemSelected: colorSelect,
      // hover背景颜色
      colorBgMenuItemHover: colorSelect,
      // 文字颜色
      colorTextMenuTitle: colorSelect,
      colorTextMenu: colorSelect,
      colorTextMenuSelected: themeColor || colorPrimary,
      colorTextMenuItemHover: themeColor || colorPrimary,
      // 菜单项高度
      menuHeight: 56,
    },
  },
});
