[{"mods": [{"description": "大屏相关查询操作", "interfaces": [{"consumes": ["application/json"], "description": "查询MTD、YTD的GMV数据", "name": "postGmvAccumulated", "method": "post", "path": "/platform/gmv/accumulated", "response": {"typeArgs": [{"typeArgs": [], "typeName": "PlatformPeriodAccumulatedGmvVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "查询下钻地图GMV数据", "name": "postGmvArea", "method": "post", "path": "/platform/gmv/area", "response": {"typeArgs": [{"typeArgs": [], "typeName": "PlatformMapFormatVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenAreaParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "查询各平台GMV表现", "name": "postGmvPlatforms", "method": "post", "path": "/platform/gmv/platforms", "response": {"typeArgs": [{"typeArgs": [], "typeName": "PlatformGmvDataVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "GMV趋势-查询选择日期的前七天GMV数据", "name": "postGmvRecentSevenDays", "method": "post", "path": "/platform/gmv/recent-seven-days", "response": {"typeArgs": [{"typeArgs": [{"typeArgs": [], "typeName": "PlatformSevenDaysGmvTrendVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "轮播图1 查询GMV维度：客户组：NKA、RKA、LKA、Non-KA2 大区：南西区、北区、东区3 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈", "name": "postGmvThreeDimension", "method": "post", "path": "/platform/gmv/three-dimension", "response": {"typeArgs": [{"typeArgs": [], "typeName": "PlatformGmvDimensionVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "获取重点客户上翻率NKA、RKA、LKA、Non-KA", "name": "postKeyCustomerOnlineRate", "method": "post", "path": "/platform/key/customer/online-rate", "response": {"typeArgs": [{"typeArgs": [{"typeArgs": [], "typeName": "PlatformKeyCustomerOnlineRateVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "获取重点子品牌上翻率奥利奥、乐之、趣多多、太平、闲趣、炫迈", "name": "postKeySubBrandOnlineRate", "method": "post", "path": "/platform/key/sub-brand/online-rate", "response": {"typeArgs": [{"typeArgs": [{"typeArgs": [], "typeName": "PlatformKeySubBrandOnlineRateVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "获取区域必分销品达成率区域：南西区、北区、东区", "name": "postRegionAchievementRate", "method": "post", "path": "/platform/region/achievement-rate", "response": {"typeArgs": [{"typeArgs": [{"typeArgs": [], "typeName": "PlatformRegionDistributionAchievementRateVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}, {"consumes": ["application/json"], "description": "获取大区商品上翻率理论上：上翻率是指将线下数据放到平台上：京东、美团、饿了么等", "name": "postRegionOnlineRate", "method": "post", "path": "/platform/region/online-rate", "response": {"typeArgs": [{"typeArgs": [{"typeArgs": [], "typeName": "PlatformRegionProduceOnlineRateVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "ResponseData", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "parameters": [{"in": "body", "description": "bigScreenParam", "name": "bigScreenParam", "required": true, "dataType": {"typeArgs": [], "typeName": "BigScreenParam", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}}]}], "name": "bigScreen"}], "name": "omnipilot", "baseClasses": [{"name": "BigScreenAreaParam", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaCode", "description": "区域code", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaName", "description": "区域名字 查询全国为空", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaType", "description": "区域等级 0-全国（查询全部省的数据）areaName则为null 1-查询单个省下全部的市 areaName则为省名字2-查询单个市下全部的区县 areaName则为市的字", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "brand", "description": "品牌", "required": true}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "platform", "description": "平台", "required": true}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "queryDate", "description": "请求日期", "required": true}], "templateArgs": []}, {"name": "BigScreenParam", "properties": [{"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "brand", "description": "品牌", "required": true}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "platform", "description": "平台", "required": true}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "queryDate", "description": "请求日期", "required": true}], "templateArgs": []}, {"name": "IconFormat", "properties": [{"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "iconFormatName", "description": "名称", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "iconFormatRatio", "description": "占比", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "iconFormatType", "description": "类型", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "iconFormatValue", "description": "数值", "required": false}], "templateArgs": []}, {"name": "MapFormat", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaCode", "description": "区域编码国标6位", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaDistributionRate", "description": "铺货率", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaDistributionVolume", "description": "铺货量", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaGmv", "description": "区域Gmv", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaName", "description": "区域名称-国标", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaType", "description": "区域等级 0-全国 1-省、2-市", "required": false}], "templateArgs": []}, {"name": "PlatformGmvDataVO", "properties": [{"dataType": {"typeArgs": [{"typeArgs": [], "typeName": "IconFormat", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "gmvRatio", "description": "GMV占比，不区分平台", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "gmvValue", "description": "GMV数据，不区分平台", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "year<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "同比增长率，不区分平台", "required": false}], "templateArgs": []}, {"name": "PlatformGmvDimensionVO", "properties": [{"dataType": {"typeArgs": [{"typeArgs": [], "typeName": "IconFormat", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "areaGmv", "description": "大区：南西区、北区、东区、其他", "required": false}, {"dataType": {"typeArgs": [{"typeArgs": [], "typeName": "IconFormat", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "customerGroupGmv", "description": "客户组：NKA、RKA、LKA、Non-KA", "required": false}, {"dataType": {"typeArgs": [{"typeArgs": [], "typeName": "IconFormat", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "mainSubBrands", "description": "重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈", "required": false}], "templateArgs": []}, {"name": "PlatformKeyCustomerOnlineRateVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keyCustomerCount", "description": "重点客户Gmv", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keyCustomerName", "description": "重点客户名称：NKA、RKA、LKA、Non-KA", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keyCustomerOnlineRate", "description": "重点客户上翻成率", "required": false}], "templateArgs": []}, {"name": "PlatformKeySubBrandOnlineRateVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keySubBrandCount", "description": "重点子品牌Gmv", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keySubBrandName", "description": "重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "keySubBrandOnlineRate", "description": "重点子品牌翻成率", "required": false}], "templateArgs": []}, {"name": "PlatformMapFormatVO", "properties": [{"dataType": {"typeArgs": [{"typeArgs": [], "typeName": "MapFormat", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}], "typeName": "Array", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "mapFormatList", "description": "平台区域地图数据", "required": false}], "templateArgs": []}, {"name": "PlatformPeriodAccumulatedGmvVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "mtdAchievementRater", "description": "MTD-达成率 ", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "mtdGmv", "description": "MTD-GMV之和", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "ytdAchievementRater", "description": "YTD-达成率 ", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "ytdGmv", "description": "MTD-GMV之和", "required": false}], "templateArgs": []}, {"name": "PlatformRegionDistributionAchievementRateVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionDistributionAchievementRate", "description": "必分销品达成率", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionDistributionCount", "description": "必分销品数量", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionName", "description": "区域名称：南西区、北区、东区三个区域", "required": false}], "templateArgs": []}, {"name": "PlatformRegionProduceOnlineRateVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionName", "description": "区域名称：南西区、北区、东区三个区域", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionProduceOnlineCount", "description": "区域铺货数", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "regionProduceOnlineRate", "description": "区域上翻率", "required": false}], "templateArgs": []}, {"name": "PlatformSevenDaysGmvTrendVO", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "gmv", "description": "gmvDate对应的gmv数值", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "gmvDate", "description": "时间", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "gmvYearOnYear", "description": "GMV的同比率", "required": false}], "templateArgs": []}, {"name": "ResponseData", "properties": [{"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "code", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "currentTime", "required": false}, {"dataType": {"typeArgs": [], "typeName": "PlatformGmvDataVO", "isDefsType": true, "templateIndex": 0, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "data", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "message", "required": false}, {"dataType": {"typeArgs": [], "typeName": "boolean", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "success", "required": false}], "templateArgs": [{"typeArgs": [], "typeName": "PlatformGmvDataVO", "isDefsType": true, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}]}, {"name": "UserDto", "properties": [{"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "brandId", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "createBy", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "createTime", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "deleted", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "departmentId", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "email", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "id", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "name", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "password", "required": false}, {"dataType": {"typeArgs": [], "typeName": "number", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "state", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "updateBy", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "updateTime", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "userId", "required": false}, {"dataType": {"typeArgs": [], "typeName": "string", "isDefsType": false, "templateIndex": -1, "compileTemplateKeyword": "#/definitions/", "enum": [], "typeProperties": []}, "name": "userName", "required": false}], "templateArgs": []}]}]