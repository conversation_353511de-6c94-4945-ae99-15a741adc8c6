/**
 * @desc 获取重点客户上翻率NKA、RKA、LKA、Non-KA
 */

import { request } from '@umijs/max';

export function postKeyCustomerOnlineRate(params: any = {}, options?: any) {
  const { method = 'post', ...rest } = options ?? {};

  const url = '/omnipilot/api/platform/key/customer/online-rate'.replace(
    /{([^}]+)}/g,
    (s, s1) => params[s1],
  );

  return request(url, {
    ...rest,
    method,
    data: params,
  });
}
