/**
 * @desc GMV趋势-查询选择日期的前七天GMV数据
 */

import { request } from '@umijs/max';

export function postGmvRecentSevenDays(params: any = {}, options?: any) {
  const { method = 'post', ...rest } = options ?? {};

  const url = '/omnipilot/api/platform/gmv/recent-seven-days'.replace(
    /{([^}]+)}/g,
    (s, s1) => params[s1],
  );

  return request(url, {
    ...rest,
    method,
    data: params,
  });
}
