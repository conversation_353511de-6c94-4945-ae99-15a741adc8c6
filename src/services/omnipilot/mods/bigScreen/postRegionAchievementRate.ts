/**
 * @desc 获取区域必分销品达成率区域：南西区、北区、东区
 */

import { request } from '@umijs/max';

export function postRegionAchievementRate(params: any = {}, options?: any) {
  const { method = 'post', ...rest } = options ?? {};

  const url = '/omnipilot/api/platform/region/achievement-rate'.replace(
    /{([^}]+)}/g,
    (s, s1) => params[s1],
  );

  return request(url, {
    ...rest,
    method,
    data: params,
  });
}
