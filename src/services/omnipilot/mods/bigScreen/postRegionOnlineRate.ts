/**
 * @desc 获取大区商品上翻率理论上：上翻率是指将线下数据放到平台上：京东、美团、饿了么等
 */

import { request } from '@umijs/max';

export function postRegionOnlineRate(params: any = {}, options?: any) {
  const { method = 'post', ...rest } = options ?? {};

  const url = '/omnipilot/api/platform/region/online-rate'.replace(
    /{([^}]+)}/g,
    (s, s1) => params[s1],
  );

  return request(url, {
    ...rest,
    method,
    data: params,
  });
}
