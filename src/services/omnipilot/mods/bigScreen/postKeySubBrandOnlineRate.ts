/**
 * @desc 获取重点子品牌上翻率奥利奥、乐之、趣多多、太平、闲趣、炫迈
 */

import { request } from '@umijs/max';

export function postKeySubBrandOnlineRate(params: any = {}, options?: any) {
  const { method = 'post', ...rest } = options ?? {};

  const url = '/omnipilot/api/platform/key/sub-brand/online-rate'.replace(
    /{([^}]+)}/g,
    (s, s1) => params[s1],
  );

  return request(url, {
    ...rest,
    method,
    data: params,
  });
}
