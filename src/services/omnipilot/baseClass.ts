class BigScreenAreaParam {
  /** 区域code */
  areaCode = undefined;

  /** 区域名字 查询全国为空 */
  areaName = '';

  /** 区域等级 0-全国（查询全部省的数据）areaName则为null 1-查询单个省下全部的市 areaName则为省名字2-查询单个市下全部的区县 areaName则为市的字 */
  areaType = undefined;

  /** 品牌 */
  brand = '';

  /** 平台 */
  platform = '';

  /** 请求日期 */
  queryDate = '';
}

class BigScreenParam {
  /** 品牌 */
  brand = '';

  /** 平台 */
  platform = '';

  /** 请求日期 */
  queryDate = '';
}

class IconFormat {
  /** 名称 */
  iconFormatName = '';

  /** 占比 */
  iconFormatRatio = undefined;

  /** 类型 */
  iconFormatType = '';

  /** 数值 */
  iconFormatValue = undefined;
}

class MapFormat {
  /** 区域编码国标6位 */
  areaCode = undefined;

  /** 铺货率 */
  areaDistributionRate = undefined;

  /** 铺货量 */
  areaDistributionVolume = undefined;

  /** 区域Gmv */
  areaGmv = undefined;

  /** 区域名称-国标 */
  areaName = '';

  /** 区域等级 0-全国 1-省、2-市 */
  areaType = undefined;
}

class PlatformGmvDataVO {
  /** GMV占比，不区分平台 */
  gmvRatio = [];

  /** GMV数据，不区分平台 */
  gmvValue = undefined;

  /** 同比增长率，不区分平台 */
  yearOnYearGrowth = undefined;
}

class PlatformGmvDimensionVO {
  /** 大区：南西区、北区、东区、其他 */
  areaGmv = [];

  /** 客户组：NKA、RKA、LKA、Non-KA */
  customerGroupGmv = [];

  /** 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈 */
  mainSubBrands = [];
}

class PlatformKeyCustomerOnlineRateVO {
  /** 重点客户Gmv */
  keyCustomerCount = undefined;

  /** 重点客户名称：NKA、RKA、LKA、Non-KA */
  keyCustomerName = '';

  /** 重点客户上翻成率 */
  keyCustomerOnlineRate = undefined;
}

class PlatformKeySubBrandOnlineRateVO {
  /** 重点子品牌Gmv */
  keySubBrandCount = undefined;

  /** 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈 */
  keySubBrandName = '';

  /** 重点子品牌翻成率 */
  keySubBrandOnlineRate = undefined;
}

class PlatformMapFormatVO {
  /** 平台区域地图数据 */
  mapFormatList = [];
}

class PlatformPeriodAccumulatedGmvVO {
  /** MTD-达成率  */
  mtdAchievementRater = undefined;

  /** MTD-GMV之和 */
  mtdGmv = undefined;

  /** YTD-达成率  */
  ytdAchievementRater = undefined;

  /** MTD-GMV之和 */
  ytdGmv = undefined;
}

class PlatformRegionDistributionAchievementRateVO {
  /** 必分销品达成率 */
  regionDistributionAchievementRate = undefined;

  /** 必分销品数量 */
  regionDistributionCount = undefined;

  /** 区域名称：南西区、北区、东区三个区域 */
  regionName = '';
}

class PlatformRegionProduceOnlineRateVO {
  /** 区域名称：南西区、北区、东区三个区域 */
  regionName = '';

  /** 区域铺货数 */
  regionProduceOnlineCount = undefined;

  /** 区域上翻率 */
  regionProduceOnlineRate = undefined;
}

class PlatformSevenDaysGmvTrendVO {
  /** gmvDate对应的gmv数值 */
  gmv = undefined;

  /** 时间 */
  gmvDate = '';

  /** GMV的同比率 */
  gmvYearOnYear = undefined;
}

class ResponseData {
  /** code */
  code = undefined;

  /** currentTime */
  currentTime = '';

  /** data */
  data = new PlatformGmvDataVO();

  /** message */
  message = '';

  /** success */
  success = false;
}

class UserDto {
  /** brandId */
  brandId = '';

  /** createBy */
  createBy = '';

  /** createTime */
  createTime = '';

  /** deleted */
  deleted = undefined;

  /** departmentId */
  departmentId = undefined;

  /** email */
  email = '';

  /** id */
  id = undefined;

  /** name */
  name = '';

  /** password */
  password = '';

  /** state */
  state = undefined;

  /** updateBy */
  updateBy = '';

  /** updateTime */
  updateTime = '';

  /** userId */
  userId = '';

  /** userName */
  userName = '';
}

export const omnipilot = {
  BigScreenAreaParam,
  BigScreenParam,
  IconFormat,
  MapFormat,
  PlatformGmvDataVO,
  PlatformGmvDimensionVO,
  PlatformKeyCustomerOnlineRateVO,
  PlatformKeySubBrandOnlineRateVO,
  PlatformMapFormatVO,
  PlatformPeriodAccumulatedGmvVO,
  PlatformRegionDistributionAchievementRateVO,
  PlatformRegionProduceOnlineRateVO,
  PlatformSevenDaysGmvTrendVO,
  ResponseData,
  UserDto,
};
