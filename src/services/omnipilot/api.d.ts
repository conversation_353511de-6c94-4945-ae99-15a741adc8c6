import type { RequestOptions } from '@umijs/max';

type RequestOptions = RequestOptions;

export namespace api {
  /**
   * 大屏相关查询操作
   */
  export namespace bigScreen {
    /**
     * 查询MTD、YTD的GMV数据
     * /platform/gmv/accumulated
     */
    export function postGmvAccumulated(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<defs.PlatformPeriodAccumulatedGmvVO>>;

    /**
     * 查询下钻地图GMV数据
     * /platform/gmv/area
     */
    export function postGmvArea(
      params: defs.BigScreenAreaParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<defs.PlatformMapFormatVO>>;

    /**
     * 查询各平台GMV表现
     * /platform/gmv/platforms
     */
    export function postGmvPlatforms(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<defs.PlatformGmvDataVO>>;

    /**
     * GMV趋势-查询选择日期的前七天GMV数据
     * /platform/gmv/recent-seven-days
     */
    export function postGmvRecentSevenDays(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<Array<defs.PlatformSevenDaysGmvTrendVO>>>;

    /**
     * 轮播图1 查询GMV维度：客户组：NKA、RKA、LKA、Non-KA2 大区：南西区、北区、东区3 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈
     * /platform/gmv/three-dimension
     */
    export function postGmvThreeDimension(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<defs.PlatformGmvDimensionVO>>;

    /**
     * 获取重点客户上翻率NKA、RKA、LKA、Non-KA
     * /platform/key/customer/online-rate
     */
    export function postKeyCustomerOnlineRate(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<Array<defs.PlatformKeyCustomerOnlineRateVO>>>;

    /**
     * 获取重点子品牌上翻率奥利奥、乐之、趣多多、太平、闲趣、炫迈
     * /platform/key/sub-brand/online-rate
     */
    export function postKeySubBrandOnlineRate(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<defs.ResponseData<Array<defs.PlatformKeySubBrandOnlineRateVO>>>;

    /**
     * 获取区域必分销品达成率区域：南西区、北区、东区
     * /platform/region/achievement-rate
     */
    export function postRegionAchievementRate(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<
      defs.ResponseData<Array<defs.PlatformRegionDistributionAchievementRateVO>>
    >;

    /**
     * 获取大区商品上翻率理论上：上翻率是指将线下数据放到平台上：京东、美团、饿了么等
     * /platform/region/online-rate
     */
    export function postRegionOnlineRate(
      params: defs.BigScreenParam,
      options?: RequestOptions,
    ): Promise<
      defs.ResponseData<Array<defs.PlatformRegionProduceOnlineRateVO>>
    >;
  }
}

export namespace defs {
  export class BigScreenAreaParam {
    /** 区域code */
    areaCode?: number;

    /** 区域名字 查询全国为空 */
    areaName?: string;

    /** 区域等级 0-全国（查询全部省的数据）areaName则为null 1-查询单个省下全部的市 areaName则为省名字2-查询单个市下全部的区县 areaName则为市的字 */
    areaType?: number;

    /** 品牌 */
    brand: string;

    /** 平台 */
    platform: string;

    /** 请求日期 */
    queryDate: string;
  }

  export class BigScreenParam {
    /** 品牌 */
    brand: string;

    /** 平台 */
    platform: string;

    /** 请求日期 */
    queryDate: string;
  }

  export class IconFormat {
    /** 名称 */
    iconFormatName?: string;

    /** 占比 */
    iconFormatRatio?: number;

    /** 类型 */
    iconFormatType?: string;

    /** 数值 */
    iconFormatValue?: number;
  }

  export class MapFormat {
    /** 区域编码国标6位 */
    areaCode?: number;

    /** 铺货率 */
    areaDistributionRate?: number;

    /** 铺货量 */
    areaDistributionVolume?: number;

    /** 区域Gmv */
    areaGmv?: number;

    /** 区域名称-国标 */
    areaName?: string;

    /** 区域等级 0-全国 1-省、2-市 */
    areaType?: number;
  }

  export class PlatformGmvDataVO {
    /** GMV占比，不区分平台 */
    gmvRatio?: Array<defs.IconFormat>;

    /** GMV数据，不区分平台 */
    gmvValue?: number;

    /** 同比增长率，不区分平台 */
    yearOnYearGrowth?: number;
  }

  export class PlatformGmvDimensionVO {
    /** 大区：南西区、北区、东区、其他 */
    areaGmv?: Array<defs.IconFormat>;

    /** 客户组：NKA、RKA、LKA、Non-KA */
    customerGroupGmv?: Array<defs.IconFormat>;

    /** 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈 */
    mainSubBrands?: Array<defs.IconFormat>;
  }

  export class PlatformKeyCustomerOnlineRateVO {
    /** 重点客户Gmv */
    keyCustomerCount?: number;

    /** 重点客户名称：NKA、RKA、LKA、Non-KA */
    keyCustomerName?: string;

    /** 重点客户上翻成率 */
    keyCustomerOnlineRate?: number;
  }

  export class PlatformKeySubBrandOnlineRateVO {
    /** 重点子品牌Gmv */
    keySubBrandCount?: number;

    /** 重点子品牌：奥利奥、乐之、趣多多、太平、闲趣、炫迈 */
    keySubBrandName?: string;

    /** 重点子品牌翻成率 */
    keySubBrandOnlineRate?: number;
  }

  export class PlatformMapFormatVO {
    /** 平台区域地图数据 */
    mapFormatList?: Array<defs.MapFormat>;
  }

  export class PlatformPeriodAccumulatedGmvVO {
    /** MTD-达成率  */
    mtdAchievementRater?: number;

    /** MTD-GMV之和 */
    mtdGmv?: number;

    /** YTD-达成率  */
    ytdAchievementRater?: number;

    /** MTD-GMV之和 */
    ytdGmv?: number;
  }

  export class PlatformRegionDistributionAchievementRateVO {
    /** 必分销品达成率 */
    regionDistributionAchievementRate?: number;

    /** 必分销品数量 */
    regionDistributionCount?: number;

    /** 区域名称：南西区、北区、东区三个区域 */
    regionName?: string;
  }

  export class PlatformRegionProduceOnlineRateVO {
    /** 区域名称：南西区、北区、东区三个区域 */
    regionName?: string;

    /** 区域铺货数 */
    regionProduceOnlineCount?: number;

    /** 区域上翻率 */
    regionProduceOnlineRate?: number;
  }

  export class PlatformSevenDaysGmvTrendVO {
    /** gmvDate对应的gmv数值 */
    gmv?: number;

    /** 时间 */
    gmvDate?: string;

    /** GMV的同比率 */
    gmvYearOnYear?: number;
  }

  export class ResponseData<T0 = any> {
    /** code */
    code?: number;

    /** currentTime */
    currentTime?: string;

    /** data */
    data?: T0;

    /** message */
    message?: string;

    /** success */
    success?: boolean;
  }

  export class UserDto {
    /** brandId */
    brandId?: string;

    /** createBy */
    createBy?: string;

    /** createTime */
    createTime?: string;

    /** deleted */
    deleted?: number;

    /** departmentId */
    departmentId?: number;

    /** email */
    email?: string;

    /** id */
    id?: number;

    /** name */
    name?: string;

    /** password */
    password?: string;

    /** state */
    state?: number;

    /** updateBy */
    updateBy?: string;

    /** updateTime */
    updateTime?: string;

    /** userId */
    userId?: string;

    /** userName */
    userName?: string;
  }
}
