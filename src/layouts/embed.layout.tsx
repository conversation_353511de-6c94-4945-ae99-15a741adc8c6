import { useEffect } from 'react';

import { findFirstValidRoute } from '@/utils';
import { Outlet, useLocation } from '@umijs/max';
import { history, useModel } from '@umijs/max';

import routes from '../../config/routes';
import './embed.layout.less';

const EmbedLayout = () => {
  const location = useLocation();
  const { access } = useModel('@@initialState', (model) => ({
    access: model.initialState?.access,
  }));

  // 嵌入模式下，没有权限的路由，跳转到第一个有权限的路由
  useEffect(() => {
    if (access && location.pathname?.split('/').length === 2) {
      const currentRoute =
        (routes as any[]).find((item) => item.path === location.pathname)
          ?.routes || [];
      const firstValidRoute = findFirstValidRoute(currentRoute, access);
      if (firstValidRoute) {
        history.replace(firstValidRoute.path);
      }
    }
  }, [access, location.pathname]);

  // page layout变成embed layout时候，清空历史记录
  useEffect(() => {
    console.log('embed layout清空历史记录');
    try {
      (window as any).historyIframes = [];
      const container = document.getElementById('embed_layout_AaBbCc');
      if (container) {
        const iframes = container.getElementsByClassName('embed_iframe');
        while (iframes.length > 0) {
          iframes[0].remove();
        }
      }
    } catch (error) {
      console.log(
        '%c [ error ]-38',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error,
      );
    }
  }, []);

  return (
    <div className="embed_layout_AaBbCc" id="embed_layout_AaBbCc">
      <Outlet />
    </div>
  );
};

export default EmbedLayout;
