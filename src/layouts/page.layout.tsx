import { useEffect } from 'react';

import { findFirstValidRoute } from '@/utils';
import { GridContent, PageContainer } from '@ant-design/pro-components';
import { Outlet, history, useLocation, useModel } from '@umijs/max';

import routes from '../../config/routes';

const PageLayout = () => {
  const { key, pathname } = useLocation();
  const { access } = useModel('@@initialState', (model) => ({
    access: model.initialState?.access,
  }));

  useEffect(() => {
    if (access && pathname?.split('/').length === 2) {
      const currentRoute =
        (routes as any[]).find((item) => item.path === pathname)?.routes || [];
      const firstValidRoute = findFirstValidRoute(currentRoute, access);
      if (firstValidRoute) {
        history.replace(firstValidRoute.path);
      }
    }
  }, [access, pathname]);

  useEffect(() => {
    console.log('page layout清空历史记录');
    try {
      (window as any).historyIframes = [];
      const container = document.getElementById('embed_layout_AaBbCc');
      if (container) {
        const iframes = container.getElementsByClassName('embed_iframe');
        while (iframes.length > 0) {
          iframes[0].remove();
        }
      }
    } catch (error) {
      console.log(
        '%c [ error ]-38',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error,
      );
    }
  }, []);

  return (
    <PageContainer
      title={false}
      style={{
        minHeight: 'calc(100vh - 130px)',
      }}
    >
      <GridContent>
        <Outlet key={key} />
      </GridContent>
    </PageContainer>
  );
};

export default PageLayout;
