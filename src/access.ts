import { includes } from 'lodash';

/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: {
  userInfo?: Record<string, any>;
  access?: any[];
}) {
  const { userInfo, access } = initialState ?? {};

  return {
    pageAccess: (route: any) => {
      return access?.some((res: any) => res.resPath?.includes(route.path));
      // return true;
    },
  };
}
