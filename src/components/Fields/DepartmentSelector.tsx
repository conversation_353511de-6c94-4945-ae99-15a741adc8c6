import { useEffect, useState } from 'react';

import api from '@/services';
import { useRequest } from '@umijs/max';
import type { SelectProps } from 'antd';
import { Select } from 'antd';

type DepartmentSelectorProps = SelectProps & { brandId?: string };

const DepartmentSelector = ({ brandId, ...rest }: DepartmentSelectorProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  // const { run } = useRequest(api.aimofang.department.getGetByBrand, {
  //   manual: true,
  // });
  // useEffect(() => {
  //   if (brandId) {
  //     run({
  //       brandId,
  //     }).then((res) => {
  //       if (res) {
  //         setOptions(
  //           res.map((item) => ({
  //             label: item.departName,
  //             value: item.departId,
  //           })),
  //         );
  //       }
  //     });
  //   }
  // }, [brandId]);
  return <Select options={options} placeholder="请选择" {...rest} />;
};

export default DepartmentSelector;
