import { useEffect, useState } from 'react';

import api from '@/services';
import { useRequest } from '@umijs/max';
import type { SelectProps } from 'antd';
import { Select } from 'antd';

type RoleSeletorProps = SelectProps & { brandId?: string; departId?: string };

const RoleSeletor = ({ brandId, departId, ...rest }: RoleSeletorProps) => {
  const [options, setOptions] = useState<SelectProps['options']>([]);
  // const { run: getByBrandId } = useRequest(api.aimofang.role.getGetByBrandId, {
  //   manual: true,
  // });
  // const { run: getByDepId } = useRequest(api.aimofang.role.getGetByDepId, {
  //   manual: true,
  // });

  // useEffect(() => {
  //   const request = departId ? getByDepId : getByBrandId;
  //   if (brandId || departId) {
  //     request({
  //       brandId,
  //       departId,
  //     }).then((res) => {
  //       if (res) {
  //         setOptions(
  //           res.map((item) => ({
  //             label: item.name,
  //             value: item.roleId,
  //           })),
  //         );
  //       }
  //     });
  //   }
  // }, [brandId, departId]);
  return <Select options={options} placeholder="请选择" {...rest} />;
};

export default RoleSeletor;
