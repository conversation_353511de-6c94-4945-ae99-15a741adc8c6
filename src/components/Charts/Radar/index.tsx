import React, { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';

import type { AtheisticChanelTypes, LegendComponent } from '@antv/g2';
import { Chart } from '@antv/g2';
// import { Radar } from '@ant-design/plots';
import { tooltip } from '@antv/g2/lib/interaction/tooltip';
import type { Closeable } from '@antv/g2/lib/spec/utils';
import { Renderer } from '@antv/g-svg';
import numeral from 'numeral';

type RadarProps = {
  data: any[];
  // data: any[];
  id?: string;
  tooltipItem?: any[];
  x: string;
  y: string;
  colorFiled: string;
  legend?: Closeable<
    Partial<Record<AtheisticChanelTypes, Closeable<LegendComponent>>>
  >;
  isFullscreen: boolean;
  chartSize: any;
};

export default function Radar(props: RadarProps) {
  const { data, tooltipItem, isFullscreen, chartSize, x, y, colorFiled } =
    props;

  const divRef = useRef<HTMLElement>();
  const chartRef = useRef<Chart>();

  useEffect(() => {
    chartRef.current = new Chart({ container: divRef.current, autoFit: true });

    chartRef.current.options({
      type: 'view',
      autoFit: true,
      data: data,
      scale: {
        x: { padding: 0.5, align: 0 },
        y: { tickCount: 7 },
        color: { range: ['#5E5FC2', '#0370B1'] },
      },

      legend: {
        color: {
          position: 'right',
          layout: {
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
          },
          itemLabelFill: '#ffffff',
          itemMarker: 'rect',
          itemLabelFontSize: chartSize,
        },
      },

      coordinate: { type: 'polar' },
      axis: {
        x: {
          grid: true,
          gridLineWidth: 1,
          tick: false,
          gridLineDash: [0, 0],
          lineStroke: '#ffffff',
          tickStroke: '#ffffff',
          labelFill: '#ffffff',
          labelFontSize: chartSize,
        },
        y: {
          zIndex: 1,
          title: false,
          label: false,
          gridConnect: 'line',
          gridLineWidth: 1,
          labelFontSize: chartSize,
          gridLineDash: [0, 0],
          lineStroke: '#ffffff',
          gridStroke: '#ffffff',

          tickStroke: '#ffffff',
          labelFill: '#ffffff',
          gridAreaFill: (dataum, index, data) => {
            return index % 2 === 1 ? 'rgba(185, 152, 255, 0.50)' : '';
          },
        },
      },

      interaction: {
        tooltip: {
          crosshairsLineDash: [4, 4],
        },
      },
      children: [
        {
          type: 'area',
          encode: { x: x, y: y, color: colorFiled },
          style: { fillOpacity: 0.5 },
          tooltip: null,
        },
        {
          type: 'line',
          encode: { x: x, y: y, color: colorFiled },
          style: { lineWidth: 2 },
          tooltip: [
            {
              field: 'iconFormatValue',
              valueFormatter: (v) => numeral(v).format('0,0.00'),
            },
          ],
        },
        {
          type: 'point',
          encode: {
            x: x,
            y: y,
            color: colorFiled,
            shape: 'point',
            size: 3,
          },
          tooltip: null,
        },
      ],
    });
    chartRef.current.render();

    return () => {
      chartRef.current?.destroy();
      chartRef.current = undefined;
    };
  }, [chartSize, data]);

  // useEffect(() => {
  //   if (data) {
  //     chartRef.current?.changeData(data);
  //   }
  // }, [data]);

  useEffect(() => {
    chartRef.current?.render();
  }, [isFullscreen, chartSize, data]);

  return <div className="h-full w-full" ref={divRef as any} />;
}
