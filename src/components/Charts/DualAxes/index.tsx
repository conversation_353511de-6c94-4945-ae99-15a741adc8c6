import React, { useEffect, useMemo, useRef } from 'react';
import ReactDOM from 'react-dom';

import { DualAxes } from '@ant-design/plots';
import { tooltip } from '@antv/g2/lib/interaction/tooltip';
import { Renderer } from '@antv/g-svg';
import numeral from 'numeral';

type DualAxesProps = {
  data?: any[];
  xField: string;
  intervalYField: string;
  lineYField: string;
  legendPosition?: 'top' | 'bottom' | 'left' | 'right';
  alignItems?: 'flex-start' | 'flex-end' | 'center';
  justifyContent?: 'flex-start' | 'flex-end' | 'center';
  yLabelFormatter?: (v: number) => string;
  id?: string;
  chartSize?: number;
  isFullscreen?: boolean;
};

const DemoDualAxes = (props: DualAxesProps) => {
  const ref = useRef(null);

  const {
    data,
    xField,
    intervalYField,
    lineYField,
    yLabelFormatter,
    legendPosition = 'top',
    alignItems = 'flex-start',
    justifyContent = 'flex-start',
    id,
    chartSize,
    isFullscreen,
  } = props;
  const config = useMemo(() => {
    const dualAxes: DualAxesProps = {
      xField: xField,
      data: data,
      animate: false,
      renderer: new Renderer(),
      autoFit: true,
      legend: {
        position: legendPosition,
        color: {
          layout: {
            justifyContent: justifyContent,
            alignItems: alignItems,
            flexDirection: 'column',
          },
          itemMarker: 'rect',
          itemMarkerRadius: 5,
          itemMarkerFill: (datum, index, data) => {
            if (datum.id === intervalYField) {
              return '#5E0AFF';
            }
            if (datum.id === lineYField) {
              return '#85E5FF';
            }
          },
          itemLabelFill: '#A279AC',
          itemLabelFontSize: 10,
        },
      },

      children: [
        {
          type: 'interval',
          yField: intervalYField,
          style: {
            fill: () => {
              return 'l(90) 0:#6F00FF 1:#081A3D';
            },
            radiusTopLeft: 5,
            radiusTopRight: 5,
            maxWidth: 34,
          },
          axis: {
            x: {
              labelFill: '#FFFFFF',
              tickStroke: '#FFFFFF',
              labelFillOpacity: 0.5,
              tickStrokeOpacity: 0.5,
              labelFontSize: chartSize,
            },
            y: {
              labelFill: '#FFFFFF',
              labelFormatter: yLabelFormatter,
              labelFillOpacity: 0.5,
              labelFontSize: chartSize,
              gridStroke: '#FFFFFF',
            },
          },
          tooltip: [
            {
              field: intervalYField,
              valueFormatter: (v) => numeral(v).format('0,0.00'),
              color: '#5E0AFF',
            },
          ],
        },
        {
          type: 'line',
          yField: lineYField,
          shapeField: 'smooth',
          scale: { color: { relations: [[lineYField, '#85E5FF']] } },
          axis: {
            y: {
              position: 'right',
              labelFill: '#FFFFFF',
              labelFillOpacity: 0.5,
              labelFormatter: (v: number) => numeral(v).format('0.%'),
              labelFontSize: chartSize,
            },
          },

          style: { lineWidth: 2 },
          tooltip: [
            {
              field: lineYField,
              valueFormatter: (v) => numeral(v).format('0.00%'),
              color: '#70BAF6',
            },
          ],
        },
      ],
    };
    return dualAxes;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    data,
    isFullscreen,
    chartSize,
    intervalYField,
    lineYField,
    xField,
    yLabelFormatter,
  ]);

  return <DualAxes id={id} {...config} ref={ref} />;
};
export default DemoDualAxes;
