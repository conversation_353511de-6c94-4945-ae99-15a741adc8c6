import React, { useMemo } from 'react';

import type { PieConfig } from '@ant-design/plots';
import { Pie } from '@ant-design/plots';
import numeral from 'numeral';

type PieProps = {
  data: any[];
  angleField: string;
  colorField: string;
  height?: number;
  isFullscreen?: boolean;
  chartSize?: number;
};
const DemoPie = (props: PieProps) => {
  const { data, angleField, colorField, height, isFullscreen, chartSize } =
    props;

  const ref = React.useRef(null);
  const config = useMemo(() => {
    const pieConfig: PieConfig = {
      data: data,
      angleField: angleField,
      colorField: colorField,
      innerRadius: 0.6,
      label: {
        text: (d: any) => {
          const percent = d[colorField];
          const value = numeral(d.iconFormatRatio).format('0.00%');
          return `${percent}-${value}`;
        },
        style: {
          fill: '#8AFFFFFF',
          fontSize: chartSize,
          fontWeight: 600,
          fillOpacity: 0.6,
          connectorStroke: '#8AFFFFFF',
        },
        position: 'spider',
      },
      legend: {
        color: {
          title: false,
          position: 'right',
          rowPadding: 5,
          layout: {
            justifyContent: 'flex-end',
            alignItems: 'center',
            flexDirection: 'column',
          },
          itemLabelFill: '#A279AC',
          itemLabelFontSize: chartSize,
        },
      },
      tooltip: false,
      scale: {
        color: {
          palette: [
            '#5E5FC2',
            '#528D92',
            '#60C09B',
            '#00A1B1',
            '#A279AC',
            '#007ABE',
          ],
        },
      },
      animate: false,
      // annotations: [
      //   {
      //     type: 'text',
      //     style: {
      //       text: 'AntV\nCharts',
      //       x: '50%',
      //       y: '50%',
      //       textAlign: 'center',
      //       fontSize: 40,
      //       fontStyle: 'bold',
      //     },
      //   },
      // ],
    };
    return pieConfig;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [angleField, chartSize, colorField, data, isFullscreen]);

  return (
    <div className="pie-container">
      <Pie paddingTop={10} ref={ref} paddingBottom={15} {...config} />
    </div>
  );
};
export default DemoPie;
