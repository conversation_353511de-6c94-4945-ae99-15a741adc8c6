import { useMemo, useState } from 'react';

import China from '@/assets/geojson/100000.json';
import api from '@/services';
import type { LayerPopupProps } from '@antv/larkmap';
import {
  ChoroplethLayer,
  CustomControl,
  LarkMap,
  LayerPopup,
  LegendCategories,
  LegendRamp,
  LineLayer,
  PolygonLayer,
  TextLayer,
  ZoomControl,
} from '@antv/larkmap';
import { useRequest } from '@umijs/max';
import { Button } from 'antd';

// import { interpolateBlues, schemePaired } from 'd3-scale-chromatic';
import {
  MapBaseConfigCategories,
  MapBaseConfigHeatmap,
  linearColors,
} from './config';

type CategoriesMapProps = {
  popUpFields?: LayerPopupProps['items'][number]['fields'];
  onPolygonLayerClick?: (data: any) => void;
} & (
  | {
      type: 'categories';
      data?: {
        /**
         * @name 和地图名字做匹配用的
         * @type 枚举地图区分类型用的
         */
        name: string;
        type: string;
        [props: string]: any;
      }[];
    }
  | {
      type: 'heatmap';
      colorField?: string;
      data?: {
        /**
         * @name 和地图名字做匹配用的
         * @value 做热力用的
         */
        name?: string;
        value?: number;
        [props: string]: any;
      }[];
    }
);
const PurpleColorPalette = [
  '#523D95',
  '#4C3C8D',
  '#483886',
  '#44357D',
  '#10115E',
  '#38316E',
  '#302D64',
  '#2C2A5F',
  '#282757',
  '#28224F',
  '#10115E',
  '#021459',

  // '#C1B5D3',
  // '#B4A7C5',
  // '#A998B7',
  // '#9D8AAA',
  // '#907D9D',
  // '#847193',
  // '#786587',
  // '#6E5A80',
  // '#644D76',
  // '#58436D',
  // '#4B3A65',
  // '#42335D',
];

const Map = (props: CategoriesMapProps) => {
  const [geoJson, setGeoJson] = useState(China);

  const dataPalette = useMemo(() => {
    if (!props.data || props.type === 'heatmap') {
      return;
    }
    const types = Object.keys(Object.groupBy(props.data, (item) => item.type));
    return types.reduce((prev, current, idx) => {
      const index = idx % PurpleColorPalette.length;
      return { ...prev, [current]: PurpleColorPalette[index] };
    }, {});
  }, [props.data, props.type]);

  return (
    <LarkMap {...MapBaseConfigCategories.LarkMap} style={{ height: '100%' }}>
      <ChoroplethLayer
        id="ChoroplethLayer"
        autoFit
        source={{
          data: geoJson,
          parser: {
            type: 'geojson',
          },
          transforms: [
            {
              type: 'join',
              sourceField: 'areaName',
              targetField: 'fullname',
              data: props.data,
            },
          ],
        }}
        lineWidth={0.5}
        strokeColor="rgba(255, 255, 255, 0.3)"
        state={{
          active: {
            fillColor: '#ffffff',
            strokeColor: '#ffffff',
          },
        }}
        fillColor={
          props.type === 'categories'
            ? {
                field: 'type',
                value: (v) => {
                  if (!v.type) {
                    return '#ddd';
                  }
                  return dataPalette[v.type];
                },
                scale: {
                  type: 'cat',
                },
              }
            : {
                field: props.colorField || 'value',
                value: MapBaseConfigHeatmap,
                scale: {
                  type: 'linear',
                },
              }
        }
        onClick={async (e) => {
          if (e.feature.properties.level === 'city') {
            return;
          }
          console.log(
            '%c [ e ]-49',
            'font-size:13px; background:pink; color:#bf2c9f;',
            e,
          );
          props.onPolygonLayerClick?.(e);
          if (e.feature.properties.level === 'district') {
            return;
          }
          const res = await import(
            `@/assets/geojson/${e.feature.properties.code}.json`
          );
          const geo = res.default;
          setGeoJson(geo);
        }}
      />
      <TextLayer
        {...MapBaseConfigCategories.TextLayer}
        source={{
          data: geoJson.features
            .map((item) => {
              return {
                name: item.properties?.fullname,
                x: item.properties?.centroid?.[0],
                y: item.properties?.centroid?.[1],
              };
            })
            .filter((item) => item.name && item.x && item.y),
          parser: {
            type: 'json',
            x: 'x',
            y: 'y',
          },
        }}
      />
      <ZoomControl position="bottomleft" />
      <CustomControl position="topleft">
        <Button
          type="link"
          // type="default"

          size="small"
          onClick={() => {
            setGeoJson(China);
            props.onPolygonLayerClick?.({
              feature: { properties: { areaName: '全国', level: 'china' } },
            });
          }}
        >
          返回全国
        </Button>
      </CustomControl>
      <CustomControl position="bottomright">
        <div
          className="h-96px w-24px"
          style={{
            background: `linear-gradient( ${MapBaseConfigHeatmap[MapBaseConfigHeatmap.length - 1]} 0%,${MapBaseConfigHeatmap[0]} 100%)`,
          }}
        />
      </CustomControl>
      <LayerPopup
        trigger="hover"
        items={[
          {
            layer: 'ChoroplethLayer',
            fields: props.popUpFields?.map((item: any) => ({
              ...item,
              formatValue: item.formatValue
                ? item.formatValue
                : (value: any) => value || '-',
            })),
            title: (feature) => {
              return feature.fullname;
            },
          },
        ]}
      />
      {props.type === 'categories' && (
        <CustomControl position="bottomright">
          <LegendCategories
            labels={Object.keys(dataPalette || {})}
            colors={schemePaired as string[]}
          />
        </CustomControl>
      )}

      {/* {props.type === 'heatmap' && (
        <CustomControl position="bottomright">
          <LegendRamp
            style={{
              width: 100,
            }}
            isContinuous
            labels={
              props.data?.map((item) => item[props.colorField || 'value']) || []
            }
            colors={linearColors}
          />
        </CustomControl>
      )} */}
    </LarkMap>
  );
};

export default Map;
