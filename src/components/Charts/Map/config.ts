import type { LarkMapProps, TextLayerProps } from '@antv/larkmap';

export const MapBaseConfigCategories = {
  LarkMap: {
    mapType: 'Map',
    mapOptions: {
      center: [104.299012, 37.876151],
      zoom: 2.33,
      scrollZoom: false,
    },
    logoVisible: false,
  } as LarkMapProps,
  TextLayer: {
    field: 'name',
    style: {
      fill: '#333333',
      fontSize: 10,
    },
  } as TextLayerProps,
};

export const linearColors = [
  'rgb(247, 251, 255)',
  'rgb(236, 244, 252)',
  'rgb(226, 238, 248)',
  'rgb(216, 231, 245)',
  'rgb(205, 224, 241)',
  'rgb(192, 217, 237)',
  'rgb(176, 210, 232)',
  'rgb(159, 201, 226)',
  'rgb(139, 191, 221)',
  'rgb(119, 180, 216)',
  'rgb(99, 168, 210)',
  'rgb(82, 156, 204)',
  'rgb(65, 144, 197)',
  'rgb(51, 130, 190)',
];

export const MapBaseConfigHeatmap = [
  '#9386FB',
  '#7868FF',
  '#5E54CA',
  '#473F98',
  '#322B66',
  '#171532',
  // 88
  // '#A380FD',
  // '#A06EFB',
  // '#5E38BA',
  // '#2C0296',
  // '#180C90',
  // '#030061',
  // 77
  // '#2CDEC7',
  // '#0090C6',
  // '#00B8C9',
  // '#003B95',
  // '#0066B7',
  // '#280062',
  // 99
  // '#AEA4FF',
  // '#9386FF',
  // '#8071FE',
  // '#5E54CA',
  // '#473F98',
  // '#322B66',
  // 11
  // '#523D95',
  // '#4C3C8D',
  // '#483886',
  // '#44357D',
  // '#10115E',
  // '#38316E',
  // '#302D64',
  // '#2C2A5F',
  // '#282757',
  // '#28224F',
  // '#10115E',
  // '#021459',
  // 33
  // '#C1B5D3',
  // '#B4A7C5',
  // '#A998B7',
  // '#9D8AAA',
  // '#907D9D',
  // '#847193',
  // '#786587',
  // '#6E5A80',
  // '#644D76',
  // '#58436D',
  // '#4B3A65',
  // '#42335D',
  // 00
  // '#E0BBE4',
  // '#D291BC',
  // '#957DAD',
  // '#8E44AD',
  // '#7F3E98',
  // '#6F2D91',
  // '#5A189A',
  // '#4A148C',
  // '#3A0D69',
  // '#290661',
  // 11
  '#766DD4',
  '#5E54CA',
  '#554CB9',
  '#453E8D',
  '#393173',
  '#201C58',
];
