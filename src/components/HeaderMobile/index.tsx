import { BRAND_INFO_STORAGE_KEY } from '@/constants';
import { trace } from '@/utils/trace';
import { CheckOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Dropdown, Popover, Space } from 'antd';

const HeaderMobile = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const items = initialState?.brandList?.map((item) => {
    return {
      key: item.id,
      label: (
        <div
          onClick={() => {
            const access =
              initialState.brandAccessMap?.find((s) => s.brandId === item.id)
                ?.access || [];
            setInitialState((prev) => ({
              ...prev,
              currentBrandInfo: item,
              access: access,
            }));
            trace.registerPage({
              $$_brand_id: item.id,
              $$_brand_name: item.brandNameCn,
            });
            trace.track('visit');
            localStorage.setItem(BRAND_INFO_STORAGE_KEY, JSON.stringify(item));
            // const threeLevelRegex = /^\/[^\/]+\/[^\/]+\/[^\/]+\/?$/;
            // const twoLevelRegex = /^\/[^\/]+\/[^\/]+\/?$/;
            // let defaultPath = access?.find((item) =>
            //   twoLevelRegex.test(item.resPath),
            // )?.resPath;
            // if (!defaultPath) {
            //   defaultPath = access?.find((item) =>
            //     threeLevelRegex.test(item.resPath),
            //   )?.resPath;
            // }
            setTimeout(() => {
              history.replace({
                pathname: '/welcome',
                search: `t=${Date.now()}`,
              });
            });
          }}
          className="flex items-center justify-start"
        >
          <Space>
            <img
              width={24}
              height={24}
              src={
                item.logoUrl ||
                require('@/assets/images/default_brand_logo.png')
              }
            />
            <span>{item.brandNameCn}</span>
            {initialState.currentBrandInfo?.id === item.id && <CheckOutlined />}
          </Space>
        </div>
      ),
    };
  });
  return (
    <div className="border-[rgba(130, 145, 169, 0.53)] flex h-5 items-center rounded-none border-r-[1px] border-solid">
      {(initialState?.brandList?.length ?? 0) > 1 && (
        <Dropdown
          menu={{
            items,
            style: {
              maxHeight: '50vh',
              overflow: 'auto',
            },
          }}
        >
          <a
            onClick={(e) => e.preventDefault()}
            style={{
              color: initialState?.currentBrandInfo?.theme,
            }}
          >
            <Space>{initialState?.currentBrandInfo?.brandNameCn}</Space>
          </a>
        </Dropdown>
      )}

      {/* <Popover
        placement="bottomLeft"
        content={
          <Space>
            <div className="flex flex-col items-center">
              <img
                width={145}
                height={145}
                alt=""
                src={require('@/assets/images/heyixiaotong/heyixiaotong.png')}
              />
              <span>合易销通</span>
            </div>
            <div className="flex flex-col items-center">
              <img
                width={150}
                height={150}
                alt=""
                src={require('@/assets/images/qrcode.jpg')}
              />
              <span>公众号</span>
            </div>
          </Space>
        }
      >
        <div className="cursor-pointer px-[12px] text-[#96A9C7]">移动版</div>
      </Popover> */}
    </div>
  );
};

export default HeaderMobile;
