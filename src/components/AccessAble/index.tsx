import type { FC, PropsWithChildren } from 'react';
import { useMemo } from 'react';

import { Access, useModel } from '@umijs/max';

type AccessAbleProps = {
  path?: string;
  code?: string;
};

const AccessAble: FC<PropsWithChildren<AccessAbleProps>> = ({
  children,
  path,
}) => {
  const { initialState } = useModel('@@initialState');
  const accessible = useMemo(() => {
    return !!initialState?.access?.some((res: any) =>
      res.resPath?.includes(path),
    );
  }, [path, initialState]);
  return <Access accessible={accessible}>{children}</Access>;
};

export default AccessAble;
