import { useEffect } from 'react';

import { useIframeSrc } from '@/hooks';

(window as any).historyIframes = [] as {
  src: string;
  dom: HTMLElement;
}[];

export const MemoIframe = (props: { src: string }) => {
  useEffect(() => {
    (window as any).historyIframes.forEach((item: any) => {
      item.dom.style.display = 'none';
    });
    if (
      (window as any).historyIframes.find((item: any) => item.src === props.src)
    ) {
      const dom = (window as any).historyIframes.find(
        (item: any) => item.src === props.src,
      )?.dom;
      if (dom) {
        dom.style.display = 'block';
      }
      return;
    }

    setTimeout(() => {
      const iframe = document.createElement('iframe');
      iframe.src = props.src;
      iframe.id = props.src;
      iframe.className = 'embed_iframe';
      iframe.style.display = 'block';
      document.getElementById('embed_layout_AaBbCc')?.appendChild(iframe);
      (window as any).historyIframes.push({
        src: props.src,
        dom: iframe,
      });
    }, 16.6);
  }, [props.src]);
  // return createPortal(
  //   <iframe src={props.src} id={props.src} />,
  //   // <div>{props.src}</div>,
  //   document.getElementById('embed_layout_AaBbCc') as HTMLElement,
  // );
  return null;
};

const EmbedIframe = () => {
  const src = useIframeSrc();

  return <MemoIframe src={src} />;
};

export default EmbedIframe;
