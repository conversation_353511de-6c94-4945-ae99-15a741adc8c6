import { AvatarDropdown, Footer, HeaderMobile } from '@/components';
import {
  AUTHORIZATUION_TOKEN_NAME,
  BRAND_INFO_STORAGE_KEY,
  LOGIN_PAGE_PATH,
  SYSTEMS,
  USER_ID_STORAGE_KEY,
} from '@/constants';
import { themeSettings } from '@/project.config';
import { trace } from '@/utils/trace';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig, RuntimeConfig } from '@umijs/max';
import { history, request } from '@umijs/max';
import 'animate.css';
import { ConfigProvider } from 'antd';
import Cookies from 'js-cookie';
import { throttle } from 'lodash';

import { logout } from './utils/auth';

export { request } from '@/configs/request';

const isDev = process.env.NODE_ENV === 'development';

const TRACE_VALID_LIST = [
  'omnipilot.mpsmedia.com.cn',
  // 'dev.mpsmedia.com.cn',
  // '**************',
];

if (TRACE_VALID_LIST.includes(window.location.hostname)) {
  trace.init({
    server_url: `${window.location.origin}/auth/api/v1/open/tracking/save`,
  });
}

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  collapsed?: boolean;
  userInfo?: {
    id?: number;
    username?: string;
    type?: string;
  };
  brandList?: any[];
  access?: {
    id: number;
    resPath: string;
    resCode: string;
    resName: string;
    resType: string;
    sysId: number;
  }[];
  brandAccessMap?: any[];
  currentBrandInfo?: Partial<{
    id: number;
    brandNameCn: string;
    theme: string;
  }>;
}> {
  trace.registerPage({
    $$_biz_name: '全域驾驶舱',
  });
  const { location } = history;
  const authToken = Cookies.get(AUTHORIZATUION_TOKEN_NAME);
  if (location.pathname.includes(LOGIN_PAGE_PATH)) {
    return {};
  }
  if (!authToken || !localStorage.getItem(USER_ID_STORAGE_KEY)) {
    logout();
    return {};
  }

  const { data: userInfo } = await request(
    `/auth/api/v1/open/account/role/${localStorage.getItem(USER_ID_STORAGE_KEY)}/${SYSTEMS.全域驾驶舱}/details`,
    {
      baseURL: '',
    },
  );
  if (!userInfo?.accountInfo) {
    logout();
    return {};
  }
  const initialBrand = userInfo?.brandList?.[0];
  const mergeItems = (data) => {
    const brandMap = new Map();

    data.forEach((item) => {
      if (!brandMap.has(item.brandId)) {
        brandMap.set(item.brandId, {
          ...item,
          permissionList: { rolePermissionResourcesDTOS: [] },
        });
      }

      const existingItem = brandMap.get(item.brandId);
      existingItem.permissionList.rolePermissionResourcesDTOS = [
        ...existingItem.permissionList.rolePermissionResourcesDTOS,
        ...(item.permissionList.rolePermissionResourcesDTOS || []),
      ];
    });

    const mergedData = Array.from(brandMap.values());

    mergedData.forEach((item) => {
      const uniquePermissions = [];
      const permissionMap = new Map();

      item.permissionList.rolePermissionResourcesDTOS.forEach((permission) => {
        if (!permissionMap.has(permission.id)) {
          permissionMap.set(permission.id, permission);
          uniquePermissions.push(permission);
        }
      });

      item.permissionList.rolePermissionResourcesDTOS = uniquePermissions;
    });

    return mergedData;
  };
  const mererBrandRoleList = mergeItems(userInfo?.brandRoleList);

  const brandAccessMap = mererBrandRoleList.map((item) => {
    return {
      ...item,
      access: item.permissionList?.rolePermissionResourcesDTOS || [],
    };
  });

  const initialAccess =
    brandAccessMap?.find((item) => item.brandId === initialBrand.id)?.access ||
    [];

  trace.setProfile({
    $$_user_id: userInfo?.accountInfo.id,
    $$_username: userInfo?.accountInfo.username,
    $$_account_type: userInfo?.accountInfo?.type,
    $$_email: userInfo?.accountInfo?.email,
    $$_phone: userInfo?.accountInfo?.phone,
  });
  trace.registerPage({
    $$_brand_id: initialBrand.id,
    $$_brand_name: initialBrand.brandNameCn,
  });
  trace.track('visit');
  localStorage.setItem(USER_ID_STORAGE_KEY, userInfo?.accountInfo?.id);
  localStorage.setItem(BRAND_INFO_STORAGE_KEY, JSON.stringify(initialBrand));
  return {
    collapsed: window.innerWidth < 992,
    userInfo: {
      id: userInfo?.accountInfo.id,
      username: userInfo?.accountInfo.username,
    },
    brandAccessMap: brandAccessMap,
    access: initialAccess,
    brandList: userInfo?.brandList,
    currentBrandInfo: initialBrand,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState,
}) => {
  return {
    siderWidth: 240,
    navTheme: 'light',
    layout: 'mix',
    contentWidth: 'Fluid',
    fixedHeader: true,
    fixSiderbar: true,
    siderMenuType: 'sub',
    splitMenus: true,
    waterMarkProps: {
      content: initialState?.userInfo?.username,
    },
    logo: (
      <img
        src={require('@/assets/images/logo.png')}
        style={{
          width: '50px',
          height: '50px',
          maxWidth: 'none',
          marginRight: initialState?.collapsed ? 0 : 22,
        }}
        alt=""
      />
    ),
    onCollapse: (collapsed) => {
      setInitialState((prev) => ({ ...prev, collapsed }));
    },
    actionsRender: () => [
      <HeaderMobile key="HeaderMobile" />,
      <AvatarDropdown key="AvatarDropdown" />,
    ],
    headerRender(props, defaultDom) {
      return (
        <div
          style={{
            paddingLeft: initialState?.collapsed ? 64 : 240,
          }}
        >
          {defaultDom}
        </div>
      );
    },
    headerTitleRender: false,
    menuHeaderRender: (logo, title, props) => {
      return (
        <div
          className="menuHeader_aa"
          style={{
            width: initialState?.collapsed ? 64 : 240,
            transition: 'all 0.2s',
            display: 'flex',
            alignItems: 'center',
            height: '100%',
          }}
        >
          <img
            src={require('@/assets/images/logo.png')}
            style={{
              width: '50px',
              height: '50px',
              marginRight: initialState?.collapsed ? 0 : 22,
              marginLeft: initialState?.collapsed ? 10 : 0,
            }}
            alt=""
          />
          {initialState?.collapsed === false && (
            <div
              style={{
                fontSize: 16,
                color: '#fff',
                fontWeight: 600,
                whiteSpace: 'nowrap',
              }}
            >
              {props?.title}
            </div>
          )}
        </div>
      );
    },
    childrenRender: (children) => {
      return (
        <ConfigProvider
          theme={{
            cssVar: true,
            token: {
              colorPrimary: initialState?.currentBrandInfo?.theme || '#0095FF',
            },
          }}
        >
          {children}
          <Footer />
        </ConfigProvider>
      );
    },
    ...(themeSettings(
      initialState?.currentBrandInfo?.theme,
    ) as Partial<LayoutSettings>),
  };
};

export const onRouteChange: RuntimeConfig['onRouteChange'] = throttle(
  (props) => {
    // const route = matchRoutes(
    //   props.clientRoutes,
    //   props.location.pathname,
    // )?.pop();
    // console.log(
    //   '%c [ route ]-274',
    //   'font-size:13px; background:pink; color:#bf2c9f;',
    //   route?.pathnameBase,
    // );

    // 为啥放throttle里：route redirect时候会触发两次
    trace.track('pageview');
  },
  // 为啥是一秒？：防止title还没设置成正确的
  1000,
  { trailing: true, leading: false },
);
