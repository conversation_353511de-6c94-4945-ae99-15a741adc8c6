import React from 'react';

import { SmileOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Config<PERSON><PERSON>ider, Result } from 'antd';

const Empty: React.FC = () => (
  // <Result
  //   className="bg-white "
  //   icon={<SmileOutlined />}
  //   title="敬请期待！"
  //   // extra={<Button type="primary">Next</Button>}
  // />
  <div
    // style={{
    //   height: 'calc(100vh - 80px -64px)',
    // }}
    className="flex items-center justify-center pt-20"
  >
    <img
      alt=""
      width={480}
      height={367}
      src={require('@/assets/images/<EMAIL>')}
    />
  </div>
);

export default Empty;
