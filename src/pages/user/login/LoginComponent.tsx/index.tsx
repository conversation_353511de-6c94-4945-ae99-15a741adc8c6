import React, { useEffect, useState } from 'react';

import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { Helmet, request, useRequest } from '@umijs/max';
import { Alert, Image } from 'antd';
import { createStyles } from 'antd-style';

type LoginProps = {
  onSubmit?: (values: any) => Promise<void>;
  bgImageSrc?: string;
};

// const useStyles = createStyles(({ token }) => {
//   return {
//     action: {
//       marginLeft: '8px',
//       color: 'rgba(0, 0, 0, 0.2)',
//       fontSize: '24px',
//       verticalAlign: 'middle',
//       cursor: 'pointer',
//       transition: 'color 0.3s',
//       '&:hover': {
//         color: token.colorPrimaryActive,
//       },
//     },
//     lang: {
//       width: 42,
//       height: 42,
//       lineHeight: '42px',
//       position: 'fixed',
//       right: 16,
//       borderRadius: token.borderRadius,
//       ':hover': {
//         backgroundColor: token.colorBgTextHover,
//       },
//     },
//     container: {
//       display: 'flex',
//       flexDirection: 'row-reverse',
//       alignItems: 'center',
//       height: '100vh',
//       overflow: 'auto',
//       backgroundImage: `url(${bgImageSrc})`,
//       backgroundSize: '100% 100%',
//     },
//   };
// });

const LoginComponent: React.FC<LoginProps> = (props) => {
  const { onSubmit, bgImageSrc } = props;

  const [errorMessage, setErrorMessage] = useState('');
  const { run, loading, data } = useRequest(() =>
    request('/auth/api/v1/open/generate', {
      method: 'get',
    }),
  );

  // const { styles } = useStyles();

  const LoginMessage: React.FC<{
    content: string;
  }> = ({ content }) => {
    return (
      <Alert
        style={{
          marginBottom: 24,
          marginTop: 24,
        }}
        message={content}
        type="error"
        showIcon
      />
    );
  };
  return (
    <div
      className="flex w-full items-center justify-end"
      style={{
        height: '100vh',
        overflow: 'auto',
        backgroundImage: `url(${bgImageSrc})`,
        backgroundSize: 'cover',
        backgroundPosition: '0 0',
      }}
    >
      <div className="mr-28 h-[35%] min-h-[450px] w-[30%] min-w-[400px] max-w-[450px] bg-white">
        <Helmet>
          <title>{'登录'}</title>
        </Helmet>
        <div className="mt-10">
          <LoginForm
            title={'账号登录'}
            subTitle={null}
            onFinish={async (values) => {
              setErrorMessage('');
              try {
                await onSubmit?.({
                  ...values,
                  cacheKey: data?.cacheKey,
                });
              } catch (error: any) {
                run();
                setErrorMessage(error.message || '未知错误');
              }
            }}
            // actions={
            //   <div
            //     style={{
            //       marginBlockEnd: 24,
            //     }}
            //   >
            //     <a
            //       style={{
            //         float: 'left',
            //       }}
            //     >
            //       忘记密码 ?
            //     </a>
            //   </div>
            // }
          >
            {errorMessage && <LoginMessage content={errorMessage} />}
            <div className="mt-6">
              <ProFormText
                name="username"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined />,
                }}
                placeholder={'请输入邮箱或手机号'}
                rules={[
                  {
                    required: true,
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined />,
                }}
                placeholder={'请输入密码'}
                rules={[
                  {
                    required: true,
                  },
                  {
                    min: 6,
                    message: '密码长度不能小于6位',
                  },
                ]}
              />
              <div className="flex">
                <ProFormText
                  style={{
                    flex: 1,
                  }}
                  name="code"
                  fieldProps={{
                    size: 'large',
                    maxLength: 4,
                    // prefix: <LockOutlined />,
                  }}
                  placeholder={'请输入验证码'}
                  rules={[
                    {
                      required: true,
                    },
                    {
                      min: 4,
                      message: '验证码不能小于4位',
                    },
                  ]}
                />
                <Image
                  src={data?.base64Image}
                  placeholder={loading}
                  alt=""
                  width={100}
                  height={40}
                  preview={false}
                  onClick={run}
                  style={{
                    marginLeft: 8,
                  }}
                />
              </div>
            </div>
          </LoginForm>
        </div>
      </div>
      {/* <Footer /> */}
    </div>
  );
};
export default LoginComponent;
