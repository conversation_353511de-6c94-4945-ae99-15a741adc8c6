import React from 'react';

import bgLoginImg from '@/assets/images/bg-login.png';
import {
  AUTHORIZATUION_TOKEN_NAME,
  DOMAIN,
  USER_ID_STORAGE_KEY,
  USER_INFO_STORAGE_KEY,
} from '@/constants';
import { request, useModel, useRequest } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import Cookies from 'js-cookie';

import LoginComponent from './LoginComponent.tsx';

const Login: React.FC = () => {
  const { run: login, error } = useRequest(
    (data, options) =>
      request('/auth/api/v1/open/account/login', {
        data,
        method: 'post',
        // baseURL: '',
        // ...options,
      }),
    {
      manual: true,
      throwOnError: true,
    },
  );
  const { setInitialState } = useModel('@@initialState');

  const handleSubmit = async (values: any) => {
    const data = await login(
      {
        ...values,
        username: values.username,
        passwd: values.password,
        // passwd: '123456',
        // username: '<EMAIL>',
        sysCode: 'QYJSC',
      },
      {
        skipErrorHandler: true,
      },
    );
    setInitialState({
      userInfo: data,
    });
    // message.success('登录成功');

    Cookies.set(AUTHORIZATUION_TOKEN_NAME, data.token, {
      expires: dayjs().add(10, 'hours').toDate(),
      domain: DOMAIN,
    });
    localStorage.setItem(USER_INFO_STORAGE_KEY, JSON.stringify(data));
    localStorage.setItem(USER_ID_STORAGE_KEY, data.id);
    message.success('登录成功');

    // const urlParams = new URL(window.location.href).searchParams;
    // window.location.replace(urlParams.get('redirect') || '/');
    window.location.replace('/welcome');
  };
  return (
    <>
      <LoginComponent bgImageSrc={bgLoginImg} onSubmit={handleSubmit} />
    </>
  );
};
export default Login;
