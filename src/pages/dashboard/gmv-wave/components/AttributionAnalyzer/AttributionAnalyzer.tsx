import { useEffect, useRef, useState } from 'react';
import Xarrow, { Xwrapper, useXarrow } from 'react-xarrows';

import consumptionPotential from '@/assets/images/consumptionPotential.jpg';
import supplyHhealth from '@/assets/images/supplyHealth.jpg';
import { useSize } from 'ahooks';
import classnames from 'classnames';

import { initialEdges, newInitialEdges } from '../../data/edges';
import { Card } from '../Card';
import Legend from '../Legend/Legend';
import { useDataStore } from '../store';

export default function LayoutContent() {
  const data = useDataStore((state: any) => state.data);
  const GMVnode = data.find((item: any) => item.id === '销售额');

  data.forEach((item: any) => {
    if (
      item.id === '新客数' ||
      item.id === '老客数' ||
      item.id === '十大人群人数' ||
      item.id === '购买频次' ||
      item.id === '单次购买金额' ||
      item.id === '16大场景人数' ||
      item.id === '16大场景频次' ||
      item.id === '16大场景单次购买金额' ||
      item.id === '十大人群人数' ||
      item.id === '店铺动销率' ||
      item.id === '店铺铺货率' ||
      item.id === '建议铺货店铺数' ||
      item.id === '在售SKU数' ||
      item.id === '日均动销SKU产出' ||
      item.id === '日均动销SKU数' ||
      item.id === '售罄SKU数'
    ) {
      item.data.dataFlowType = 'input';
      item.data.observationMetrics = '---';
      item.data.actionValue = '---';
    } else if (
      item.id === '订单数' ||
      item.id === '场景数量' ||
      item.id === '交易用户数' ||
      item.id === '购买频次' ||
      item.id === '老客数' ||
      item.id === '新客频次' ||
      item.id === '历史用户数' ||
      item.id === '动销店铺数' ||
      item.id === '店铺产出' ||
      item.id === '铺货店铺数' ||
      item.id === '建议铺货店铺数' ||
      item.id === '售罄SKU数' ||
      item.id === '动销SKU数' ||
      item.id === '日均动销SKU产出' ||
      item.id === '日均动销SKU数' ||
      item.id === '售罄SKU预估损失' ||
      item.id === '在售SKU平均产出' ||
      item.id === '在售SKU数' ||
      item.id === '16大场景订单数' ||
      item.id === '16大场景销售额'
    ) {
      item.data.observationMetrics = '---';
      item.data.dataFlowType = 'process';
      item.data.actionValue = '---';
    }
  });

  data.forEach((item: any) => {
    if (
      item.id === '老客频次' ||
      item.id === '老客复购率' ||
      item.id === '店铺动销率'
    ) {
      item.data.alarm = false;
    }
  });

  const [flag, setFlag] = useState(false);

  useEffect(() => {
    setFlag(true);
  }, []);

  return (
    <div className="flex justify-center bg-white py-10">
      <div className="3xl:px-10 3xl:gap-y-5 flex w-[1200px] flex-col px-5">
        <div className="3xl:gap-x-10 flex justify-between">
          {/* GMV */}
          <div className="w-1/3 px-4 py-5">
            {GMVnode && (
              <Card
                alarm={GMVnode.data.alarm}
                className={GMVnode.id}
                key={GMVnode.id}
                id={GMVnode.id}
                headerProps={GMVnode.data.headerProps}
                contentProps={GMVnode.data.contentProps}
                footerProps={GMVnode.data.footerProps}
                dataFlowType="output"
                observationMetrics={'--'}
              />
            )}
          </div>
          {/* 图例 */}
          <Legend />
        </div>

        <div className="3xl:gap-y-10 flex flex-col gap-y-5">
          {/* 需求  */}
          <div className="3xl:gap-y-10 flex flex-col gap-y-5">
            <div className="3xl:gap-x-10 flex gap-x-5 2xl:gap-x-6">
              {[
                ['订单数', '单次购买金额', '16大场景销售额'],
                [
                  '交易用户数',
                  '购买频次',
                  '16大场景订单数',
                  '16大场景单次购买金额',
                ],
                [
                  '新客数',
                  '老客数',
                  '十大人群人数',
                  '16大场景人数',
                  '16大场景频次',
                ],
              ].map((item, i) => {
                return (
                  <div
                    className="h-fulls flex w-1/3 flex-col gap-y-6 border-[1px] border-dashed border-[#0147EB] bg-[rgba(24,113,255,0.04)] px-4 py-5"
                    key={i}
                  >
                    {item.map((item) => {
                      const node = data.find((node: any) => node.id === item);
                      if (!node) return null;
                      let cardClassName = node.id;
                      if (node.id === '订单数') {
                        cardClassName = classnames(node.id, 'mt-[100px]');
                      } else if (node.id === '16大场景订单数') {
                        cardClassName = classnames(node.id, 'mt-[400px]');
                      } else if (node.id === '16大场景销售额') {
                        cardClassName = classnames(node.id, 'mt-[400px]');
                      }
                      return (
                        // <Card
                        //   observationMetrics={node.data.observationMetrics}
                        //   actionValue={node.data.actionValue}
                        //   alarm={node.data.alarm}
                        //   className={cardClassName}
                        //   id={node.id}
                        //   key={node.id}
                        //   headerProps={node.data.headerProps}
                        //   contentProps={node.data.contentProps}
                        //   footerProps={node.data.footerProps}
                        //   dataFlowType={node.data.dataFlowType}
                        // />
                        <Card
                          observationMetrics={node.data.observationMetrics}
                          actionValue={node.data.actionValue}
                          alarm={node.data.alarm}
                          className={cardClassName}
                          id={node.id}
                          key={node.id}
                          headerProps={node.data.headerProps}
                          contentProps={node.data.contentProps}
                          footerProps={node.data.footerProps}
                          dataFlowType={node.data.dataFlowType}
                        />
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 供给 */}
          <div className="3xl:gap-y-10 flex flex-col gap-y-5">
            <div className="3xl:gap-x-10 flex gap-x-5 2xl:gap-x-6">
              {[
                ['动销店铺数', '店铺产出'],
                [
                  '店铺动销率',
                  '铺货店铺数',
                  '在售SKU数',
                  '在售SKU平均产出',
                  '售罄SKU预估损失',
                ],
                [
                  '建议铺货店铺数',
                  '店铺铺货率',
                  '日均动销SKU产出',
                  '日均动销SKU数',
                  '售罄SKU数',
                ],
              ].map((item, i) => {
                return (
                  <div
                    className="h-fulls flex w-1/3 flex-col gap-y-6 border-[1px] border-dashed border-[#16C89A] bg-[rgba(22,200,154,0.04)] px-4 py-6"
                    key={i}
                  >
                    {item.map((item) => {
                      const node = data.find((node: any) => node.id === item);

                      if (!node) return null;
                      let cardClassName = node.id;
                      if (node.id === '店铺产出') {
                        cardClassName = classnames(node.id, 'mt-[200px]');
                      } else if (node.id === '日均动销SKU产出') {
                        cardClassName = classnames(node.id, 'mt-[100px]');
                      } else if (node.id === '动销店铺数') {
                        cardClassName = classnames(node.id, 'mt-[100px]');
                      }
                      return (
                        <>
                          <Card
                            observationMetrics={node.data.observationMetrics}
                            actionValue={node.data.actionValue}
                            alarm={node.data.alarm}
                            id={node.id}
                            key={node.id}
                            className={cardClassName}
                            headerProps={node.data.headerProps}
                            contentProps={node.data.contentProps}
                            footerProps={node.data.footerProps}
                            dataFlowType={node.data.dataFlowType}
                          />
                        </>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 线 */}
      {flag && (
        <div>
          {newInitialEdges.map((item) => {
            return (
              <Xarrow
                start={item.target}
                end={item.source}
                key={item.id}
                startAnchor={item.startAnchor || 'left'}
                endAnchor={item.endAnchor || 'left'}
                // startAnchor={'left'}
                path="grid"
                strokeWidth={1.1}
                gridBreak="30"
                showHead={false}
                showTail
                lineColor="#08162E"
                headColor="#08162E"
                tailColor="#08162E"
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
