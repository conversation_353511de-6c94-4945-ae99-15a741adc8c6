import { create } from 'zustand';

import {
  baiEdges,
  coloNodes,
  fendaNodes,
  guiEdges,
  haEdges,
  leEdges,
  meizhiyuanNodes,
  wanEdges,
  xuebiNodes,
} from '../data/nodes';

const types = {
  coco: '可口可乐',
  fen: '芬达',
  mei: '美汁源',
  xue: '雪碧',
  le: '乐事',
  bai: '百草味',
  gui: '桂格',
  wan: '湾仔码头',
  ha: '哈根达斯',
};

type brandType =
  | '可口可乐'
  | '芬达'
  | '美之源'
  | '雪碧'
  | '乐事'
  | '百草味'
  | '桂格'
  | '湾仔码头'
  | '哈根达斯';

const reducer = (type: brandType) => {
  switch (type) {
    case types.coco:
      return { data: coloNodes };
    case types.fen:
      return { data: fendaNodes };
    case types.mei:
      return { data: meizhiyuanNodes };
    case types.xue:
      return { data: xuebiNodes };
    case types.le:
      return { data: leEdges };
    case types.bai:
      return { data: baiEdges };
    case types.gui:
      return { data: guiEdges };
    case types.wan:
      return { data: wanEdges };
    case types.ha:
      return { data: haEdges };
  }
};

export const useDataStore = create((set) => ({
  data: coloNodes,
  setData: (type: brandType) => {
    set(() => reducer(type));
  },
}));
