import { useEffect, useRef, useState } from 'react';
import Xarrow, { <PERSON>wrapper, useXarrow } from 'react-xarrows';

import consumptionPotential from '@/assets/images/consumptionPotential.jpg';
import supplyHhealth from '@/assets/images/supplyHealth.jpg';
import { useSize } from 'ahooks';
import classnames from 'classnames';

import { initialEdges } from '../../data/edges';
import { Card } from '../Card';
import { useDataStore } from '../store';

export default function LayoutContent() {
  const data = useDataStore((state: any) => state.data);
  const GMVnode = data.find((item: any) => item.id === '销售额');

  data.forEach((item: any) => {
    if (
      item.id === '场景人数' ||
      item.id === '场景花费' ||
      item.id === '新客客单价' ||
      item.id === '老客客单价' ||
      item.id === '十大人群' ||
      item.id === '新客数' ||
      item.id === '老客频次' ||
      item.id === '老客复购率' ||
      item.id === '店铺动销率' ||
      item.id === '平均SKU产出' ||
      item.id === '未铺货门店数' ||
      item.id === '在售SKU数' ||
      item.id === '未动销SKU数' ||
      item.id === '售罄SKU预估损失'
    ) {
      item.data.dataFlowType = 'input';
      item.data.observationMetrics = '---';
      item.data.actionValue = '---';
    } else if (
      item.id === '客单价' ||
      item.id === '订单数' ||
      item.id === '场景数量' ||
      item.id === '交易用户数' ||
      item.id === '购买频次' ||
      item.id === '老客数' ||
      item.id === '新客频次' ||
      item.id === '历史用户数' ||
      item.id === '动销店铺数' ||
      item.id === '店铺产出' ||
      item.id === '铺货店铺数' ||
      item.id === '建议铺货店铺数' ||
      item.id === '售罄SKU数' ||
      item.id === '动销SKU数'
    ) {
      item.data.observationMetrics = '---';
      item.data.dataFlowType = 'process';
      item.data.actionValue = '---';
    }
  });
  data.forEach((item: any) => {
    if (item.id === '场景数量') {
      item.data.headerProps.title = '人均场景数量';
    }
    if (item.id === '场景人数') {
      item.data.headerProps.title = '场景人数占比';
    }
    if (item.id === '场景花费') {
      item.data.headerProps.title = '人均场景花费';
    }
  });
  data.forEach((item: any) => {
    if (
      item.id === '老客频次' ||
      item.id === '老客复购率' ||
      item.id === '店铺动销率'
    ) {
      item.data.alarm = false;
    }
  });

  const [flag, setFlag] = useState(false);

  useEffect(() => {
    setFlag(true);
  }, []);

  return (
    <div className="flex justify-center bg-white py-10">
      <div className="3xl:px-10 3xl:gap-y-5 flex w-[1200px] flex-col px-5">
        <div className="3xl:gap-x-10 flex justify-between">
          {/* GMV */}
          <div className="w-1/4 px-4 py-5">
            {GMVnode && (
              <Card
                alarm={GMVnode.data.alarm}
                className={GMVnode.id}
                key={GMVnode.id}
                id={GMVnode.id}
                headerProps={GMVnode.data.headerProps}
                contentProps={GMVnode.data.contentProps}
                footerProps={GMVnode.data.footerProps}
                dataFlowType="output"
                observationMetrics={'--'}
              />
            )}
          </div>
          {/* 图例 */}
          <div className="flex w-3/4 flex-col justify-end">
            <div className="3xl:gap-x-6 flex h-1/4 items-center justify-end gap-x-3">
              <div className="flex items-center gap-x-1">
                <div
                  className="3xl:w-6 3xl:h-6 h-4 w-4 2xl:h-5 2xl:w-5"
                  style={{
                    background:
                      'linear-gradient( 101deg, #45B3FD 0%, #0CE8FD 100%)',
                  }}
                />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  output指标
                </span>
              </div>

              <div className="flex items-center gap-x-1">
                <div
                  className="3xl:w-6 3xl:h-6 h-4 w-4 2xl:h-5 2xl:w-5"
                  style={{
                    background:
                      ' linear-gradient( 117deg, #1871FF 0%, #0FBEFF 100%)',
                  }}
                />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  过程指标
                </span>
              </div>
              <div className="flex items-center gap-x-1">
                <div
                  className="3xl:w-6 3xl:h-6 h-4 w-4 2xl:h-5 2xl:w-5"
                  style={{
                    background:
                      'linear-gradient( 135deg, #16C89A 0%, #63EAA7 100%)',
                  }}
                />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  input指标
                </span>
              </div>

              <div className="flex items-center gap-x-1">
                <div className="3xl:w-6 3xl:h-6 h-4 w-4 border-2 border-solid border-[#38D6A1] 2xl:h-5 2xl:w-5" />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  Action
                </span>
              </div>
              <div className="flex items-center gap-x-1">
                <div className="3xl:w-6 3xl:h-6 h-4 w-4 border-2 border-dashed border-[#0147EB] bg-[rgba(24,113,255,0.04)] 2xl:h-5 2xl:w-5" />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  需求诊断
                </span>
              </div>
              <div className="flex items-center gap-x-1">
                <div className="3xl:w-6 3xl:h-6 h-4 w-4 border-2 border-dashed border-[#16C89A] bg-[rgba(22,200,154,0.04)] 2xl:h-5 2xl:w-5" />
                <span className="3xl:text-xl text-base 2xl:text-lg">
                  供给诊断
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="3xl:gap-y-10 flex flex-col gap-y-5">
          {/* 需求  */}
          <div className="3xl:gap-y-10 flex flex-col gap-y-5">
            <div className="flex h-12 items-center gap-x-4 border-[1px] border-dashed border-[#0147EB] bg-[rgba(24,113,255,0.04)] pl-5 2xl:h-16">
              <div className="3xl:w-[26px] 3xl:h-[26px] h-5 w-5 2xl:h-6 2xl:w-6">
                <img src={consumptionPotential} alt="" />
              </div>
              <div className="3xl:text-xl text-base text-[#1871FF] 2xl:text-lg">
                <span>消费潜力指数:</span>
                <span className="3xl:text-2xl text-base 2xl:text-xl">
                  29,385
                </span>
              </div>
            </div>
            <div className="3xl:gap-x-10 flex gap-x-5 2xl:gap-x-6">
              {[
                ['客单价', '场景数量', '订单数', '场景人数', '场景花费'],
                ['新客客单价', '老客客单价', '交易用户数', '购买频次'],
                ['十大人群', '老客数', '新客数', '新客频次', '老客频次'],
                ['历史用户数', '老客复购率'],
              ].map((item, i) => {
                return (
                  <div
                    className="h-fulls flex w-1/4 flex-col gap-y-6 border-[1px] border-dashed border-[#0147EB] bg-[rgba(24,113,255,0.04)] px-4 py-5"
                    key={i}
                  >
                    {item.map((item) => {
                      const node = data.find((node: any) => node.id === item);
                      if (!node) return null;
                      let cardClassName = node.id;
                      if (node.id === '历史用户数') {
                        cardClassName = classnames(node.id, 'mt-[400px]');
                      }
                      return (
                        // <Card
                        //   observationMetrics={node.data.observationMetrics}
                        //   actionValue={node.data.actionValue}
                        //   alarm={node.data.alarm}
                        //   className={cardClassName}
                        //   id={node.id}
                        //   key={node.id}
                        //   headerProps={node.data.headerProps}
                        //   contentProps={node.data.contentProps}
                        //   footerProps={node.data.footerProps}
                        //   dataFlowType={node.data.dataFlowType}
                        // />
                        <Card
                          observationMetrics={node.data.observationMetrics}
                          actionValue={node.data.actionValue}
                          alarm={node.data.alarm}
                          className={cardClassName}
                          id={node.id}
                          key={node.id}
                          headerProps={node.data.headerProps}
                          contentProps={node.data.contentProps}
                          footerProps={node.data.footerProps}
                          dataFlowType={node.data.dataFlowType}
                        />
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 供给 */}
          <div className="3xl:gap-y-10 flex flex-col gap-y-5">
            <div className="flex h-12 items-center gap-x-4 border-[1px] border-dashed border-[#16C89A] bg-[rgba(22,200,154,0.04)] pl-5 2xl:h-16">
              <div className="3xl:w-[26px] 3xl:h-[26px] h-5 w-5 2xl:h-6 2xl:w-6">
                <img src={supplyHhealth} alt="" />
              </div>
              <div className="3xl:text-xl text-base text-[#16C89A] 2xl:text-lg">
                <span>供给健康指数:</span>
                <span className="3xl:text-2xl text-base 2xl:text-xl">
                  29,385
                </span>
              </div>
            </div>
            <div className="3xl:gap-x-10 flex gap-x-5 2xl:gap-x-6">
              {[
                ['动销店铺数', '店铺产出'],
                ['店铺动销率', '铺货店铺数', '平均SKU产出', '动销SKU数'],
                [
                  '建议铺货店铺数',
                  '未铺货门店数',
                  '售罄SKU数',
                  '在售SKU数',
                  '未动销SKU数',
                ],
                ['售罄SKU预估损失'],
              ].map((item, i) => {
                return (
                  <div
                    className="h-fulls flex w-1/4 flex-col gap-y-6 border-[1px] border-dashed border-[#16C89A] bg-[rgba(22,200,154,0.04)] px-4 py-6"
                    key={i}
                  >
                    {item.map((item) => {
                      const node = data.find((node: any) => node.id === item);

                      if (!node) return null;
                      let cardClassName = node.id;
                      if (node.id === '店铺产出') {
                        cardClassName = classnames(node.id, 'mt-[200px]');
                      } else if (node.id === '售罄SKU预估损失') {
                        cardClassName = classnames(node.id, 'mt-[740px]');
                      }
                      return (
                        <>
                          <Card
                            observationMetrics={node.data.observationMetrics}
                            actionValue={node.data.actionValue}
                            alarm={node.data.alarm}
                            id={node.id}
                            key={node.id}
                            className={cardClassName}
                            headerProps={node.data.headerProps}
                            contentProps={node.data.contentProps}
                            footerProps={node.data.footerProps}
                            dataFlowType={node.data.dataFlowType}
                          />
                        </>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 线 */}
      {flag && (
        <div>
          {initialEdges.map((item) => {
            return (
              <Xarrow
                start={item.target}
                end={item.source}
                key={item.id}
                startAnchor={item.startAnchor || 'left'}
                endAnchor={item.endAnchor || 'left'}
                // startAnchor={'left'}
                path="grid"
                strokeWidth={1}
                gridBreak="30"
                showHead={false}
                showTail
                lineColor="#08162E"
                headColor="#08162E"
                tailColor="#08162E"
              />
            );
          })}
        </div>
      )}
    </div>
  );
}
