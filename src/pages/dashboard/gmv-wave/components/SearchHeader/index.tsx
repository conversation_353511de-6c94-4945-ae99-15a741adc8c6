import { useEffect, useMemo, useState } from 'react';

import { RedoOutlined, SearchOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Select, Space } from 'antd';

import { useDataStore } from '../store';

export default function SearchHeader() {
  const { initialState } = useModel('@@initialState');

  const [select, setSelect] = useState<string>();

  const options = useMemo(() => {
    switch (initialState?.currentBrandInfo?.brandNameCn) {
      case '百事食品':
        return [
          { value: '乐事', label: '乐事' },
          { value: '百草味', label: '百草味' },
          { value: '桂格', label: '桂格' },
        ];
      case '通用磨坊':
        return [
          { value: '湾仔码头', label: '湾仔码头' },
          { value: '哈根达斯', label: '哈根达斯' },
        ];
      default:
        return [
          { value: '可口可乐', label: '品牌A' },
          { value: '雪碧', label: '品牌B' },
          { value: '美汁源', label: '品牌C' },
          { value: '芬达', label: '品牌D' },
          // { value: '乐事', label: '品牌A' },
        ];
    }
  }, [initialState]);

  const dispatch = useDataStore((state: any) => state.setData);

  useEffect(() => {
    setSelect(options[0]?.value);
  }, [options]);

  useEffect(() => {
    dispatch(options[0]?.value);
  }, [dispatch, options]);

  return (
    <div className="flex bg-white pl-8">
      <Space size={30}>
        <div className="flex items-center gap-x-4 py-4">
          <span className="text-base text-[#536387]">品牌</span>
          <Select
            style={{ width: 120 }}
            value={select}
            onChange={setSelect}
            options={options}
          />
        </div>
        <Button className="h-9" icon={<RedoOutlined />}>
          重置
        </Button>
        <Button
          className="h-9"
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => {
            dispatch(select);
          }}
        >
          查询
        </Button>
      </Space>
    </div>
  );
}
