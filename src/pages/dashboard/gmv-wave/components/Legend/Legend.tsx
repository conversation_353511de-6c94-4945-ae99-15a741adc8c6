import React from 'react';

export default function Legend() {
  return (
    <div className="mb-2 flex flex-col justify-end gap-y-3 xl:flex-row xl:items-end xl:justify-start xl:gap-x-3">
      <div className="flex items-center gap-x-3">
        <div className="flex items-center gap-x-1">
          <div
            className="h-4 w-4"
            style={{
              background: 'linear-gradient( 101deg, #45B3FD 0%, #0CE8FD 100%)',
            }}
          />
          <span className="text-base">output指标</span>
        </div>

        <div className="flex items-center gap-x-1">
          <div
            className="h-4 w-4"
            style={{
              background: ' linear-gradient( 117deg, #1871FF 0%, #0FBEFF 100%)',
            }}
          />
          <span className="text-base">过程指标</span>
        </div>

        <div className="flex items-center gap-x-1">
          <div
            className="h-4 w-4"
            style={{
              background: 'linear-gradient( 135deg, #16C89A 0%, #63EAA7 100%)',
            }}
          />
          <span className="text-base">input指标</span>
        </div>
      </div>
      <div className="flex items-center gap-x-3">
        <div className="flex items-center gap-x-1">
          <div className="h-4 w-4 border-2 border-solid border-[#38D6A1]" />
          <span className="text-base">Action</span>
        </div>

        <div className="flex items-center gap-x-1">
          <div className="h-4 w-4 border-2 border-dashed border-[#0147EB] bg-[rgba(24,113,255,0.04)]" />
          <span className="text-base">需求诊断</span>
        </div>

        <div className="flex items-center gap-x-1">
          <div className="h-4 w-4 border-2 border-dashed border-[#16C89A] bg-[rgba(22,200,154,0.04)]" />
          <span className="text-base">供给诊断</span>
        </div>
      </div>
    </div>
  );
}
