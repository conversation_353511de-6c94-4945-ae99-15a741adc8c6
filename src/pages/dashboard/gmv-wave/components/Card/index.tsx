import type { FC } from 'react';

import type { ContentProps } from './components/Content';
import { Content } from './components/Content';
import type { FooterProps } from './components/Footer';
import { Footer } from './components/Footer';
import type { HeaderProps } from './components/Header';
import { Header } from './components/Header';
import type { longContentProps } from './components/LongCardContent';
import { LongCardContent } from './components/LongCardContent';

export type CardProps = {
  contentProps: ContentProps | longContentProps;
  footerProps?: FooterProps;
  headerProps: HeaderProps;
  id: any;
  className: string;
  dataFlowType: 'output' | 'input' | 'process';
  alarm: boolean;
  observationMetrics: string;
  actionValue?: string;
};

export const Card: FC<CardProps> = ({
  headerProps,
  contentProps,
  footerProps,
  id,
  className,
  dataFlowType,
  alarm,
  observationMetrics,
  actionValue,
}) => {
  const backgroundColor = (dataFlowType: 'output' | 'input' | 'process') => {
    switch (dataFlowType) {
      case 'output':
        return 'linear-gradient( 101deg, #45B3FD 0%, #0CE8FD 100%)';
      case 'input':
        return 'linear-gradient( 135deg, #16C89A 0%, #63EAA7 100%)';
      case 'process':
        return 'linear-gradient( 117deg, #1871FF 0%, #0FBEFF 100%)';
    }
  };
  return (
    <>
      <div
        className={`${className} shadow-2xl  rounded-md  overflow-hidden`}
        id={id}
        style={{
          background: backgroundColor(dataFlowType),
        }}
      >
        <Header {...headerProps} alarm={alarm} />

        {contentProps?.contentType === 'long' && (
          <LongCardContent
            {...contentProps}
            dataFlowType={dataFlowType}
            observationMetrics={observationMetrics}
            actionValue={actionValue}
          />
        )}
        {contentProps?.contentType === 'short' && (
          <Content
            {...contentProps}
            dataFlowType={dataFlowType}
            observationMetrics={observationMetrics}
            actionValue={actionValue}
          />
        )}
        {footerProps && <Footer {...footerProps} />}
      </div>
    </>
  );
};
