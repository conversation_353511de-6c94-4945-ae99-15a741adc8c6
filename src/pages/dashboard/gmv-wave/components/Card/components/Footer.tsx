import type { FC } from 'react';

import { Button, ConfigProvider } from 'antd';

export type FooterProps = {
  buttons: Array<{
    text: string;
    onClick?: () => void;
  }>;
};
export const Footer: FC<FooterProps> = ({
  buttons,
  // buttonColor,
}) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBorderColor: '#16C89A',
            defaultColor: '#16C89A',
          },
        },
      }}
    >
      <div className="flex flex-wrap items-center justify-center px-1  py-2 mt-[1px] bg-white  3xl:gap-x-2 3xl:gap-y-3 gap-x-1 gap-y-2">
        {buttons?.map((item, index) => {
          return (
            <ConfigProvider
              key={index}
              theme={{
                components: {
                  Button: {
                    defaultActiveBg: '#16C89A',
                    defaultActiveBorderColor: '#16C89A',
                    defaultActiveColor: '#ffffff',
                    defaultHoverBg: '#16C89A',
                    defaultHoverBorderColor: '#16C89A',
                    defaultHoverColor: '#ffffff',
                  },
                },
              }}
            >
              <Button className="h-7 3xl:h-9 4xl:h-10" key={index}>
                {item.text}
              </Button>
            </ConfigProvider>
          );
        })}
      </div>
    </ConfigProvider>
  );
};
