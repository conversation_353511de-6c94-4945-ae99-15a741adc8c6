import type { FC, ReactNode } from 'react';

import alarms from '@/assets/images/alarms.jpg';
import message from '@/assets/images/message.jpg';

export type HeaderProps = {
  title: string;
  icons?: ReactNode;
  // cardType: 'output' | 'input' | 'process';
  // dataFlowType: 'output' | 'input' | 'process';
  alarm: boolean;
};

export const Header: FC<HeaderProps> = ({ title, alarm = true }) => {
  return (
    <div className="3xl:px-5 3xl:py-3 4xl:h-20 3xl:h-14 flex h-10 items-center justify-between border-b border-b-white px-4 py-2 text-white 2xl:h-12">
      <div className="3xl:text-2xl 4xl:text-3xl text-base 2xl:text-xl">
        {title}
      </div>
      <div className="4xl:gap-x-5 3xl:gap-x-3 flex items-end justify-center gap-x-2 2xl:gap-x-3">
        <img
          className="3xl:scale-100 4xl:scale-125 scale-75 2xl:scale-100"
          src={message}
        />

        {alarm && (
          <img
            className="3xl:scale-100 4xl:scale-125 scale-75 2xl:scale-100"
            src={alarms}
          />
        )}
      </div>
    </div>
  );
};
