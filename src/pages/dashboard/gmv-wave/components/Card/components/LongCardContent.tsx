// import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import type { FC } from 'react';

export type longContentProps = {
  baseData: Array<{
    name: string;
    quantity: string;
  }>;
  cycleData?: Array<{
    name: string;
    cycleQuantity: string;
    percentageChange: 'rise' | 'fall';
    percentage: string;
  }>;

  contentType: 'long';
  dataFlowType: 'output' | 'input' | 'process';
  // 观测指标
  observationMetrics: string;

  // 做功价值
  actionValue?: string;
};
export const LongCardContent: FC<longContentProps> = ({
  baseData,
  // cycleData,

  dataFlowType,
  observationMetrics,
  actionValue,
}) => {
  return (
    <div className="flex flex-col gap-3 bg-white py-5 pl-3 pr-3">
      {/* 上部分 */}
      <div className="flex justify-between text-sm 2xl:pl-4">
        {/* 上左 */}
        <div className="flex w-[70%] flex-col gap-1">
          {baseData.map((item, index) => {
            return (
              <div
                className="flex flex-wrap items-start text-xs 2xl:text-sm"
                key={index}
                style={{
                  color: `${dataFlowType === 'process' ? '#1873FF' : '#16C89A'}`,
                }}
              >
                {/* 圆点 */}
                <div className="flex items-center gap-x-2 text-center">
                  <div
                    className="h-1 w-1 rounded 2xl:h-[6px] 2xl:w-[6px]"
                    style={{
                      background: `${dataFlowType === 'process' ? '#1873FF' : '#16C89A'}`,
                    }}
                  />
                  {/* 名字 */}
                  <div className="mr-1" style={{ color: 'black' }}>
                    {item.name} :
                  </div>
                </div>
                {/* 数据 */}
                <div className="flex justify-start gap-x-1 xl:gap-x-2">
                  <div>
                    <span>{item.quantity}</span>
                  </div>
                  {item.quantity && (
                    <div>
                      <span>{item.percentage}</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        {/* 上右 */}
        <div
          className={`3xl:gap-y-5 4xl:text-xl 3xl:text-base flex w-[30%] min-w-14 flex-shrink-0 flex-col gap-1 border-l-[1px] pl-1 text-xs 2xl:pl-2 2xl:text-sm ${dataFlowType === 'process' ? 'border-l-[#1873FF]' : ''} ${dataFlowType === 'input' ? 'border-l-[#16C89A]' : ''} ${
            dataFlowType === 'output'
              ? 'border-l-white text-white'
              : 'text-[#A4AFCA]'
          } `}
        >
          <div className="flex flex-col">
            <span>观测指标</span>
            <span className="3xl:text-sm text-xs">{observationMetrics}</span>
          </div>

          {actionValue ? (
            <div className="flex flex-col">
              <span>做功价值</span>
              <span className="3xl:text-sm text-xs">{actionValue}</span>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
