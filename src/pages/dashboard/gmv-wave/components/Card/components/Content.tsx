import type { FC, ReactNode } from 'react';

import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

export type ContentProps = {
  // 数量
  quantity: ReactNode;
  // 基期数量
  cycleQuantity: ReactNode;
  // 百分比
  percentage: ReactNode;
  // 百分比变化
  percentageChange: 'rise' | 'fall';

  contentType: 'short';
  // 单位
  unit: string;
  dataFlowType: 'output' | 'input' | 'process';
  // 观测指标
  observationMetrics: string;

  // 做功价值
  actionValue?: string;
};

export const Content: FC<ContentProps> = ({
  quantity,
  cycleQuantity,
  percentage,
  percentageChange,

  unit,
  dataFlowType,
  observationMetrics,
  actionValue,
}) => {
  const textColor = (dataFlowType: 'output' | 'input' | 'process') => {
    switch (dataFlowType) {
      case 'output':
        return '#FFFFFF';
      case 'input':
        return '#16C89A';
      case 'process':
        return '#1873FF';
    }
  };

  return (
    <div
      className={`3xl:py-5 4xl:min-h-48 3xl:min-h-36 in-h-28 3xl:px-3 flex justify-between px-2 py-3 2xl:min-h-32 ${
        dataFlowType === 'output' ? '' : 'bg-white'
      } `}
    >
      <div className="3xl:text-4xl 4xl:text-5xl flex w-[70%] max-w-64 flex-col justify-between px-3 text-xl 2xl:text-3xl">
        <div
          className="flex items-end 2xl:mt-2"
          style={{
            color: textColor(dataFlowType),
          }}
        >
          {/* 数量显示 */}
          {quantity}
          <div className="4xl:text-lg text-sm">{unit}</div>
        </div>
        <div
          className={`flex flex-wrap items-center justify-around rounded-2xl px-1 ${dataFlowType === 'process' ? 'bg-[#F1F7FF]' : ''} ${dataFlowType === 'input' ? 'bg-[#F1FCF9]' : ''} ${dataFlowType === 'output' ? 'bg-[#7EE1FD]' : ''} `}
        >
          {/* 基期数据显示 */}

          <div
            className={`${
              percentageChange === 'rise' ? 'text-[#EB0101]' : 'text-[#03BF03]'
            } 3xl:text-[22px] 4xl:text-2xl flex items-center justify-center text-base`}
          >
            {percentageChange === 'rise' ? (
              <CaretUpOutlined className="3xl:scale-100 4xl:scale-125 scale-75" />
            ) : (
              <CaretDownOutlined className="3xl:scale-100 4xl:scale-125 scale-75" />
            )}
            {/* 百分比数据 */}
            {percentage}
          </div>
          <div
            className="4xl:text-base text-[10px] 2xl:text-[11px]"
            style={{
              color: textColor(dataFlowType),
            }}
          >
            基期：{cycleQuantity}
          </div>
        </div>
      </div>
      <div
        className={`3xl:gap-y-5 4xl:text-xl 3xl:text-base flex w-[30%] min-w-14 flex-shrink-0 flex-col gap-1 border-l-[1px] pl-1 text-xs 2xl:pl-2 2xl:text-sm ${dataFlowType === 'process' ? 'border-l-[#1873FF]' : ''} ${dataFlowType === 'input' ? 'border-l-[#16C89A]' : ''} ${
          dataFlowType === 'output'
            ? 'border-l-white text-white'
            : 'text-[#A4AFCA]'
        } `}
      >
        <div className="flex flex-col">
          <span>观测指标</span>
          <span className="3xl:text-sm text-xs">{observationMetrics}</span>
        </div>

        {actionValue ? (
          <div className="flex flex-col">
            <span>做功价值</span>
            <span className="3xl:text-sm text-xs">{actionValue}</span>
          </div>
        ) : null}
      </div>
    </div>
  );
};
