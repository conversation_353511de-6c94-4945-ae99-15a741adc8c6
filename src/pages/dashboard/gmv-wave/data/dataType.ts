export type dataItemType = {
  // 唯一id
  id: string;
  parentId?: string;
  // 标题
  title: string;
  // 区分颜色
  dataflowType: 'output' | 'input' | 'process';
  // 供需类型
  compassType: 'supply' | 'demand';
  // 预警
  alarm?: any;
  // 评论
  chat?: any;
  // 观测指标
  observationMetrics: string;
  // 做功价值
  actionValue?: string;
  // 主指标
  mainMetric?: {
    value: string;
    unit: string;
  };
  // 十大人群的列表指标
  listMetrics?: {
    value: string;
    label: string;
    unit: string;
  }[];
  // 基期
  basePeriod?: {
    value: string;
    // 单位
    unit: string;
  };
  // 百分比
  percentage?: {
    value: string;
    honglv: 'down' | 'up' | 'same';
  };
  actions?: { label: string; data: any }[];
};
