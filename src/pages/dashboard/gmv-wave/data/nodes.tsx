// import { Node } from 'reactflow';

export const coloNodes: any[] = [
  {
    id: '客单价',
    type: 'cardSelectorNode',
    position: { x: 300, y: 200 },
    data: {
      headerProps: {
        title: '客单价',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '10.23',
        cycleQuantity: '9.56',
        percentage: '7%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '订单数',
    type: 'cardSelectorNode',
    position: { x: 300, y: 0 },
    data: {
      headerProps: {
        title: '订单数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '5,663,383',
        cycleQuantity: '3,584,599',
        percentage: '58%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '十大人群',
    height: 405,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '十大人群',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '941,966' },
          { name: '在校学生', quantity: '13,783,017' },
          { name: '精致妈妈', quantity: '2,301,848' },
          { name: '都市银发', quantity: '1,881,920' },
          { name: '都市白领', quantity: '5,227,354' },
          { name: '都市中产', quantity: '27,423,121' },
          { name: '都市监领', quantity: '394,829' },
          { name: '小镇青年', quantity: '21,242,079' },
          { name: '小镇中老年', quantity: '7,866,009' },
          { name: '其他', quantity: '14,035,737' },
        ],
        textColor: '#00DA9D',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '销售额',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: 'GMV',
        headerColor: 'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
      },
      contentProps: {
        quantity: '57,945,062',
        cycleQuantity: 'NNN',
        percentage: '69%',
        percentageChange: 'rise',
        contentBackgroundColor:
          'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
        textColor: 'white',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '购买频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 200 },
    data: {
      headerProps: {
        title: '购买频次',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.46',
        cycleQuantity: '1.32',
        percentage: '7%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },

  {
    id: '新客数',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 100 },
    data: {
      headerProps: {
        title: '新客数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1,283,077',
        cycleQuantity: '1,020,064',
        percentage: '26%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#38D6A1',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    type: 'cardSelectorNode',
    position: { x: 200, y: 100 },
    data: {
      headerProps: {
        title: '老客数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '2,605,035',
        cycleQuantity: '1,622,846',
        percentage: '61%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '历史用户数',
    type: 'cardSelectorNode',
    position: { x: 200, y: 100 },
    data: {
      headerProps: {
        title: '历史用户数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '3,888,112',
        cycleQuantity: '2,642,910',
        percentage: '47%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    type: 'cardSelectorNode',
    position: { x: 300, y: 100 },
    data: {
      headerProps: {
        title: '交易用户数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '3,888,112',
        cycleQuantity: '2,642,910',
        percentage: '47%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '人',
      },
    },
  },

  {
    id: '新客客单价',
    type: 'cardSelectorNode',
    position: { x: 100, y: 200 },
    data: {
      headerProps: {
        title: '新客客单价',
        headerColor: 'linear-gradient( 295deg, #ECFAA7 0%, #2BE2D3 100%)',
      },
      contentProps: {
        quantity: '10.14',
        cycleQuantity: '9.08',
        percentage: '12%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#38D6A1 ',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },

  {
    id: '老客客单价',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 300, y: 200 },
    data: {
      headerProps: {
        title: '老客客单价',
        headerColor: 'linear-gradient( 295deg, #ECFAA7 0%, #2BE2D3 100%)',
      },
      contentProps: {
        quantity: '10.26',
        cycleQuantity: '9.75',
        percentage: '5%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#38D6A1 ',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '新客频次',
    type: 'cardSelectorNode',
    position: { x: 300, y: 300 },
    data: {
      headerProps: {
        title: '新客频次',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.0',
        cycleQuantity: '1.0',
        percentage: '0%',
        percentageChange: 'stable',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '老客频次',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 500, y: 300 },
    data: {
      headerProps: {
        title: '老客频次',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1.68',
        cycleQuantity: '1.58',
        percentage: '6%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#38D6A1 ',
        contentType: 'short',
        unit: '次',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌忠诚客' },
        ],
      },
    },
  },
  {
    id: '场景数量',
    type: 'cardSelectorNode',
    position: { x: 300, y: 300 },
    data: {
      headerProps: {
        title: '场景数量',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.00',
        cycleQuantity: '1.00',
        percentage: '0%',
        percentageChange: 'stable',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    type: 'cardSelectorNode',
    height: 824,
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景人数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '1,080,390' },
          { name: '能量早午餐', quantity: '703,945' },
          { name: '独享下午茶', quantity: '654,743' },
          { name: '日间采购', quantity: '399,011' },
          { name: '本地玩乐', quantity: '309,796' },
          { name: '居家聚会', quantity: '289,955' },
          { name: '异地出行', quantity: '229,407' },
          { name: '礼赠关怀', quantity: '217,920' },
          { name: '周末补货', quantity: '197,726' },
          { name: '重物到家', quantity: '186,565' },
          { name: '买菜做饭', quantity: '135,794' },
          { name: '职场共餐', quantity: '130,590' },
          { name: '突发应急', quantity: '88,075' },
          { name: '家庭陪伴', quantity: '79,716' },
          { name: '夜间置物', quantity: '31,457' },
          { name: '甜蜜约会', quantity: '28,012' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '652,109',
            percentageChange: 'rise',
            percentage: '66%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '443,456',
            percentageChange: 'rise',
            percentage: '59%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '420,401',
            percentageChange: 'rise',
            percentage: '56%',
          },
          {
            name: '日间采购',
            cycleQuantity: '275,027',
            percentageChange: 'rise',
            percentage: '45%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '211,805',
            percentageChange: 'rise',
            percentage: '46%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '241,664',
            percentageChange: 'rise',
            percentage: '20%',
          },
          {
            name: '异地出行',
            cycleQuantity: '167,991',
            percentageChange: 'rise',
            percentage: '37%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '152,405',
            percentageChange: 'rise',
            percentage: '43%',
          },
          {
            name: '周末补货',
            cycleQuantity: '104,748',
            percentageChange: 'rise',
            percentage: '89%',
          },
          {
            name: '重物到家',
            cycleQuantity: '108,202',
            percentageChange: 'rise',
            percentage: '72%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '93,575',
            percentageChange: 'rise',
            percentage: '45%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '99,383',
            percentageChange: 'rise',
            percentage: '31%',
          },
          {
            name: '突发应急',
            cycleQuantity: '61,311',
            percentageChange: 'rise',
            percentage: '44%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '45,628',
            percentageChange: 'rise',
            percentage: '75%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '17,974',
            percentageChange: 'rise',
            percentage: '75%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '18,380',
            percentageChange: 'rise',
            percentage: '52%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '场景花费',
    type: 'cardSelectorNode',
    height: 824,
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景花费',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '11,981,889' },
          { name: '能量早午餐', quantity: '8,828,288' },
          { name: '独享下午茶', quantity: '7,999,832' },
          { name: '日间采购', quantity: '4,087,893' },
          { name: '本地玩乐', quantity: '4,080,793' },
          { name: '居家聚会', quantity: '5,926,110' },
          { name: '异地出行', quantity: '2,051,857' },
          { name: '礼赠关怀', quantity: '2,624,255' },
          { name: '周末补货', quantity: '1,949,082' },
          { name: '重物到家', quantity: '3,113,102' },
          { name: '买菜做饭', quantity: '1,212,021' },
          { name: '职场共餐', quantity: '1,966,321' },
          { name: '突发应急', quantity: '870,736  ' },
          { name: '家庭陪伴', quantity: '737,384  ' },
          { name: '夜间置物', quantity: '233,337  ' },
          { name: '甜蜜约会', quantity: '202,298  ' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '5,831,746',
            percentageChange: 'rise',
            percentage: '105.46%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '4,914,528',
            percentageChange: 'rise',
            percentage: '79.64%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '4,513,162',
            percentageChange: 'rise',
            percentage: '77.26%',
          },
          {
            name: '日间采购',
            cycleQuantity: '2,275,663',
            percentageChange: 'rise',
            percentage: '79.64%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '2,466,428',
            percentageChange: 'rise',
            percentage: '65.45%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '4,985,497',
            percentageChange: 'rise',
            percentage: '18.87%',
          },
          {
            name: '异地出行',
            cycleQuantity: '1,371,665',
            percentageChange: 'rise',
            percentage: '49.59%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '1,816,869',
            percentageChange: 'rise',
            percentage: '44.44%',
          },
          {
            name: '周末补货',
            cycleQuantity: '833,377',
            percentageChange: 'rise',
            percentage: '133.88%',
          },
          {
            name: '重物到家',
            cycleQuantity: '1,912,506',
            percentageChange: 'rise',
            percentage: '62.78%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '745,021',
            percentageChange: 'rise',
            percentage: '62.68%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '1,410,047',
            percentageChange: 'rise',
            percentage: '39.45%',
          },
          {
            name: '突发应急',
            cycleQuantity: '553,787',
            percentageChange: 'rise',
            percentage: '57.23%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '364,111',
            percentageChange: 'rise',
            percentage: '102.52%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '117,477',
            percentageChange: 'rise',
            percentage: '98.62%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '122,062',
            percentageChange: 'rise',
            percentage: '65.73%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: '全域宫销联动高价值产品' }],
      },
    },
  },
  {
    id: '老客复购率',
    type: 'cardSelectorNode',
    position: { x: 500, y: 300 },
    data: {
      headerProps: {
        title: '老客复购率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '67%',
        cycleQuantity: '61%',
        percentage: '9%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#38D6A1',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌老客' },
        ],
      },
    },
  },
  {
    id: '动销店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 400 },
    data: {
      headerProps: {
        title: '动销店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '43,448',
        cycleQuantity: '35,287',
        percentage: '23%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '店铺产出',
    type: 'cardSelectorNode',
    position: { x: 300, y: 400 },
    data: {
      headerProps: {
        title: '店铺产出',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1,333.7',
        cycleQuantity: '970.9',
        percentage: '37%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '店铺动销率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '15%',
        cycleQuantity: '13%',
        percentage: '14%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#00DA9D',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '292,581',
        cycleQuantity: '292,581',
        percentage: '0%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '建议铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '356,202',
        cycleQuantity: '323,482',
        percentage: '10%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '未铺货门店数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '未铺货门店数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '63,621',
        cycleQuantity: '30,901',
        percentage: '106%',
        percentageChange: 'fall',
        contentBackgroundColor: 'white',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '店',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '售罄SKU数',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '售罄SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '414,494',
        cycleQuantity: '320,293',
        percentage: '29%',
        percentageChange: 'fall',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '在售SKU数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '在售SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '2,156,574',
        cycleQuantity: '1,913,340',
        percentage: '13%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '未动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '未动销SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '22,059,157',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    type: 'cardSelectorNode',
    position: { x: 500, y: 400 },
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1,729,707',
        cycleQuantity: '802,038',
        percentage: '116%',
        percentageChange: 'fall',
        contentBackgroundColor: 'white',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '商端计划-下单订货' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '平均SKU产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '平均SKU产出',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '594.8',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS活动机制' }],
      },
    },
  },
  {
    id: '动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '97,417',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
];
export const xuebiNodes: any[] = [
  {
    id: '十大人群',
    height: 405,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '十大人群',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '307,370' },
          { name: '在校学生', quantity: '5,502,803' },
          { name: '精致妈妈', quantity: '762,201' },
          { name: '都市银发', quantity: '596,891' },
          { name: '都市白领', quantity: '1,926,093' },
          { name: '都市中产', quantity: '10,529,883' },
          { name: '都市监领', quantity: '140,657' },
          { name: '小镇青年', quantity: '9,573,793' },
          { name: '小镇中老年', quantity: '3,161,442' },
          { name: '其他', quantity: '5,583,696' },
        ],
        textColor: '#00DA9D',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '销售额',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: 'GMV',
        headerColor: 'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
      },
      contentProps: {
        quantity: '11,905,093',
        cycleQuantity: '8,663,410',
        percentage: '37%',
        percentageChange: 'rise',
        contentBackgroundColor:
          'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
        textColor: 'white',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '订单数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '订单数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1,566,768',
        cycleQuantity: '1,118,259',
        percentage: '40%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '576,247',
        cycleQuantity: '480,695',
        percentage: '20%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '649,811',
        cycleQuantity: '433,904',
        percentage: '50%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '交易用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '交易用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1,226,058',
        cycleQuantity: '914,599',
        percentage: '34%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '人',
      },
    },
  },
  {
    id: '购买频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '购买频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.28',
        cycleQuantity: '1.22',
        percentage: '5%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '客单价',
    type: 'cardSelectorNode',
    position: { x: 300, y: 200 },
    data: {
      headerProps: {
        title: '客单价',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '7.60',
        cycleQuantity: '7.75',
        percentage: '-2%',
        percentageChange: 'fall',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '新客客单价',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '8.08',
        cycleQuantity: '7.96',
        percentage: '1%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '老客客单价',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '7.32',
        cycleQuantity: '7.58',
        percentage: '-4%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '新客频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.0',
        cycleQuantity: '1.0',
        percentage: '0%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '老客频次',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客频次',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1.52',
        cycleQuantity: '1.47',
        percentage: '4%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '次',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌忠诚客' },
        ],
      },
    },
  },
  {
    id: '场景数量',
    type: 'cardSelectorNode',
    position: { x: 300, y: 300 },
    data: {
      headerProps: {
        title: '场景数量',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.00',
        cycleQuantity: '1.00',
        percentage: '0%',
        percentageChange: 'stable',
        contentBackgroundColor: 'white',
        textColor: '##1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    height: 824,
    data: {
      headerProps: {
        title: '场景人数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '337,074' },
          { name: '能量早午餐', quantity: '201,349' },
          { name: '独享下午茶', quantity: '183,861' },
          { name: '日间采购', quantity: '114,035' },
          { name: '本地玩乐', quantity: '97,170' },
          { name: '居家聚会', quantity: '89,886' },
          { name: '异地出行', quantity: '63,245' },
          { name: '礼赠关怀', quantity: '62,902' },
          { name: '周末补货', quantity: '59,293' },
          { name: '重物到家', quantity: '56,449' },
          { name: '职场共餐', quantity: '37,069' },
          { name: '买菜做饭', quantity: '32,877' },
          { name: '突发应急', quantity: '25,669' },
          { name: '家庭陪伴', quantity: '19,335' },
          { name: '夜间置物', quantity: '7,012' },
          { name: '甜蜜约会', quantity: '7,008' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '237,110',
            percentageChange: 'rise',
            percentage: '42%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '143,533',
            percentageChange: 'rise',
            percentage: '40%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '130,686',
            percentageChange: 'rise',
            percentage: '41%',
          },
          {
            name: '日间采购',
            cycleQuantity: '92,572',
            percentageChange: 'rise',
            percentage: '23%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '73,865',
            percentageChange: 'rise',
            percentage: '32%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '75,153',
            percentageChange: 'rise',
            percentage: '20%',
          },
          {
            name: '异地出行',
            cycleQuantity: '50,228',
            percentageChange: 'rise',
            percentage: '26%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '46,547',
            percentageChange: 'rise',
            percentage: '35%',
          },
          {
            name: '周末补货',
            cycleQuantity: '37,215',
            percentageChange: 'rise',
            percentage: '59%',
          },
          {
            name: '重物到家',
            cycleQuantity: '32,962',
            percentageChange: 'rise',
            percentage: '71%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '29,713',
            percentageChange: 'rise',
            percentage: '25%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '26,901',
            percentageChange: 'rise',
            percentage: '22%',
          },
          {
            name: '突发应急',
            cycleQuantity: '19,109',
            percentageChange: 'rise',
            percentage: '34%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '11,676',
            percentageChange: 'rise',
            percentage: '66%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '4,417',
            percentageChange: 'rise',
            percentage: '59%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '4,632',
            percentageChange: 'rise',
            percentage: '51%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '场景花费',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    height: 824,
    data: {
      headerProps: {
        title: '场景花费',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '2,451,959' },
          { name: '能量早午餐', quantity: '1,776,897' },
          { name: '独享下午茶', quantity: '1,589,506' },
          { name: '日间采购', quantity: '792,881' },
          { name: '本地玩乐', quantity: '907,353' },
          { name: '居家聚会', quantity: '1,253,462' },
          { name: '异地出行', quantity: '420,193' },
          { name: '礼赠关怀', quantity: '569,078' },
          { name: '周末补货', quantity: '400,026' },
          { name: '重物到家', quantity: '686,289' },
          { name: '职场共餐', quantity: '395,450' },
          { name: '买菜做饭', quantity: '222,110' },
          { name: '突发应急', quantity: '197,488' },
          { name: '家庭陪伴', quantity: '143,171' },
          { name: '夜间置物', quantity: '40,635' },
          { name: '甜蜜约会', quantity: '38,930' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '1,720,598',
            percentageChange: 'rise',
            percentage: '42.51%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '1,239,753',
            percentageChange: 'rise',
            percentage: '43.33%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '1,112,230',
            percentageChange: 'rise',
            percentage: '42.91%',
          },
          {
            name: '日间采购',
            cycleQuantity: ' 654,816',
            percentageChange: 'rise',
            percentage: '  21.08%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: ' 670,998',
            percentageChange: 'rise',
            percentage: '  35.22%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '1,003,815',
            percentageChange: 'rise',
            percentage: '24.87%',
          },
          {
            name: '异地出行',
            cycleQuantity: ' 340,341',
            percentageChange: 'rise',
            percentage: '  23.46%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: ' 454,082',
            percentageChange: 'rise',
            percentage: '  25.32%',
          },
          {
            name: '周末补货',
            cycleQuantity: ' 254,238',
            percentageChange: 'rise',
            percentage: '  57.34%',
          },
          {
            name: '重物到家',
            cycleQuantity: ' 428,878',
            percentageChange: 'rise',
            percentage: '  60.02%',
          },
          {
            name: '职场共餐',
            cycleQuantity: ' 314,082',
            percentageChange: 'rise',
            percentage: '  25.91%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: ' 183,633',
            percentageChange: 'rise',
            percentage: '  20.95%',
          },
          {
            name: '突发应急',
            cycleQuantity: ' 141,403',
            percentageChange: 'rise',
            percentage: '  39.66%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: ' 81,198',
            percentageChange: 'rise',
            percentage: '  76.32%',
          },
          {
            name: '夜间置物',
            cycleQuantity: ' 28,335',
            percentageChange: 'rise',
            percentage: '  43.41%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: ' 26,598',
            percentageChange: 'rise',
            percentage: '  46.36%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: '全域宫销联动高价值产品' }],
      },
    },
  },
  {
    id: '历史用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '历史用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1,226,058',
        cycleQuantity: '914,599',
        percentage: '34%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '老客复购率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客复购率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '53%',
        cycleQuantity: '47%',
        percentage: '12%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌老客' },
        ],
      },
    },
  },
  {
    id: '动销店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '23,888',
        cycleQuantity: '19,111',
        percentage: '25%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '店铺产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺产出',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '498.4',
        cycleQuantity: '453.3',
        percentage: '10%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '店铺动销率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺动销率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '9%',
        cycleQuantity: '7%',
        percentage: '18%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '272,383',
        cycleQuantity: '257,567',
        percentage: '6%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '平均SKU产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '平均SKU产出',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '313.6',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS活动机制' }],
      },
    },
  },
  {
    id: '动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '37,963',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '建议铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '333,108',
        cycleQuantity: '310,984',
        percentage: '7%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '未铺货门店数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未铺货门店数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '60,725',
        cycleQuantity: '53,417',
        percentage: '14%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '店',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '售罄SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '202,607',
        cycleQuantity: '189,449',
        percentage: '7%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '在售SKU数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '在售SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1,283,138',
        cycleQuantity: '1,241,474',
        percentage: '3%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '未动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未动销SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1,245,175',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '317,432',
        cycleQuantity: '184,149',
        percentage: '72%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '商端计划-下单订货' }, { text: 'BC一体化' }],
      },
    },
  },
];

export const meizhiyuanNodes: any[] = [
  {
    id: '十大人群',
    height: 405,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '十大人群',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '164,363' },
          { name: '在校学生', quantity: '3,561,296' },
          { name: '精致妈妈', quantity: '460,947' },
          { name: '都市银发', quantity: '322,039' },
          { name: '都市白领', quantity: '1,126,453' },
          { name: '都市中产', quantity: '6,688,205' },
          { name: '都市监领', quantity: '75,418' },
          { name: '小镇青年', quantity: '5,962,242' },
          { name: '小镇中老年', quantity: '1,832,877' },
          { name: '其他', quantity: '3,581,862' },
        ],
        textColor: '#00DA9D',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '销售额',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: 'GMV',
        headerColor: 'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
      },
      contentProps: {
        quantity: '6,650,955',
        cycleQuantity: '5,034,625',
        percentage: '32%',
        percentageChange: 'rise',
        contentBackgroundColor:
          'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
        textColor: 'white',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '订单数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '订单数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '755,951',
        cycleQuantity: '516,299',
        percentage: '46%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '347,717',
        cycleQuantity: '280,065',
        percentage: '24%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '262,313',
        cycleQuantity: '154,496',
        percentage: '70%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '交易用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '交易用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '610,030',
        cycleQuantity: '434,561',
        percentage: '40%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '人',
      },
    },
  },
  {
    id: '购买频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '购买频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.24',
        cycleQuantity: '1.19',
        percentage: '4%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '客单价',
    type: 'cardSelectorNode',
    position: { x: 300, y: 200 },
    data: {
      headerProps: {
        title: '客单价',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '7.27',
        cycleQuantity: '7.12',
        percentage: '2%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '新客客单价',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '9.61',
        cycleQuantity: '10.76',
        percentage: '-11%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '老客客单价',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '8.11',
        cycleQuantity: '8.56',
        percentage: '-5%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '新客频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.0',
        cycleQuantity: '1.0',
        percentage: '0%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '老客频次',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客频次',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1.56',
        cycleQuantity: '1.53',
        percentage: '2%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '次',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌忠诚客' },
        ],
      },
    },
  },
  {
    id: '场景数量',
    type: 'cardSelectorNode',
    position: { x: 300, y: 300 },
    data: {
      headerProps: {
        title: '场景数量',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.00',
        cycleQuantity: '1.00',
        percentage: '0%',
        percentageChange: 'stable',
        contentBackgroundColor: 'white',
        textColor: '##1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    type: 'cardSelectorNode',
    height: 824,
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景人数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '169,962' },
          { name: '能量早午餐', quantity: '103,611' },
          { name: '独享下午茶', quantity: '82,614' },
          { name: '本地玩乐', quantity: '54,324' },
          { name: '日间采购', quantity: '50,355' },
          { name: '居家聚会', quantity: '37,838' },
          { name: '异地出行', quantity: '35,893' },
          { name: '礼赠关怀', quantity: '29,831' },
          { name: '周末补货', quantity: '25,665' },
          { name: '重物到家', quantity: '24,808' },
          { name: '职场共餐', quantity: '19,042' },
          { name: '突发应急', quantity: '15,471' },
          { name: '买菜做饭', quantity: '12,733' },
          { name: '家庭陪伴', quantity: '10,151' },
          { name: '夜间置物', quantity: '4,096' },
          { name: '甜蜜约会', quantity: '3,907' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '110,111',
            percentageChange: 'rise',
            percentage: '54%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '72,413',
            percentageChange: 'rise',
            percentage: '43%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '59,265',
            percentageChange: 'rise',
            percentage: '39%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '37,847',
            percentageChange: 'rise',
            percentage: '44%',
          },
          {
            name: '日间采购',
            cycleQuantity: '40,021',
            percentageChange: 'rise',
            percentage: '26%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '31,497',
            percentageChange: 'rise',
            percentage: '20%',
          },
          {
            name: '异地出行',
            cycleQuantity: '25,657',
            percentageChange: 'rise',
            percentage: '40%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '21,832',
            percentageChange: 'rise',
            percentage: '37%',
          },
          {
            name: '周末补货',
            cycleQuantity: '16,257',
            percentageChange: 'rise',
            percentage: '58%',
          },
          {
            name: '重物到家',
            cycleQuantity: '13,781',
            percentageChange: 'rise',
            percentage: '80%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '14,317',
            percentageChange: 'rise',
            percentage: '33%',
          },
          {
            name: '突发应急',
            cycleQuantity: '10,981',
            percentageChange: 'rise',
            percentage: '41%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '10,426',
            percentageChange: 'rise',
            percentage: '22%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '6,062',
            percentageChange: 'rise',
            percentage: '67%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '2,483',
            percentageChange: 'rise',
            percentage: '65%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '2,526',
            percentageChange: 'rise',
            percentage: '55%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '场景花费',
    height: 824,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景花费',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '1,495,292' },
          { name: '能量早午餐', quantity: '992,700' },
          { name: '独享下午茶', quantity: '796,219' },
          { name: '本地玩乐', quantity: '574,674' },
          { name: '日间采购', quantity: '430,874' },
          { name: '居家聚会', quantity: '593,178' },
          { name: '异地出行', quantity: '304,476' },
          { name: '礼赠关怀', quantity: '320,820' },
          { name: '周末补货', quantity: '214,968' },
          { name: '重物到家', quantity: '291,854' },
          { name: '职场共餐', quantity: '236,418' },
          { name: '突发应急', quantity: '136,782' },
          { name: '买菜做饭', quantity: '102,176' },
          { name: '家庭陪伴', quantity: '83,517' },
          { name: '夜间置物', quantity: '31,431' },
          { name: '甜蜜约会', quantity: '27,260' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '992,99',
            percentageChange: 'rise',
            percentage: '50.58%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '734,50',
            percentageChange: 'rise',
            percentage: '35.15%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '595,47',
            percentageChange: 'rise',
            percentage: '33.71%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '428,048',
            percentageChange: 'rise',
            percentage: '34.25%',
          },
          {
            name: '日间采购',
            cycleQuantity: '350,841',
            percentageChange: 'rise',
            percentage: '22.81%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '622,318',
            percentageChange: 'rise',
            percentage: '-4.68%',
          },
          {
            name: '异地出行',
            cycleQuantity: '235,363',
            percentageChange: 'rise',
            percentage: '29.36%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '268,592',
            percentageChange: 'rise',
            percentage: '19.45%',
          },
          {
            name: '周末补货',
            cycleQuantity: '143,064',
            percentageChange: 'rise',
            percentage: '50.26%',
          },
          {
            name: '重物到家',
            cycleQuantity: '179,389',
            percentageChange: 'rise',
            percentage: '62.69%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '196,827',
            percentageChange: 'rise',
            percentage: '20.11%',
          },
          {
            name: '突发应急',
            cycleQuantity: '104,641',
            percentageChange: 'rise',
            percentage: '30.72%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '85,027',
            percentageChange: 'rise',
            percentage: '20.17%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '52,220',
            percentageChange: 'rise',
            percentage: '59.93%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '21,505',
            percentageChange: 'rise',
            percentage: '46.16%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '18,853',
            percentageChange: 'rise',
            percentage: '44.59%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: '全域宫销联动高价值产品' }],
      },
    },
  },
  {
    id: '历史用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '历史用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '610,030',
        cycleQuantity: '434,561',
        percentage: '40%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '老客复购率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客复购率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '43%',
        cycleQuantity: '36%',
        percentage: '21%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌老客' },
        ],
      },
    },
  },
  {
    id: '动销店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '14,985',
        cycleQuantity: '10,996',
        percentage: '36%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '店铺产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺产出',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '443.9',
        cycleQuantity: '457.9',
        percentage: '-3%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '店铺动销率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺动销率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '7%',
        cycleQuantity: '5%',
        percentage: '26%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '221,337',
        cycleQuantity: '204,762',
        percentage: '8%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '平均SKU产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '平均SKU产出',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '316.1',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS活动机制' }],
      },
    },
  },
  {
    id: '动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '21,038',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '建议铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '332,762',
        cycleQuantity: '300,317',
        percentage: '11%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '未铺货门店数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未铺货门店数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '111,425',
        cycleQuantity: '95,555',
        percentage: '17%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '店',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '售罄SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '173,571',
        cycleQuantity: '157,967',
        percentage: '10%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '在售SKU数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '在售SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1,011,487',
        cycleQuantity: '846,101',
        percentage: '20%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '未动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未动销SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '990,449',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '185,746',
        cycleQuantity: '175,452',
        percentage: '6%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '商端计划-下单订货' }, { text: 'BC一体化' }],
      },
    },
  },
];
export const fendaNodes: any[] = [
  {
    id: '十大人群',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    height: 405,
    data: {
      headerProps: {
        title: '十大人群',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '177,479' },
          { name: '在校学生', quantity: '3,438,342' },
          { name: '精致妈妈', quantity: '500,096' },
          { name: '都市银发', quantity: '354,253' },
          { name: '都市白领', quantity: '1,239,171' },
          { name: '都市中产', quantity: '7,201,369' },
          { name: '都市监领', quantity: '84,940' },
          { name: '小镇青年', quantity: '6,495,714' },
          { name: '小镇中老年', quantity: '2,116,969' },
          { name: '其他', quantity: '3,484,361' },
        ],
        textColor: '#00DA9D',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '销售额',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: 'GMV',
        headerColor: 'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
      },
      contentProps: {
        quantity: '6,082,985',
        cycleQuantity: '4,329,629',
        percentage: '40%',
        percentageChange: 'rise',
        contentBackgroundColor:
          'linear-gradient( 136deg, #45B3FD 0%, #0CE8FD 100%)',
        textColor: 'white',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '订单数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '订单数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '837,230',
        cycleQuantity: '608,058',
        percentage: '38%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '310,936',
        cycleQuantity: '260,070',
        percentage: '20%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '336,848',
        cycleQuantity: '232,492',
        percentage: '45%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '交易用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '交易用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '647,784',
        cycleQuantity: '492,562',
        percentage: '32%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '人',
      },
    },
  },
  {
    id: '购买频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '购买频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.29',
        cycleQuantity: '1.23',
        percentage: '5%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '客单价',
    type: 'cardSelectorNode',
    position: { x: 300, y: 200 },
    data: {
      headerProps: {
        title: '客单价',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '9.39',
        cycleQuantity: '8.79',
        percentage: '7%',
        percentageChange: 'rise',
        contentBackgroundColor: 'white',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '元',
      },
    },
  },
  {
    id: '新客客单价',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '7.54',
        cycleQuantity: '7.33',
        percentage: '3%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '老客客单价',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客客单价',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '7.11',
        cycleQuantity: '6.96',
        percentage: '2%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '元',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS-活动机制' }],
      },
    },
  },
  {
    id: '新客频次',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '新客频次',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '1.0',
        cycleQuantity: '1.0',
        percentage: '0%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '次',
      },
    },
  },
  {
    id: '老客频次',
    height: 309,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客频次',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '1.56',
        cycleQuantity: '1.50',
        percentage: '4%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
        unit: '次',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌忠诚客' },
        ],
      },
    },
  },
  {
    id: '场景数量',
    type: 'cardSelectorNode',
    position: { x: 300, y: 300 },
    data: {
      headerProps: {
        title: '场景数量',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '1.00',
        cycleQuantity: '1.00',
        percentage: '0%',
        percentageChange: 'stable',
        contentBackgroundColor: 'white',
        textColor: '##1873FF',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    type: 'cardSelectorNode',
    height: 824,
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景人数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '184,822' },
          { name: '能量早午餐', quantity: '110,182' },
          { name: '独享下午茶', quantity: '92,405' },
          { name: '日间采购', quantity: '60,145' },
          { name: '居家聚会', quantity: '57,152' },
          { name: '本地玩乐', quantity: '49,622' },
          { name: '异地出行', quantity: '31,961' },
          { name: '周末补货', quantity: '30,134' },
          { name: '礼赠关怀', quantity: '29,328' },
          { name: '重物到家', quantity: '26,639' },
          { name: '职场共餐', quantity: '18,983' },
          { name: '买菜做饭', quantity: '17,050' },
          { name: '突发应急', quantity: '14,568' },
          { name: '家庭陪伴', quantity: '11,023' },
          { name: '夜间置物', quantity: '4,038' },
          { name: '甜蜜约会', quantity: '3,386' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '135,747',
            percentageChange: 'rise',
            percentage: '36%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '78,952',
            percentageChange: 'rise',
            percentage: '40%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '65,548',
            percentageChange: 'rise',
            percentage: '41%',
          },
          {
            name: '日间采购',
            cycleQuantity: '50,164',
            percentageChange: 'rise',
            percentage: '20%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '44,881',
            percentageChange: 'rise',
            percentage: '27%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '38,589',
            percentageChange: 'rise',
            percentage: '29%',
          },
          {
            name: '异地出行',
            cycleQuantity: '26,805',
            percentageChange: 'rise',
            percentage: '19%',
          },
          {
            name: '周末补货',
            cycleQuantity: '19,126',
            percentageChange: 'rise',
            percentage: '58%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '23,835',
            percentageChange: 'rise',
            percentage: '23%',
          },
          {
            name: '重物到家',
            cycleQuantity: '15,923',
            percentageChange: 'rise',
            percentage: '67%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '15,642',
            percentageChange: 'rise',
            percentage: '21%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '13,139',
            percentageChange: 'rise',
            percentage: '30%',
          },
          {
            name: '突发应急',
            cycleQuantity: '10,667',
            percentageChange: 'rise',
            percentage: '37%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '7,101',
            percentageChange: 'rise',
            percentage: '55%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '2,718',
            percentageChange: 'rise',
            percentage: '49%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '2,472',
            percentageChange: 'rise',
            percentage: '37%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '场景花费',
    height: 824,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '场景花费',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        baseData: [
          { name: '夜宵解馋', quantity: '1,229,984' },
          { name: '能量早午餐', quantity: '907,777' },
          { name: '独享下午茶', quantity: '759,038' },
          { name: '日间采购', quantity: '408,052 ' },
          { name: '居家聚会', quantity: '880,778 ' },
          { name: '本地玩乐', quantity: '440,739 ' },
          { name: '异地出行', quantity: '195,853 ' },
          { name: '周末补货', quantity: '193,738 ' },
          { name: '礼赠关怀', quantity: '260,886 ' },
          { name: '重物到家', quantity: '282,053 ' },
          { name: '职场共餐', quantity: '191,852 ' },
          { name: '买菜做饭', quantity: '107,918 ' },
          { name: '突发应急', quantity: '107,477 ' },
          { name: '家庭陪伴', quantity: '71,819 ' },
          { name: '夜间置物', quantity: '23,463 ' },
          { name: '甜蜜约会', quantity: '20,116 ' },
        ],
        cycleData: [
          {
            name: '夜宵解馋',
            cycleQuantity: '874,443',
            percentageChange: 'rise',
            percentage: '40.66%',
          },
          {
            name: '能量早午餐',
            cycleQuantity: '624,812',
            percentageChange: 'rise',
            percentage: '45.29%',
          },
          {
            name: '独享下午茶',
            cycleQuantity: '521,749',
            percentageChange: 'rise',
            percentage: '45.48%',
          },
          {
            name: '日间采购',
            cycleQuantity: '  337,428',
            percentageChange: 'rise',
            percentage: '20.93%',
          },
          {
            name: '居家聚会',
            cycleQuantity: '  574,052',
            percentageChange: 'rise',
            percentage: '53.43%',
          },
          {
            name: '本地玩乐',
            cycleQuantity: '  313,529',
            percentageChange: 'rise',
            percentage: '40.57%',
          },
          {
            name: '异地出行',
            cycleQuantity: '  163,355',
            percentageChange: 'rise',
            percentage: '19.89%',
          },
          {
            name: '周末补货',
            cycleQuantity: '  123,698',
            percentageChange: 'rise',
            percentage: '56.62%',
          },
          {
            name: '礼赠关怀',
            cycleQuantity: '  220,106',
            percentageChange: 'rise',
            percentage: '18.53%',
          },
          {
            name: '重物到家',
            cycleQuantity: '  181,370',
            percentageChange: 'rise',
            percentage: '55.51%',
          },
          {
            name: '职场共餐',
            cycleQuantity: '  152,295',
            percentageChange: 'rise',
            percentage: '25.97%',
          },
          {
            name: '买菜做饭',
            cycleQuantity: '  88,298',
            percentageChange: 'rise',
            percentage: '22.22%',
          },
          {
            name: '突发应急',
            cycleQuantity: '  73,964',
            percentageChange: 'rise',
            percentage: '45.31%',
          },
          {
            name: '家庭陪伴',
            cycleQuantity: '  48,778',
            percentageChange: 'rise',
            percentage: '47.24%',
          },
          {
            name: '夜间置物',
            cycleQuantity: '  18,374',
            percentageChange: 'rise',
            percentage: '27.70%',
          },
          {
            name: '甜蜜约会',
            cycleQuantity: '  13,379',
            percentageChange: 'rise',
            percentage: '50.36%',
          },
        ],
        textColor: '#38D6A1',
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: '全域宫销联动高价值产品' }],
      },
    },
  },
  {
    id: '历史用户数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '历史用户数',
        headerColor: 'linear-gradient( 130deg, #1871FF 0%, #0FBEFF 100%)',
      },
      contentProps: {
        quantity: '647,784',
        cycleQuantity: '492,562',
        percentage: '32%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1871FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '老客复购率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '老客复购率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '52%',
        cycleQuantity: '47%',
        percentage: '10%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#38D6A1',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '全域营销拓宽场景' },
          { text: '私欲运营提升粘性' },
          { text: 'RTB-品牌老客' },
        ],
      },
    },
  },
  {
    id: '动销店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '14,832',
        cycleQuantity: '11,131',
        percentage: '33%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '店铺产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺产出',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '410.1',
        cycleQuantity: '389.0',
        percentage: '5%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '',
      },
    },
  },
  {
    id: '店铺动销率',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '店铺动销率',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '7%',
        cycleQuantity: '6%',
        percentage: '25%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '207,435',
        cycleQuantity: '193,923',
        percentage: '7%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '平均SKU产出',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '平均SKU产出',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '262.2',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-选品机制' }, { text: 'TS活动机制' }],
      },
    },
  },
  {
    id: '动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '动销SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '23,198',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '建议铺货店铺数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '315,059',
        cycleQuantity: '296,284',
        percentage: '6%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '店',
      },
    },
  },
  {
    id: '未铺货门店数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未铺货门店数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '107,624',
        cycleQuantity: '102,361',
        percentage: '5%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '店',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '售罄SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU数',
        headerColor: 'linear-gradient( 115deg, #1871FF 0%, #0EBEFF 100%)',
      },
      contentProps: {
        quantity: '112,287',
        cycleQuantity: '121,296',
        percentage: '-7%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#1873FF',
        contentType: 'short',
        unit: '个',
      },
    },
  },
  {
    id: '在售SKU数',
    height: 261,
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '在售SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '820,212',
        cycleQuantity: '760,951',
        percentage: '8%',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '未动销SKU数',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '未动销SKU数',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '797,014',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    type: 'cardSelectorNode',
    position: { x: 100, y: 0 },
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
        headerColor: 'linear-gradient( 127deg, #2BE2D2 0%, #D4F8AC 100%)',
      },
      contentProps: {
        quantity: '195,354',
        cycleQuantity: '106,460',
        percentage: '84%',
        percentageChange: 'fall',
        contentBackgroundColor: '#fff',
        textColor: '#00DA9D',
        contentType: 'short',
        unit: '个',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '商端计划-下单订货' }, { text: 'BC一体化' }],
      },
    },
  },
];

// const getDataCode = (percentageChangeAugData) => {
//   return percentageChangeAugData.map((item, index) => {
//     const keys = Object.keys(item);
//     const values = Object.values(item);
//     const data = keys.map((key, index) => {
//       return {
//         id: key,
//         data: {
//           headerProps: {
//             title: key,
//           },
//           contentProps: {
//             quantity: values[index],
//             cycleQuantity: 'NA',
//             percentage: '-',
//             percentageChange: 'rise',
//             contentType: 'short',
//           },
//         },
//       };
//     });
//     return data;
//   });
// };
// const updateCycleQuantity = (data, cycleQuantityAugData) => {
//   // 遍历 data 数组
//   data.forEach((brandArray) => {
//     // 提取品牌和子品牌信息
//     const brand = brandArray.find((item) => item.id === '品牌').data
//       .contentProps.quantity;
//     const subBrand = brandArray.find((item) => item.id === '子品牌').data
//       .contentProps.quantity;

//     // 查找 cycleQuantityAugData 中对应的条目
//     const cycleData = cycleQuantityAugData.find(
//       (item) => item.品牌 === brand && item.子品牌 === subBrand,
//     );

//     // 如果找到对应的条目
//     if (cycleData) {
//       // 遍历 brandArray 更新 cycleQuantity 属性
//       brandArray.forEach((item) => {
//         const id = item.id; // 获取当前项的 id
//         if (cycleData[id] !== undefined) {
//           item.data.contentProps.cycleQuantity = cycleData[id]; // 更新 cycleQuantity 属性
//         }
//       });
//     }
//   });

//   return data;
// };
// const updatePercentage = (data, PercentageAugData) => {
//   // 遍历 data 数组
//   data.forEach((brandArray) => {
//     // 提取品牌和子品牌信息
//     const brand = brandArray.find((item) => item.id === '品牌').data
//       .contentProps.quantity;
//     const subBrand = brandArray.find((item) => item.id === '子品牌').data
//       .contentProps.quantity;

//     // 查找 cycleQuantityAugData 中对应的条目
//     const cycleData = PercentageAugData.find(
//       (item) => item.品牌 === brand && item.子品牌 === subBrand,
//     );

//     // 如果找到对应的条目
//     if (cycleData) {
//       // 遍历 brandArray 更新 cycleQuantity 属性
//       brandArray.forEach((item) => {
//         const id = item.id; // 获取当前项的 id
//         if (cycleData[id] !== undefined) {
//           item.data.contentProps.percentage = cycleData[id]; // 更新 cycleQuantity 属性
//         }
//       });
//     }
//   });

//   return data;
// };

// const leEdgesData = getDataCode(AugData);
// const leEdgesData2 = updateCycleQuantity(leEdgesData, cycleQuantityAugData);

// const leEdgesData3 = updatePercentage(leEdgesData2, percentageChangeAugData);
// console.log(
//   '%c [ leEdgesData3 ]-4143',
//   'font-size:13px; background:pink; color:#bf2c9f;',
//   leEdgesData3,
// );

export const leEdges: any[] = [
  {
    id: '16大场景频次',
    data: {
      headerProps: {
        title: '16大场景频次',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '1.2',
            '23年8月场景购买频次': '1.1',
            percentage: '2%',
          },
          {
            name: '能量早午餐',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '0%',
          },
          {
            name: '独享下午茶',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '0%',
          },
          {
            name: '日间采购',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '0%',
          },
          {
            name: '本地玩乐',
            quantity: '1.2',
            '23年8月场景购买频次': '1.2',
            percentage: '-1%',
          },
          {
            name: '居家聚会',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '-3%',
          },
          {
            name: '异地出行',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '0%',
          },
          {
            name: '礼赠关怀',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '周末补货',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '重物到家',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '买菜做饭',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '职场共餐',
            quantity: '1.1',
            '23年8月场景购买频次': '1.2',
            percentage: '-1%',
          },
          {
            name: '突发应急',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '0%',
          },
          {
            name: '家庭陪伴',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '夜间置物',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '甜蜜约会',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景单次购买金额',
    data: {
      headerProps: {
        title: '16大场景单次购买金额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '11.12',
            '23年8月场景单次购买金额': '11.63',
            percentage: '-4%',
          },
          {
            name: '能量早午餐',
            quantity: '13.47',
            '23年8月场景单次购买金额': '14.46',
            percentage: '-7%',
          },
          {
            name: '独享下午茶',
            quantity: '13.03',
            '23年8月场景单次购买金额': '13.77',
            percentage: '-5%',
          },
          {
            name: '日间采购',
            quantity: '12.57',
            '23年8月场景单次购买金额': '13.89',
            percentage: '-9%',
          },
          {
            name: '本地玩乐',
            quantity: '13.15',
            '23年8月场景单次购买金额': '17.58',
            percentage: '-25%',
          },
          {
            name: '居家聚会',
            quantity: '20.80',
            '23年8月场景单次购买金额': '25.99',
            percentage: '-20%',
          },
          {
            name: '异地出行',
            quantity: '11.09',
            '23年8月场景单次购买金额': '12.87',
            percentage: '-14%',
          },
          {
            name: '礼赠关怀',
            quantity: '14.33',
            '23年8月场景单次购买金额': '15.43',
            percentage: '-7%',
          },
          {
            name: '周末补货',
            quantity: '13.53',
            '23年8月场景单次购买金额': '13.58',
            percentage: '0%',
          },
          {
            name: '重物到家',
            quantity: '11.77',
            '23年8月场景单次购买金额': '12.26',
            percentage: '-4%',
          },
          {
            name: '买菜做饭',
            quantity: '11.66',
            '23年8月场景单次购买金额': '12.17',
            percentage: '-4%',
          },
          {
            name: '职场共餐',
            quantity: '14.85',
            '23年8月场景单次购买金额': '17.86',
            percentage: '-17%',
          },
          {
            name: '突发应急',
            quantity: '10.68',
            '23年8月场景单次购买金额': '11.60',
            percentage: '-8%',
          },
          {
            name: '家庭陪伴',
            quantity: '10.11',
            '23年8月场景单次购买金额': '11.03',
            percentage: '-8%',
          },
          {
            name: '夜间置物',
            quantity: '10.97',
            '23年8月场景单次购买金额': '12.58',
            percentage: '-13%',
          },
          {
            name: '甜蜜约会',
            quantity: '9.65',
            '23年8月场景单次购买金额': '11.31',
            percentage: '-15%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景订单数',
    data: {
      headerProps: {
        title: '16大场景订单数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '1,057,380',
            '23年8月场景订单数': '800,266',
            percentage: '32%',
          },
          {
            name: '能量早午餐',
            quantity: '782,941',
            '23年8月场景订单数': '654,622',
            percentage: '20%',
          },
          {
            name: '独享下午茶',
            quantity: '816,445',
            '23年8月场景订单数': '683,216',
            percentage: '20%',
          },
          {
            name: '日间采购',
            quantity: '455,355',
            '23年8月场景订单数': '398,678',
            percentage: '14%',
          },
          {
            name: '本地玩乐',
            quantity: '354,774',
            '23年8月场景订单数': '321,428',
            percentage: '10%',
          },
          {
            name: '居家聚会',
            quantity: '230,288',
            '23年8月场景订单数': '190,639',
            percentage: '21%',
          },
          {
            name: '异地出行',
            quantity: '401,554',
            '23年8月场景订单数': '314,136',
            percentage: '28%',
          },
          {
            name: '礼赠关怀',
            quantity: '352,112',
            '23年8月场景订单数': '286,092',
            percentage: '23%',
          },
          {
            name: '周末补货',
            quantity: '165,336',
            '23年8月场景订单数': '120,855',
            percentage: '37%',
          },
          {
            name: '重物到家',
            quantity: '96,887',
            '23年8月场景订单数': '71,677',
            percentage: '35%',
          },
          {
            name: '买菜做饭',
            quantity: '144,925',
            '23年8月场景订单数': '123,780',
            percentage: '17%',
          },
          {
            name: '职场共餐',
            quantity: '140,052',
            '23年8月场景订单数': '135,429',
            percentage: '3%',
          },
          {
            name: '突发应急',
            quantity: '152,735',
            '23年8月场景订单数': '128,841',
            percentage: '19%',
          },
          {
            name: '家庭陪伴',
            quantity: '127,371',
            '23年8月场景订单数': '82,171',
            percentage: '55%',
          },
          {
            name: '夜间置物',
            quantity: '61,996',
            '23年8月场景订单数': '47,934',
            percentage: '29%',
          },
          {
            name: '甜蜜约会',
            quantity: '33,196',
            '23年8月场景订单数': '24,182',
            percentage: '37%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景人数',
    data: {
      headerProps: {
        title: '16大场景人数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '907,979',
            '23年8月场景人数': '700,360',
            percentage: '30%',
          },
          {
            name: '能量早午餐',
            quantity: '712,940',
            '23年8月场景人数': '598,589',
            percentage: '19%',
          },
          {
            name: '独享下午茶',
            quantity: '755,227',
            '23年8月场景人数': '633,272',
            percentage: '19%',
          },
          {
            name: '日间采购',
            quantity: '433,063',
            '23年8月场景人数': '379,110',
            percentage: '14%',
          },
          {
            name: '本地玩乐',
            quantity: '302,227',
            '23年8月场景人数': '271,852',
            percentage: '11%',
          },
          {
            name: '居家聚会',
            quantity: '210,764',
            '23年8月场景人数': '169,207',
            percentage: '25%',
          },
          {
            name: '异地出行',
            quantity: '372,004',
            '23年8月场景人数': '291,580',
            percentage: '28%',
          },
          {
            name: '礼赠关怀',
            quantity: '332,563',
            '23年8月场景人数': '271,782',
            percentage: '22%',
          },
          {
            name: '周末补货',
            quantity: '161,450',
            '23年8月场景人数': '118,132',
            percentage: '37%',
          },
          {
            name: '重物到家',
            quantity: '93,385',
            '23年8月场景人数': '69,453',
            percentage: '34%',
          },
          {
            name: '买菜做饭',
            quantity: '141,091',
            '23年8月场景人数': '120,577',
            percentage: '17%',
          },
          {
            name: '职场共餐',
            quantity: '122,343',
            '23年8月场景人数': '117,514',
            percentage: '4%',
          },
          {
            name: '突发应急',
            quantity: '141,851',
            '23年8月场景人数': '120,208',
            percentage: '18%',
          },
          {
            name: '家庭陪伴',
            quantity: '121,605',
            '23年8月场景人数': '79,611',
            percentage: '53%',
          },
          {
            name: '夜间置物',
            quantity: '60,975',
            '23年8月场景人数': '47,251',
            percentage: '29%',
          },
          {
            name: '甜蜜约会',
            quantity: '31,818',
            '23年8月场景人数': '23,396',
            percentage: '36%',
          },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '16大场景销售额',
    data: {
      headerProps: {
        title: '16大场景销售额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '11,755,160',
            '23年8月场景GMV': '9,310,413',
            percentage: '26.26%',
          },
          {
            name: '能量早午餐',
            quantity: '10,549,682',
            '23年8月场景GMV': '9,463,419',
            percentage: '11.48%',
          },
          {
            name: '独享下午茶',
            quantity: '10,636,688',
            '23年8月场景GMV': '9,405,338',
            percentage: '13.09%',
          },
          {
            name: '日间采购',
            quantity: '5,723,325',
            '23年8月场景GMV': '5,536,152',
            percentage: '3.38%',
          },
          {
            name: '本地玩乐',
            quantity: '4,667,049',
            '23年8月场景GMV': '5,649,545',
            percentage: '-17.39%',
          },
          {
            name: '居家聚会',
            quantity: '4,790,179',
            '23年8月场景GMV': '4,955,460',
            percentage: '-3.34%',
          },
          {
            name: '异地出行',
            quantity: '4,454,746',
            '23年8月场景GMV': '4,043,346',
            percentage: '10.17%',
          },
          {
            name: '礼赠关怀',
            quantity: '5,044,276',
            '23年8月场景GMV': '4,415,519',
            percentage: '14.24%',
          },
          {
            name: '周末补货',
            quantity: '2,237,597',
            '23年8月场景GMV': '1,641,582',
            percentage: '36.31%',
          },
          {
            name: '重物到家',
            quantity: '1,139,958',
            '23年8月场景GMV': '878,901',
            percentage: '29.70%',
          },
          {
            name: '买菜做饭',
            quantity: '1,689,112',
            '23年8月场景GMV': '1,506,926',
            percentage: '12.09%',
          },
          {
            name: '职场共餐',
            quantity: '2,079,121',
            '23年8月场景GMV': '2,419,213',
            percentage: '-14.06%',
          },
          {
            name: '突发应急',
            quantity: '1,630,888',
            '23年8月场景GMV': '1,494,909',
            percentage: '9.10%',
          },
          {
            name: '家庭陪伴',
            quantity: '1,287,369',
            '23年8月场景GMV': '906,227',
            percentage: '42.06%',
          },
          {
            name: '夜间置物',
            quantity: '680,081',
            '23年8月场景GMV': '602,809',
            percentage: '12.82%',
          },
          {
            name: '甜蜜约会',
            quantity: '320,248',
            '23年8月场景GMV': '273,581',
            percentage: '17.06%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '十大人群人数',
    data: {
      headerProps: {
        title: '十大人群人数',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '1,081,300', percentage: '1.1%' },
          { name: '在校学生', quantity: '14,073,572', percentage: '14.4%' },
          { name: '精致妈妈', quantity: '2,724,358', percentage: '2.8%' },
          { name: '都市银发', quantity: '2,025,768', percentage: '2.1%' },
          { name: '都市白领', quantity: '4,955,589', percentage: '5.1%' },
          { name: '都市中产', quantity: '25,511,359', percentage: '26.1%' },
          { name: '都市监领', quantity: '481,466', percentage: '0.5%' },
          { name: '小镇青年', quantity: '22,007,678', percentage: '22.5%' },
          { name: '小镇中老年', quantity: '9,010,749', percentage: '9.2%' },
          { name: '其他', quantity: '15,840,745', percentage: '16.2%' },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '品牌',
    data: {
      headerProps: {
        title: '品牌',
      },
      contentProps: {
        quantity: '百事食品',
        cycleQuantity: '百事食品',
        percentage: '百事食品',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '年月',
    data: {
      headerProps: {
        title: '年月',
      },
      contentProps: {
        quantity: 'Aug-24',
        cycleQuantity: 'Aug-23',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '子品牌',
    data: {
      headerProps: {
        title: '子品牌',
      },
      contentProps: {
        quantity: '乐事',
        cycleQuantity: '乐事',
        percentage: '乐事',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '销售额',
    data: {
      headerProps: {
        title: '销售额',
      },
      contentProps: {
        quantity: '68,741,496',
        cycleQuantity: '62,546,341',
        percentage: '10%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客订单数',
    data: {
      headerProps: {
        title: '新客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: '',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客订单数',
    data: {
      headerProps: {
        title: '老客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '订单数',
    data: {
      headerProps: {
        title: '订单数',
      },
      contentProps: {
        quantity: '5,376,549',
        cycleQuantity: '4,386,143',
        percentage: '23%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景数量',
    data: {
      headerProps: {
        title: '场景数量',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    data: {
      headerProps: {
        title: '场景人数',
      },
      contentProps: {
        quantity: '1.1348',
        cycleQuantity: '1.1212',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景花费',
    data: {
      headerProps: {
        title: '场景花费',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    data: {
      headerProps: {
        title: '新客数',
      },
      contentProps: {
        quantity: '1,750,375',
        cycleQuantity: '1,647,299',
        percentage: '6%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    data: {
      headerProps: {
        title: '老客数',
      },
      contentProps: {
        quantity: '2,571,422',
        cycleQuantity: '1,932,508',
        percentage: '33%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    data: {
      headerProps: {
        title: '交易用户数',
      },
      contentProps: {
        quantity: '4,321,797',
        cycleQuantity: '3,579,807',
        percentage: '21%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '单次购买金额',
    data: {
      headerProps: {
        title: '单次购买金额',
      },
      contentProps: {
        quantity: '12.79',
        cycleQuantity: '14.26',
        percentage: '-10%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '购买频次',
    data: {
      headerProps: {
        title: '购买频次',
      },
      contentProps: {
        quantity: '1.24',
        cycleQuantity: '1.23',
        percentage: '2%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客人均花费',
    data: {
      headerProps: {
        title: '新客人均花费',
      },
      contentProps: {
        quantity: '13.15',
        cycleQuantity: '14.74',
        percentage: '-11%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客人均花费',
    data: {
      headerProps: {
        title: '老客人均花费',
      },
      contentProps: {
        quantity: '17.78',
        cycleQuantity: '19.73',
        percentage: '-10%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客销售额',
    data: {
      headerProps: {
        title: '新客销售额',
      },
      contentProps: {
        quantity: '23,009,151',
        cycleQuantity: '24,287,109',
        percentage: '-5%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客销售额',
    data: {
      headerProps: {
        title: '老客销售额',
      },
      contentProps: {
        quantity: '45,732,345',
        cycleQuantity: '38,259,232',
        percentage: '20%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客频次',
    data: {
      headerProps: {
        title: '新客频次',
      },
      contentProps: {
        quantity: '0.0',
        cycleQuantity: '0.0',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客频次',
    data: {
      headerProps: {
        title: '老客频次',
      },
      contentProps: {
        quantity: '0.00',
        cycleQuantity: '0.00',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '历史用户数',
    data: {
      headerProps: {
        title: '历史用户数',
      },
      contentProps: {
        quantity: '2,570,197',
        cycleQuantity: '1,922,589',
        percentage: '34%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客占比',
    data: {
      headerProps: {
        title: '老客占比',
      },
      contentProps: {
        quantity: '59%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '动销店铺数',
    data: {
      headerProps: {
        title: '动销店铺数',
      },
      contentProps: {
        quantity: '45,748',
        cycleQuantity: '40,481',
        percentage: '13%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺产出',
    data: {
      headerProps: {
        title: '店铺产出',
      },
      contentProps: {
        quantity: '1,502.6',
        cycleQuantity: '1,545.1',
        percentage: '-3%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    data: {
      headerProps: {
        title: '店铺动销率',
      },
      contentProps: {
        quantity: '19.63%',
        cycleQuantity: '17.63%',
        percentage: '11%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    data: {
      headerProps: {
        title: '铺货店铺数',
      },
      contentProps: {
        quantity: '233,053',
        cycleQuantity: '229,614',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU产出',
    data: {
      headerProps: {
        title: '日均动销SKU产出',
      },
      contentProps: {
        quantity: '12.79',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU数',
    data: {
      headerProps: {
        title: '日均动销SKU数',
      },
      contentProps: {
        quantity: '173,344 ',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均SKU动销率',
    data: {
      headerProps: {
        title: '日均SKU动销率',
      },
      contentProps: {
        quantity: '3.64%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    data: {
      headerProps: {
        title: '建议铺货店铺数',
      },
      contentProps: {
        quantity: '339,628',
        cycleQuantity: '334,125',
        percentage: '2%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺铺货率',
    data: {
      headerProps: {
        title: '店铺铺货率',
      },
      contentProps: {
        quantity: '68.62%',
        cycleQuantity: '68.72%',
        percentage: '0%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未铺货门店数',
    data: {
      headerProps: {
        title: '未铺货门店数',
      },
      contentProps: {
        quantity: '106,575',
        cycleQuantity: '104,511',
        percentage: '2%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU数',
    data: {
      headerProps: {
        title: '售罄SKU数',
      },
      contentProps: {
        quantity: '1,167,214',
        cycleQuantity: '1,136,455',
        percentage: '3%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '在售SKU数',
    data: {
      headerProps: {
        title: '在售SKU数',
      },
      contentProps: {
        quantity: '4,762,196',
        cycleQuantity: '5,123,782',
        percentage: '-7%',
        percentageChange: 'fall',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '在售SKU平均产出',
    data: {
      headerProps: {
        title: '在售SKU平均产出',
      },
      contentProps: {
        quantity: '14.43',
        cycleQuantity: '12.21',
        percentage: '18%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未动销SKU数据',
    data: {
      headerProps: {
        title: '未动销SKU数据',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
      },
      contentProps: {
        quantity: '3,933,235',
        cycleQuantity: '4,216,893',
        percentage: '-7%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
];

export const baiEdges: any[] = [
  {
    id: '16大场景频次',
    data: {
      headerProps: {
        title: '16大场景频次',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '能量早午餐',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '独享下午茶',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '日间采购',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '本地玩乐',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '居家聚会',
            quantity: '1.1',
            '23年8月场景购买频次': '1.0',
            percentage: '4%',
          },
          {
            name: '异地出行',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '礼赠关怀',
            quantity: '1.1',
            '23年8月场景购买频次': '1.0',
            percentage: '2%',
          },
          {
            name: '周末补货',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '重物到家',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '买菜做饭',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '职场共餐',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '突发应急',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '家庭陪伴',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '夜间置物',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '甜蜜约会',
            quantity: '1.0',
            '23年8月场景购买频次': '1.1',
            percentage: '-8%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景单次购买金额',
    data: {
      headerProps: {
        title: '16大场景单次购买金额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '13.31',
            '23年8月场景单次购买金额': '14.13',
            percentage: '-6%',
          },
          {
            name: '能量早午餐',
            quantity: '20.72',
            '23年8月场景单次购买金额': '19.15',
            percentage: '8%',
          },
          {
            name: '独享下午茶',
            quantity: '18.84',
            '23年8月场景单次购买金额': '16.87',
            percentage: '12%',
          },
          {
            name: '日间采购',
            quantity: '17.55',
            '23年8月场景单次购买金额': '17.43',
            percentage: '1%',
          },
          {
            name: '本地玩乐',
            quantity: '14.57',
            '23年8月场景单次购买金额': '15.39',
            percentage: '-5%',
          },
          {
            name: '居家聚会',
            quantity: '28.21',
            '23年8月场景单次购买金额': '19.39',
            percentage: '45%',
          },
          {
            name: '异地出行',
            quantity: '13.44',
            '23年8月场景单次购买金额': '14.31',
            percentage: '-6%',
          },
          {
            name: '礼赠关怀',
            quantity: '22.04',
            '23年8月场景单次购买金额': '18.28',
            percentage: '21%',
          },
          {
            name: '周末补货',
            quantity: '14.84',
            '23年8月场景单次购买金额': '14.90',
            percentage: '0%',
          },
          {
            name: '重物到家',
            quantity: '17.78',
            '23年8月场景单次购买金额': '17.70',
            percentage: '0%',
          },
          {
            name: '买菜做饭',
            quantity: '19.85',
            '23年8月场景单次购买金额': '18.60',
            percentage: '7%',
          },
          {
            name: '职场共餐',
            quantity: '14.66',
            '23年8月场景单次购买金额': '14.90',
            percentage: '-2%',
          },
          {
            name: '突发应急',
            quantity: '21.85',
            '23年8月场景单次购买金额': '18.83',
            percentage: '16%',
          },
          {
            name: '家庭陪伴',
            quantity: '14.99',
            '23年8月场景单次购买金额': '18.34',
            percentage: '-18%',
          },
          {
            name: '夜间置物',
            quantity: '12.64',
            '23年8月场景单次购买金额': '14.46',
            percentage: '-13%',
          },
          {
            name: '甜蜜约会',
            quantity: '16.17',
            '23年8月场景单次购买金额': '28.66',
            percentage: '-44%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景订单数',
    data: {
      headerProps: {
        title: '16大场景订单数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '47,651',
            '23年8月场景订单数': '40,728',
            percentage: '17%',
          },
          {
            name: '能量早午餐',
            quantity: '32,778',
            '23年8月场景订单数': '32,343',
            percentage: '1%',
          },
          {
            name: '独享下午茶',
            quantity: '30,964',
            '23年8月场景订单数': '30,232',
            percentage: '2%',
          },
          {
            name: '日间采购',
            quantity: '17,667',
            '23年8月场景订单数': '17,559',
            percentage: '1%',
          },
          {
            name: '本地玩乐',
            quantity: '18,102',
            '23年8月场景订单数': '18,494',
            percentage: '-2%',
          },
          {
            name: '居家聚会',
            quantity: '10,029',
            '23年8月场景订单数': '8,480',
            percentage: '18%',
          },
          {
            name: '异地出行',
            quantity: '20,860',
            '23年8月场景订单数': '17,477',
            percentage: '19%',
          },
          {
            name: '礼赠关怀',
            quantity: '29,689',
            '23年8月场景订单数': '25,703',
            percentage: '16%',
          },
          {
            name: '周末补货',
            quantity: '6,113',
            '23年8月场景订单数': '5,082',
            percentage: '20%',
          },
          {
            name: '重物到家',
            quantity: '3,911',
            '23年8月场景订单数': '3,674',
            percentage: '6%',
          },
          {
            name: '买菜做饭',
            quantity: '7,377',
            '23年8月场景订单数': '7,994',
            percentage: '-8%',
          },
          {
            name: '职场共餐',
            quantity: '9,472',
            '23年8月场景订单数': '9,342',
            percentage: '1%',
          },
          {
            name: '突发应急',
            quantity: '9,128',
            '23年8月场景订单数': '8,909',
            percentage: '2%',
          },
          {
            name: '家庭陪伴',
            quantity: '6,851',
            '23年8月场景订单数': '6,103',
            percentage: '12%',
          },
          {
            name: '夜间置物',
            quantity: '3,716',
            '23年8月场景订单数': '3,452',
            percentage: '8%',
          },
          {
            name: '甜蜜约会',
            quantity: '1,641',
            '23年8月场景订单数': '1,557',
            percentage: '5%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景人数',
    data: {
      headerProps: {
        title: '16大场景人数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '44,287',
            '23年8月场景人数': '38,323',
            percentage: '16%',
          },
          {
            name: '能量早午餐',
            quantity: '30,817',
            '23年8月场景人数': '30,705',
            percentage: '0%',
          },
          {
            name: '独享下午茶',
            quantity: '29,681',
            '23年8月场景人数': '29,299',
            percentage: '1%',
          },
          {
            name: '本地玩乐',
            quantity: '16,126',
            '23年8月场景人数': '16,210',
            percentage: '-1%',
          },
          {
            name: '日间采购',
            quantity: '17,678',
            '23年8月场景人数': '18,174',
            percentage: '-3%',
          },
          {
            name: '居家聚会',
            quantity: '9,342',
            '23年8月场景人数': '8,179',
            percentage: '14%',
          },
          {
            name: '异地出行',
            quantity: '20,134',
            '23年8月场景人数': '16,972',
            percentage: '19%',
          },
          {
            name: '礼赠关怀',
            quantity: '28,253',
            '23年8月场景人数': '25,064',
            percentage: '13%',
          },
          {
            name: '周末补货',
            quantity: '6,051',
            '23年8月场景人数': '5,045',
            percentage: '20%',
          },
          {
            name: '重物到家',
            quantity: '3,825',
            '23年8月场景人数': '3,611',
            percentage: '6%',
          },
          {
            name: '职场共餐',
            quantity: '6,733',
            '23年8月场景人数': '7,367',
            percentage: '-9%',
          },
          {
            name: '突发应急',
            quantity: '9,056',
            '23年8月场景人数': '8,974',
            percentage: '1%',
          },
          {
            name: '买菜做饭',
            quantity: '8,838',
            '23年8月场景人数': '8,749',
            percentage: '1%',
          },
          {
            name: '家庭陪伴',
            quantity: '6,664',
            '23年8月场景人数': '6,007',
            percentage: '11%',
          },
          {
            name: '夜间置物',
            quantity: '3,691',
            '23年8月场景人数': '3,419',
            percentage: '8%',
          },
          {
            name: '甜蜜约会',
            quantity: '1,602',
            '23年8月场景人数': '1,394',
            percentage: '15%',
          },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '16大场景销售额',
    data: {
      headerProps: {
        title: '16大场景销售额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '634,138',
            '23年8月场景GMV': '575,502',
            percentage: '10.19%',
          },
          {
            name: '能量早午餐',
            quantity: '679,134',
            '23年8月场景GMV': '619,468',
            percentage: '9.63%',
          },
          {
            name: '独享下午茶',
            quantity: '583,248',
            '23年8月场景GMV': '510,129',
            percentage: '14.33%',
          },
          {
            name: '本地玩乐',
            quantity: '310,120',
            '23年8月场景GMV': '306,130',
            percentage: '1.30%',
          },
          {
            name: '日间采购',
            quantity: '263,699',
            '23年8月场景GMV': '284,675',
            percentage: '-7.37%',
          },
          {
            name: '居家聚会',
            quantity: '282,913',
            '23年8月场景GMV': '164,465',
            percentage: '72.02%',
          },
          {
            name: '异地出行',
            quantity: '280,339',
            '23年8月场景GMV': '250,139',
            percentage: '12.07%',
          },
          {
            name: '礼赠关怀',
            quantity: '654,428',
            '23年8月场景GMV': '469,810',
            percentage: '39.30%',
          },
          {
            name: '周末补货',
            quantity: '90,739',
            '23年8月场景GMV': '75,743',
            percentage: '19.80%',
          },
          {
            name: '重物到家',
            quantity: '69,548',
            '23年8月场景GMV': '65,028',
            percentage: '6.95%',
          },
          {
            name: '职场共餐',
            quantity: '146,416',
            '23年8月场景GMV': '148,649',
            percentage: '-1.50%',
          },
          {
            name: '突发应急',
            quantity: '138,896',
            '23年8月场景GMV': '139,242',
            percentage: '-0.25%',
          },
          {
            name: '买菜做饭',
            quantity: '199,468',
            '23年8月场景GMV': '167,788',
            percentage: '18.88%',
          },
          {
            name: '家庭陪伴',
            quantity: '102,726',
            '23年8月场景GMV': '111,900',
            percentage: '-8.20%',
          },
          {
            name: '夜间置物',
            quantity: '46,957',
            '23年8月场景GMV': '49,919',
            percentage: '-5.93%',
          },
          {
            name: '甜蜜约会',
            quantity: '26,539',
            '23年8月场景GMV': '44,630',
            percentage: '-40.54%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '十大人群人数',
    data: {
      headerProps: {
        title: '十大人群人数',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '49,632', percentage: '0.7%' },
          { name: '在校学生', quantity: '1,181,612', percentage: '15.5%' },
          { name: '精致妈妈', quantity: '188,185', percentage: '2.5%' },
          { name: '都市银发', quantity: '102,042', percentage: '1.3%' },
          { name: '都市白领', quantity: '346,725', percentage: '4.5%' },
          { name: '都市中产', quantity: '2,228,675', percentage: '29.2%' },
          { name: '都市监领', quantity: '22,040', percentage: '0.3%' },
          { name: '小镇青年', quantity: '1,694,293', percentage: '22.2%' },
          { name: '小镇中老年', quantity: '572,126', percentage: '7.5%' },
          { name: '其他', quantity: '1,244,121', percentage: '16.3%' },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '品牌',
    data: {
      headerProps: {
        title: '品牌',
      },
      contentProps: {
        quantity: '百事食品',
        cycleQuantity: '百事食品',
        percentage: '百事食品',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '年月',
    data: {
      headerProps: {
        title: '年月',
      },
      contentProps: {
        quantity: 'Aug-24',
        cycleQuantity: 'Aug-23',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '子品牌',
    data: {
      headerProps: {
        title: '子品牌',
      },
      contentProps: {
        quantity: '百草味',
        cycleQuantity: '百草味',
        percentage: '百草味',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '销售额',
    data: {
      headerProps: {
        title: '销售额',
      },
      contentProps: {
        quantity: '4,559,803',
        cycleQuantity: '4,007,442',
        percentage: '14%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客订单数',
    data: {
      headerProps: {
        title: '新客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: '',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客订单数',
    data: {
      headerProps: {
        title: '老客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '订单数',
    data: {
      headerProps: {
        title: '订单数',
      },
      contentProps: {
        quantity: '257,719',
        cycleQuantity: '239,321',
        percentage: '8%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景数量',
    data: {
      headerProps: {
        title: '场景数量',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    data: {
      headerProps: {
        title: '场景人数',
      },
      contentProps: {
        quantity: '1.0582',
        cycleQuantity: '1.0505',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景花费',
    data: {
      headerProps: {
        title: '场景花费',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    data: {
      headerProps: {
        title: '新客数',
      },
      contentProps: {
        quantity: '183,321',
        cycleQuantity: 'NAN',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    data: {
      headerProps: {
        title: '老客数',
      },
      contentProps: {
        quantity: '47,725',
        cycleQuantity: '-',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    data: {
      headerProps: {
        title: '交易用户数',
      },
      contentProps: {
        quantity: '231,046',
        cycleQuantity: '218,582',
        percentage: '6%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '单次购买金额',
    data: {
      headerProps: {
        title: '单次购买金额',
      },
      contentProps: {
        quantity: '17.69',
        cycleQuantity: '16.75',
        percentage: '6%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '购买频次',
    data: {
      headerProps: {
        title: '购买频次',
      },
      contentProps: {
        quantity: '1.12',
        cycleQuantity: '1.09',
        percentage: '2%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客人均花费',
    data: {
      headerProps: {
        title: '新客人均花费',
      },
      contentProps: {
        quantity: '17.91',
        cycleQuantity: '22.21',
        percentage: '-19%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客人均花费',
    data: {
      headerProps: {
        title: '老客人均花费',
      },
      contentProps: {
        quantity: '26.73',
        cycleQuantity: '33.46',
        percentage: '-20%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客销售额',
    data: {
      headerProps: {
        title: '新客销售额',
      },
      contentProps: {
        quantity: '3,284,001',
        cycleQuantity: '1,968,069',
        percentage: '67%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客销售额',
    data: {
      headerProps: {
        title: '老客销售额',
      },
      contentProps: {
        quantity: '1,275,802',
        cycleQuantity: '2,039,373',
        percentage: '-37%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客频次',
    data: {
      headerProps: {
        title: '新客频次',
      },
      contentProps: {
        quantity: '0.0',
        cycleQuantity: '#DIV/0!',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客频次',
    data: {
      headerProps: {
        title: '老客频次',
      },
      contentProps: {
        quantity: '0.00',
        cycleQuantity: '#DIV/0!',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '历史用户数',
    data: {
      headerProps: {
        title: '历史用户数',
      },
      contentProps: {
        quantity: '46,108',
        cycleQuantity: '',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客占比',
    data: {
      headerProps: {
        title: '老客占比',
      },
      contentProps: {
        quantity: '21%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '动销店铺数',
    data: {
      headerProps: {
        title: '动销店铺数',
      },
      contentProps: {
        quantity: '5,809',
        cycleQuantity: '5,237',
        percentage: '11%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺产出',
    data: {
      headerProps: {
        title: '店铺产出',
      },
      contentProps: {
        quantity: '784.9',
        cycleQuantity: '765.2',
        percentage: '3%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    data: {
      headerProps: {
        title: '店铺动销率',
      },
      contentProps: {
        quantity: '5.66%',
        cycleQuantity: '6.64%',
        percentage: '-15%',
        percentageChange: 'fall',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    data: {
      headerProps: {
        title: '铺货店铺数',
      },
      contentProps: {
        quantity: '102,638',
        cycleQuantity: '78,869',
        percentage: '30%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU产出',
    data: {
      headerProps: {
        title: '日均动销SKU产出',
      },
      contentProps: {
        quantity: '19.13',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU数',
    data: {
      headerProps: {
        title: '日均动销SKU数',
      },
      contentProps: {
        quantity: '7,690',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均SKU动销率',
    data: {
      headerProps: {
        title: '日均SKU动销率',
      },
      contentProps: {
        quantity: '1.39%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    data: {
      headerProps: {
        title: '建议铺货店铺数',
      },
      contentProps: {
        quantity: '392,019',
        cycleQuantity: '375,710',
        percentage: '4%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺铺货率',
    data: {
      headerProps: {
        title: '店铺铺货率',
      },
      contentProps: {
        quantity: '26.18%',
        cycleQuantity: '20.99%',
        percentage: '25%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未铺货门店数',
    data: {
      headerProps: {
        title: '未铺货门店数',
      },
      contentProps: {
        quantity: '289,381',
        cycleQuantity: '296,841',
        percentage: '-3%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU数',
    data: {
      headerProps: {
        title: '售罄SKU数',
      },
      contentProps: {
        quantity: '296,521',
        cycleQuantity: '202,497',
        percentage: '46%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '在售SKU数',
    data: {
      headerProps: {
        title: '在售SKU数',
      },
      contentProps: {
        quantity: '553,211',
        cycleQuantity: '550,115',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '在售SKU平均产出',
    data: {
      headerProps: {
        title: '在售SKU平均产出',
      },
      contentProps: {
        quantity: '8.24',
        cycleQuantity: '7.28',
        percentage: '13%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未动销SKU数据',
    data: {
      headerProps: {
        title: '未动销SKU数据',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
      },
      contentProps: {
        quantity: '331,080',
        cycleQuantity: '256,438',
        percentage: '29%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
];

export const guiEdges: any[] = [
  {
    id: '16大场景频次',
    data: {
      headerProps: {
        title: '16大场景频次',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '能量早午餐',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '1%',
          },
          {
            name: '独享下午茶',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '日间采购',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '-1%',
          },
          {
            name: '本地玩乐',
            quantity: '1.2',
            '23年8月场景购买频次': '1.1',
            percentage: '9%',
          },
          {
            name: '居家聚会',
            quantity: '1.1',
            '23年8月场景购买频次': '1.2',
            percentage: '-4%',
          },
          {
            name: '异地出行',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '礼赠关怀',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '周末补货',
            quantity: '1.1',
            '23年8月场景购买频次': '1.0',
            percentage: '2%',
          },
          {
            name: '重物到家',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '-1%',
          },
          {
            name: '买菜做饭',
            quantity: '1.1',
            '23年8月场景购买频次': '1.1',
            percentage: '3%',
          },
          {
            name: '职场共餐',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '突发应急',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '家庭陪伴',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '1%',
          },
          {
            name: '夜间置物',
            quantity: '1.0',
            '23年8月场景购买频次': '1.0',
            percentage: '0%',
          },
          {
            name: '甜蜜约会',
            quantity: '1.0',
            '23年8月场景购买频次': '1.1',
            percentage: '-12%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景单次购买金额',
    data: {
      headerProps: {
        title: '16大场景单次购买金额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '37.06',
            '23年8月场景单次购买金额': '36.68',
            percentage: '1%',
          },
          {
            name: '能量早午餐',
            quantity: '44.50',
            '23年8月场景单次购买金额': '40.57',
            percentage: '10%',
          },
          {
            name: '独享下午茶',
            quantity: '46.55',
            '23年8月场景单次购买金额': '42.38',
            percentage: '10%',
          },
          {
            name: '日间采购',
            quantity: '37.22',
            '23年8月场景单次购买金额': '35.18',
            percentage: '6%',
          },
          {
            name: '本地玩乐',
            quantity: '114.99',
            '23年8月场景单次购买金额': '103.03',
            percentage: '12%',
          },
          {
            name: '居家聚会',
            quantity: '64.46',
            '23年8月场景单次购买金额': '78.44',
            percentage: '-18%',
          },
          {
            name: '异地出行',
            quantity: '32.89',
            '23年8月场景单次购买金额': '29.15',
            percentage: '13%',
          },
          {
            name: '礼赠关怀',
            quantity: '40.60',
            '23年8月场景单次购买金额': '37.52',
            percentage: '8%',
          },
          {
            name: '周末补货',
            quantity: '48.83',
            '23年8月场景单次购买金额': '53.69',
            percentage: '-9%',
          },
          {
            name: '重物到家',
            quantity: '55.41',
            '23年8月场景单次购买金额': '60.41',
            percentage: '-8%',
          },
          {
            name: '买菜做饭',
            quantity: '59.49',
            '23年8月场景单次购买金额': '57.40',
            percentage: '4%',
          },
          {
            name: '职场共餐',
            quantity: '30.85',
            '23年8月场景单次购买金额': '31.05',
            percentage: '-1%',
          },
          {
            name: '突发应急',
            quantity: '35.80',
            '23年8月场景单次购买金额': '32.17',
            percentage: '11%',
          },
          {
            name: '家庭陪伴',
            quantity: '34.83',
            '23年8月场景单次购买金额': '33.54',
            percentage: '4%',
          },
          {
            name: '夜间置物',
            quantity: '27.71',
            '23年8月场景单次购买金额': '29.96',
            percentage: '-8%',
          },
          {
            name: '甜蜜约会',
            quantity: '34.58',
            '23年8月场景单次购买金额': '38.69',
            percentage: '-11%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景订单数',
    data: {
      headerProps: {
        title: '16大场景订单数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '3,078',
            '23年8月场景订单数': '2,280',
            percentage: '35%',
          },
          {
            name: '能量早午餐',
            quantity: '8,874',
            '23年8月场景订单数': '6,999',
            percentage: '27%',
          },
          {
            name: '独享下午茶',
            quantity: '5,633',
            '23年8月场景订单数': '4,160',
            percentage: '35%',
          },
          {
            name: '日间采购',
            quantity: '1,839',
            '23年8月场景订单数': '1,678',
            percentage: '10%',
          },
          {
            name: '本地玩乐',
            quantity: '1,179',
            '23年8月场景订单数': '789',
            percentage: '49%',
          },
          {
            name: '居家聚会',
            quantity: '2,436',
            '23年8月场景订单数': '2,345',
            percentage: '4%',
          },
          {
            name: '异地出行',
            quantity: '809',
            '23年8月场景订单数': '665',
            percentage: '22%',
          },
          {
            name: '礼赠关怀',
            quantity: '604',
            '23年8月场景订单数': '481',
            percentage: '26%',
          },
          {
            name: '周末补货',
            quantity: '3,695',
            '23年8月场景订单数': '2,554',
            percentage: '45%',
          },
          {
            name: '重物到家',
            quantity: '1,245',
            '23年8月场景订单数': '864',
            percentage: '44%',
          },
          {
            name: '买菜做饭',
            quantity: '944',
            '23年8月场景订单数': '777',
            percentage: '21%',
          },
          {
            name: '职场共餐',
            quantity: '1,744',
            '23年8月场景订单数': '1,357',
            percentage: '29%',
          },
          {
            name: '突发应急',
            quantity: '1,647',
            '23年8月场景订单数': '1,321',
            percentage: '25%',
          },
          {
            name: '家庭陪伴',
            quantity: '1,313',
            '23年8月场景订单数': '960',
            percentage: '37%',
          },
          {
            name: '夜间置物',
            quantity: '161',
            '23年8月场景订单数': '109',
            percentage: '48%',
          },
          {
            name: '甜蜜约会',
            quantity: '80',
            '23年8月场景订单数': '55',
            percentage: '45%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景销售额',
    data: {
      headerProps: {
        title: '16大场景销售额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '114,058',
            '23年8月场景GMV': '83,626',
            percentage: '36.39%',
          },
          {
            name: '能量早午餐',
            quantity: '394,894',
            '23年8月场景GMV': '283,915',
            percentage: '39.09%',
          },
          {
            name: '独享下午茶',
            quantity: '262,201',
            '23年8月场景GMV': '176,321',
            percentage: '48.71%',
          },
          {
            name: '日间采购',
            quantity: '68,444',
            '23年8月场景GMV': '59,037',
            percentage: '15.93%',
          },
          {
            name: '居家聚会',
            quantity: '135,570',
            '23年8月场景GMV': '81,292',
            percentage: '66.77%',
          },
          {
            name: '本地玩乐',
            quantity: '157,024',
            '23年8月场景GMV': '183,942',
            percentage: '-14.63%',
          },
          {
            name: '异地出行',
            quantity: '26,607',
            '23年8月场景GMV': '19,382',
            percentage: '37.28%',
          },
          {
            name: '周末补货',
            quantity: '24,520',
            '23年8月场景GMV': '18,046',
            percentage: '35.87%',
          },
          {
            name: '礼赠关怀',
            quantity: '180,421',
            '23年8月场景GMV': '137,115',
            percentage: '31.58%',
          },
          {
            name: '重物到家',
            quantity: '68,981',
            '23年8月场景GMV': '52,193',
            percentage: '32.17%',
          },
          {
            name: '职场共餐',
            quantity: '56,162',
            '23年8月场景GMV': '44,599',
            percentage: '25.93%',
          },
          {
            name: '买菜做饭',
            quantity: '53,811',
            '23年8月场景GMV': '42,135',
            percentage: '27.71%',
          },
          {
            name: '突发应急',
            quantity: '58,968',
            '23年8月场景GMV': '42,490',
            percentage: '38.78%',
          },
          {
            name: '家庭陪伴',
            quantity: '45,736',
            '23年8月场景GMV': '32,197',
            percentage: '42.05%',
          },
          {
            name: '夜间置物',
            quantity: '4,462',
            '23年8月场景GMV': '3,266',
            percentage: '36.62%',
          },
          {
            name: '甜蜜约会',
            quantity: '2,766',
            '23年8月场景GMV': '2,128',
            percentage: '29.98%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景人数',
    data: {
      headerProps: {
        title: '16大场景人数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: '2,978',
            '23年8月场景人数': '2,211',
            percentage: '35%',
          },
          {
            name: '能量早午餐',
            quantity: '8,354',
            '23年8月场景人数': '6,629',
            percentage: '26%',
          },
          {
            name: '独享下午茶',
            quantity: '5,378',
            '23年8月场景人数': '4,021',
            percentage: '34%',
          },
          {
            name: '日间采购',
            quantity: '1,818',
            '23年8月场景人数': '1,650',
            percentage: '10%',
          },
          {
            name: '居家聚会',
            quantity: '1,017',
            '23年8月场景人数': '742',
            percentage: '37%',
          },
          {
            name: '本地玩乐',
            quantity: '2,167',
            '23年8月场景人数': '2,010',
            percentage: '8%',
          },
          {
            name: '异地出行',
            quantity: '787',
            '23年8月场景人数': '652',
            percentage: '21%',
          },
          {
            name: '周末补货',
            quantity: '598',
            '23年8月场景人数': '478',
            percentage: '25%',
          },
          {
            name: '礼赠关怀',
            quantity: '3,498',
            '23年8月场景人数': '2,465',
            percentage: '42%',
          },
          {
            name: '重物到家',
            quantity: '1,223',
            '23年8月场景人数': '838',
            percentage: '46%',
          },
          {
            name: '职场共餐',
            quantity: '873',
            '23年8月场景人数': '739',
            percentage: '18%',
          },
          {
            name: '买菜做饭',
            quantity: '1,734',
            '23年8月场景人数': '1,345',
            percentage: '29%',
          },
          {
            name: '突发应急',
            quantity: '1,574',
            '23年8月场景人数': '1,272',
            percentage: '24%',
          },
          {
            name: '家庭陪伴',
            quantity: '1,289',
            '23年8月场景人数': '950',
            percentage: '36%',
          },
          {
            name: '夜间置物',
            quantity: '159',
            '23年8月场景人数': '108',
            percentage: '47%',
          },
          {
            name: '甜蜜约会',
            quantity: '79',
            '23年8月场景人数': '48',
            percentage: '65%',
          },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '十大人群人数',
    data: {
      headerProps: {
        title: '十大人群人数',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '50,194', percentage: '1.1%' },
          { name: '在校学生', quantity: '483,930', percentage: '10.7%' },
          { name: '精致妈妈', quantity: '171,714', percentage: '3.8%' },
          { name: '都市银发', quantity: '145,829', percentage: '3.2%' },
          { name: '都市白领', quantity: '201,454', percentage: '4.4%' },
          { name: '都市中产', quantity: '1,617,353', percentage: '35.7%' },
          { name: '都市监领', quantity: '19,580', percentage: '0.4%' },
          { name: '小镇青年', quantity: '799,213', percentage: '17.6%' },
          { name: '小镇中老年', quantity: '444,501', percentage: '9.8%' },
          { name: '其他', quantity: '598,280', percentage: '13.2%' },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '品牌',
    data: {
      headerProps: {
        title: '品牌',
      },
      contentProps: {
        quantity: '百事食品',
        cycleQuantity: '百事食品',
        percentage: '百事食品',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '年月',
    data: {
      headerProps: {
        title: '年月',
      },
      contentProps: {
        quantity: 'Aug-24',
        cycleQuantity: 'Aug-23',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '子品牌',
    data: {
      headerProps: {
        title: '子品牌',
      },
      contentProps: {
        quantity: '桂格',
        cycleQuantity: '桂格',
        percentage: '桂格',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '销售额',
    data: {
      headerProps: {
        title: '销售额',
      },
      contentProps: {
        quantity: '1,654,626',
        cycleQuantity: '1,261,686',
        percentage: '31%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客订单数',
    data: {
      headerProps: {
        title: '新客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: '',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客订单数',
    data: {
      headerProps: {
        title: '老客订单数',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '订单数',
    data: {
      headerProps: {
        title: '订单数',
      },
      contentProps: {
        quantity: '35,281',
        cycleQuantity: '27,394',
        percentage: '29%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景数量',
    data: {
      headerProps: {
        title: '场景数量',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    data: {
      headerProps: {
        title: '场景人数',
      },
      contentProps: {
        quantity: '1.0436',
        cycleQuantity: '1.0375',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景花费',
    data: {
      headerProps: {
        title: '场景花费',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    data: {
      headerProps: {
        title: '新客数',
      },
      contentProps: {
        quantity: '24,466',
        cycleQuantity: '20,070',
        percentage: '22%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    data: {
      headerProps: {
        title: '老客数',
      },
      contentProps: {
        quantity: '7,662',
        cycleQuantity: '5,141',
        percentage: '49%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    data: {
      headerProps: {
        title: '交易用户数',
      },
      contentProps: {
        quantity: '32,128',
        cycleQuantity: '25,211',
        percentage: '27%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '单次购买金额',
    data: {
      headerProps: {
        title: '单次购买金额',
      },
      contentProps: {
        quantity: '46.90',
        cycleQuantity: '46.06',
        percentage: '2%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '购买频次',
    data: {
      headerProps: {
        title: '购买频次',
      },
      contentProps: {
        quantity: '1.10',
        cycleQuantity: '1.09',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客人均花费',
    data: {
      headerProps: {
        title: '新客人均花费',
      },
      contentProps: {
        quantity: '42.93',
        cycleQuantity: '46.18',
        percentage: '-7%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客人均花费',
    data: {
      headerProps: {
        title: '老客人均花费',
      },
      contentProps: {
        quantity: '78.88',
        cycleQuantity: '65.19',
        percentage: '21%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客销售额',
    data: {
      headerProps: {
        title: '新客销售额',
      },
      contentProps: {
        quantity: '1,050,217',
        cycleQuantity: '926,774',
        percentage: '13%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客销售额',
    data: {
      headerProps: {
        title: '老客销售额',
      },
      contentProps: {
        quantity: '604,409',
        cycleQuantity: '334,912',
        percentage: '80%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客频次',
    data: {
      headerProps: {
        title: '新客频次',
      },
      contentProps: {
        quantity: '0.0',
        cycleQuantity: '0.0',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客频次',
    data: {
      headerProps: {
        title: '老客频次',
      },
      contentProps: {
        quantity: '0.00',
        cycleQuantity: '0.00',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '历史用户数',
    data: {
      headerProps: {
        title: '历史用户数',
      },
      contentProps: {
        quantity: '7,662',
        cycleQuantity: '5,123',
        percentage: '50%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客占比',
    data: {
      headerProps: {
        title: '老客占比',
      },
      contentProps: {
        quantity: '24%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '动销店铺数',
    data: {
      headerProps: {
        title: '动销店铺数',
      },
      contentProps: {
        quantity: '888',
        cycleQuantity: '720',
        percentage: '23%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺产出',
    data: {
      headerProps: {
        title: '店铺产出',
      },
      contentProps: {
        quantity: '1,864.4',
        cycleQuantity: '1,752.4',
        percentage: '6%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    data: {
      headerProps: {
        title: '店铺动销率',
      },
      contentProps: {
        quantity: '1.85%',
        cycleQuantity: '1.58%',
        percentage: '17%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    data: {
      headerProps: {
        title: '铺货店铺数',
      },
      contentProps: {
        quantity: '47,973',
        cycleQuantity: '45,569',
        percentage: '5%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU产出',
    data: {
      headerProps: {
        title: '日均动销SKU产出',
      },
      contentProps: {
        quantity: '50.43',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU数',
    data: {
      headerProps: {
        title: '日均动销SKU数',
      },
      contentProps: {
        quantity: '1,058',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均SKU动销率',
    data: {
      headerProps: {
        title: '日均SKU动销率',
      },
      contentProps: {
        quantity: '0.66%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    data: {
      headerProps: {
        title: '建议铺货店铺数',
      },
      contentProps: {
        quantity: '182,936',
        cycleQuantity: '175,734',
        percentage: '4%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺铺货率',
    data: {
      headerProps: {
        title: '店铺铺货率',
      },
      contentProps: {
        quantity: '26.22%',
        cycleQuantity: '25.93%',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未铺货门店数',
    data: {
      headerProps: {
        title: '未铺货门店数',
      },
      contentProps: {
        quantity: '134,963',
        cycleQuantity: '130,165',
        percentage: '4%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU数',
    data: {
      headerProps: {
        title: '售罄SKU数',
      },
      contentProps: {
        quantity: '56,597',
        cycleQuantity: '49,879',
        percentage: '13%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '在售SKU数',
    data: {
      headerProps: {
        title: '在售SKU数',
      },
      contentProps: {
        quantity: '160,378',
        cycleQuantity: '222,576',
        percentage: '-28%',
        percentageChange: 'fall',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '在售SKU平均产出',
    data: {
      headerProps: {
        title: '在售SKU平均产出',
      },
      contentProps: {
        quantity: '10.32',
        cycleQuantity: '5.67',
        percentage: '82%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未动销SKU数据',
    data: {
      headerProps: {
        title: '未动销SKU数据',
      },
      contentProps: {
        quantity: '',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
      },
      contentProps: {
        quantity: '99,393',
        cycleQuantity: '55,595',
        percentage: '79%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
];

export const wanEdges: any[] = [
  {
    id: '16大场景频次',
    data: {
      headerProps: {
        title: '16大场景频次',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 1.1,
            '23年8月场景购买频次': 1.0,
            percentage: '1%',
          },
          {
            name: '能量早午餐',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '1%',
          },
          {
            name: '独享下午茶',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '0%',
          },
          {
            name: '日间采购',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
          {
            name: '本地玩乐',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '1%',
          },
          {
            name: '居家聚会',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
          {
            name: '异地出行',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '0%',
          },
          {
            name: '礼赠关怀',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '0%',
          },
          {
            name: '周末补货',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
          {
            name: '重物到家',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-0%',
          },
          {
            name: '买菜做饭',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '0%',
          },
          {
            name: '职场共餐',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-0%',
          },
          {
            name: '突发应急',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '2%',
          },
          {
            name: '家庭陪伴',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '0%',
          },
          {
            name: '夜间置物',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
          {
            name: '甜蜜约会',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景单次购买金额',
    data: {
      headerProps: {
        title: '16大场景单次购买金额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 43.04,
            '23年8月场景人数': 45.33,
            percentage: '-5%',
          },
          {
            name: '能量早午餐',
            quantity: 45.87,
            '23年8月场景人数': 50.06,
            percentage: '-8%',
          },
          {
            name: '独享下午茶',
            quantity: 44.3,
            '23年8月场景人数': 48.78,
            percentage: '-9%',
          },
          {
            name: '日间采购',
            quantity: 45.55,
            '23年8月场景人数': 50.65,
            percentage: '-10%',
          },
          {
            name: '本地玩乐',
            quantity: 50.04,
            '23年8月场景人数': 53.97,
            percentage: '-7%',
          },
          {
            name: '居家聚会',
            quantity: 42.17,
            '23年8月场景人数': 43.13,
            percentage: '-2%',
          },
          {
            name: '异地出行',
            quantity: 43.65,
            '23年8月场景人数': 45.85,
            percentage: '-5%',
          },
          {
            name: '礼赠关怀',
            quantity: 43.7,
            '23年8月场景人数': 49.09,
            percentage: '-11%',
          },
          {
            name: '周末补货',
            quantity: 48.62,
            '23年8月场景人数': 53.66,
            percentage: '-9%',
          },
          {
            name: '重物到家',
            quantity: 42.15,
            '23年8月场景人数': 46.93,
            percentage: '-10%',
          },
          {
            name: '买菜做饭',
            quantity: 39.54,
            '23年8月场景人数': 42.78,
            percentage: '-8%',
          },
          {
            name: '职场共餐',
            quantity: 54.87,
            '23年8月场景人数': 60.46,
            percentage: '-9%',
          },
          {
            name: '突发应急',
            quantity: 41.89,
            '23年8月场景人数': 45.32,
            percentage: '-8%',
          },
          {
            name: '家庭陪伴',
            quantity: 38.46,
            '23年8月场景人数': 42.48,
            percentage: '-9%',
          },
          {
            name: '夜间置物',
            quantity: 37.18,
            '23年8月场景人数': 41.56,
            percentage: '-11%',
          },
          {
            name: '甜蜜约会',
            quantity: 35.22,
            '23年8月场景人数': 43.04,
            percentage: '-18%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景订单数',
    data: {
      headerProps: {
        title: '16大场景订单数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 14429,
            '23年8月场景人数': 11098,
            percentage: '30%',
          },
          {
            name: '能量早午餐',
            quantity: 55390,
            '23年8月场景人数': 37892,
            percentage: '46%',
          },
          {
            name: '独享下午茶',
            quantity: 37288,
            '23年8月场景人数': 25915,
            percentage: '44%',
          },
          {
            name: '日间采购',
            quantity: 11348,
            '23年8月场景人数': 9411,
            percentage: '21%',
          },
          {
            name: '本地玩乐',
            quantity: 7238,
            '23年8月场景人数': 5728,
            percentage: '26%',
          },
          {
            name: '居家聚会',
            quantity: 1952,
            '23年8月场景人数': 1734,
            percentage: '13%',
          },
          {
            name: '异地出行',
            quantity: 2399,
            '23年8月场景人数': 1668,
            percentage: '44%',
          },
          {
            name: '礼赠关怀',
            quantity: 22182,
            '23年8月场景人数': 15462,
            percentage: '43%',
          },
          {
            name: '周末补货',
            quantity: 4012,
            '23年8月场景人数': 2777,
            percentage: '44%',
          },
          {
            name: '重物到家',
            quantity: 5564,
            '23年8月场景人数': 4106,
            percentage: '36%',
          },
          {
            name: '买菜做饭',
            quantity: 13390,
            '23年8月场景人数': 9726,
            percentage: '38%',
          },
          {
            name: '职场共餐',
            quantity: 3177,
            '23年8月场景人数': 3029,
            percentage: '5%',
          },
          {
            name: '突发应急',
            quantity: 1451,
            '23年8月场景人数': 1028,
            percentage: '41%',
          },
          {
            name: '家庭陪伴',
            quantity: 3506,
            '23年8月场景人数': 2252,
            percentage: '56%',
          },
          {
            name: '夜间置物',
            quantity: 374,
            '23年8月场景人数': 397,
            percentage: '-6%',
          },
          {
            name: '甜蜜约会',
            quantity: 181,
            '23年8月场景人数': 135,
            percentage: '34%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景销售额',
    data: {
      headerProps: {
        title: '16大场景销售额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 621061,
            '23年8月场景人数': 503082,
            percentage: '23.45%',
          },
          {
            name: '能量早午餐',
            quantity: 2540616,
            '23年8月场景人数': 1897041,
            percentage: '33.93%',
          },
          {
            name: '独享下午茶',
            quantity: 1651945,
            '23年8月场景人数': 1264218,
            percentage: '30.67%',
          },
          {
            name: '日间采购',
            quantity: 516882,
            '23年8月场景人数': 476688,
            percentage: '8.43%',
          },
          {
            name: '本地玩乐',
            quantity: 362175,
            '23年8月场景人数': 309165,
            percentage: '17.15%',
          },
          {
            name: '居家聚会',
            quantity: 82317,
            '23年8月场景人数': 74795,
            percentage: '10.06%',
          },
          {
            name: '异地出行',
            quantity: 104718,
            '23年8月场景人数': 76485,
            percentage: '36.91%',
          },
          {
            name: '礼赠关怀',
            quantity: 969336,
            '23年8月场景人数': 759088,
            percentage: '27.70%',
          },
          {
            name: '周末补货',
            quantity: 195048,
            '23年8月场景人数': 149019,
            percentage: '30.89%',
          },
          {
            name: '重物到家',
            quantity: 234506,
            '23年8月场景人数': 192698,
            percentage: '21.70%',
          },
          {
            name: '买菜做饭',
            quantity: 529403,
            '23年8月场景人数': 416034,
            percentage: '27.25%',
          },
          {
            name: '职场共餐',
            quantity: 174327,
            '23年8月场景人数': 183138,
            percentage: '-4.81%',
          },
          {
            name: '突发应急',
            quantity: 60784,
            '23年8月场景人数': 46589,
            percentage: '30.47%',
          },
          {
            name: '家庭陪伴',
            quantity: 134835,
            '23年8月场景人数': 95667,
            percentage: '40.94%',
          },
          {
            name: '夜间置物',
            quantity: 13904,
            '23年8月场景人数': 16501,
            percentage: '-15.74%',
          },
          {
            name: '甜蜜约会',
            quantity: 6375,
            '23年8月场景人数': 5810,
            percentage: '9.72%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景人数',
    data: {
      headerProps: {
        title: '16大场景人数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 13721,
            '23年8月场景人数': 10624,
            percentage: '29%',
          },
          {
            name: '能量早午餐',
            quantity: 50927,
            '23年8月场景人数': 35050,
            percentage: '45%',
          },
          {
            name: '独享下午茶',
            quantity: 35577,
            '23年8月场景人数': 24788,
            percentage: '44%',
          },
          {
            name: '日间采购',
            quantity: 11138,
            '23年8月场景人数': 9136,
            percentage: '22%',
          },
          {
            name: '本地玩乐',
            quantity: 6374,
            '23年8月场景人数': 5071,
            percentage: '26%',
          },
          {
            name: '居家聚会',
            quantity: 1903,
            '23年8月场景人数': 1676,
            percentage: '14%',
          },
          {
            name: '异地出行',
            quantity: 2263,
            '23年8月场景人数': 1577,
            percentage: '44%',
          },
          {
            name: '礼赠关怀',
            quantity: 21202,
            '23年8月场景人数': 14783,
            percentage: '43%',
          },
          {
            name: '周末补货',
            quantity: 3972,
            '23年8月场景人数': 2733,
            percentage: '45%',
          },
          {
            name: '重物到家',
            quantity: 5473,
            '23年8月场景人数': 4028,
            percentage: '36%',
          },
          {
            name: '买菜做饭',
            quantity: 13070,
            '23年8月场景人数': 9506,
            percentage: '37%',
          },
          {
            name: '职场共餐',
            quantity: 2832,
            '23年8月场景人数': 2689,
            percentage: '5%',
          },
          {
            name: '突发应急',
            quantity: 1340,
            '23年8月场景人数': 969,
            percentage: '38%',
          },
          {
            name: '家庭陪伴',
            quantity: 3435,
            '23年8月场景人数': 2208,
            percentage: '56%',
          },
          {
            name: '夜间置物',
            quantity: 369,
            '23年8月场景人数': 388,
            percentage: '-5%',
          },
          {
            name: '甜蜜约会',
            quantity: 180,
            '23年8月场景人数': 133,
            percentage: '35%',
          },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '十大人群人数',
    data: {
      headerProps: {
        title: '十大人群人数',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '122,369', percentage: '1.9%' },
          { name: '在校学生', quantity: '1,068,806', percentage: '9.3%' },
          { name: '精致妈妈', quantity: '517,931', percentage: '4.5%' },
          { name: '都市银发', quantity: '362,349', percentage: '3.1%' },
          { name: '都市白领', quantity: '540,004', percentage: '4.7%' },
          { name: '都市中产', quantity: '4,580,129', percentage: '39.7%' },
          { name: '都市监领', quantity: '42,321', percentage: '0.4%' },
          { name: '小镇青年', quantity: '1,859,155', percentage: '16.1%' },
          { name: '小镇中老年', quantity: '1,000,983', percentage: '8.7%' },
          { name: '其他', quantity: '1,454,140', percentage: '12.6%' },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '品牌',
    data: {
      headerProps: {
        title: '品牌',
      },
      contentProps: {
        quantity: '通用磨坊',
        cycleQuantity: '通用磨坊',
        percentage: '通用磨坊',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '年月',
    data: {
      headerProps: {
        title: '年月',
      },
      contentProps: {
        quantity: 'Aug-24',
        cycleQuantity: 'Aug-23',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '子品牌',
    data: {
      headerProps: {
        title: '子品牌',
      },
      contentProps: {
        quantity: '湾仔码头',
        cycleQuantity: '湾仔码头',
        percentage: '湾仔码头',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '销售额',
    data: {
      headerProps: {
        title: 'GMV',
      },
      contentProps: {
        quantity: '8,198,232',
        cycleQuantity: '6,494,812',
        percentage: '26%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '订单数',
    data: {
      headerProps: {
        title: '订单数',
      },
      contentProps: {
        quantity: '183,881',
        cycleQuantity: '132,768',
        percentage: '38%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景数量',
    data: {
      headerProps: {
        title: '场景数量',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    data: {
      headerProps: {
        title: '场景人数',
      },
      contentProps: {
        quantity: '1.0766',
        cycleQuantity: '1.0728',
        percentage: '0%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景花费',
    data: {
      headerProps: {
        title: '场景花费',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    data: {
      headerProps: {
        title: '新客数',
      },
      contentProps: {
        quantity: '89,640',
        cycleQuantity: '64,996',
        percentage: '38%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    data: {
      headerProps: {
        title: '老客数',
      },
      contentProps: {
        quantity: '71,760',
        cycleQuantity: '52,196',
        percentage: '37%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    data: {
      headerProps: {
        title: '交易用户数',
      },
      contentProps: {
        quantity: '161,400',
        cycleQuantity: '117,192',
        percentage: '38%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '单次购买金额',
    data: {
      headerProps: {
        title: '单次购买金额',
      },
      contentProps: {
        quantity: '44.58',
        cycleQuantity: '48.92',
        percentage: '-9%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '购买频次',
    data: {
      headerProps: {
        title: '购买频次',
      },
      contentProps: {
        quantity: '1.14',
        cycleQuantity: '1.13',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客人均花费',
    data: {
      headerProps: {
        title: '新客人均花费',
      },
      contentProps: {
        quantity: '44.58',
        cycleQuantity: '48.86',
        percentage: '-9%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客人均花费',
    data: {
      headerProps: {
        title: '老客人均花费',
      },
      contentProps: {
        quantity: '58.56',
        cycleQuantity: '63.59',
        percentage: '-8%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客销售额',
    data: {
      headerProps: {
        title: '新客销售额',
      },
      contentProps: {
        quantity: '3,996,273',
        cycleQuantity: '3,175,898',
        percentage: '26%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客销售额',
    data: {
      headerProps: {
        title: '老客销售额',
      },
      contentProps: {
        quantity: '4,201,959',
        cycleQuantity: '3,318,914',
        percentage: '27%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客占比',
    data: {
      headerProps: {
        title: '老客占比',
      },
      contentProps: {
        quantity: '44%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '动销店铺数',
    data: {
      headerProps: {
        title: '动销店铺数',
      },
      contentProps: {
        quantity: '3,444',
        cycleQuantity: '2,857',
        percentage: '21%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺产出',
    data: {
      headerProps: {
        title: '店铺产出',
      },
      contentProps: {
        quantity: '2,380.1',
        cycleQuantity: '2,273.4',
        percentage: '5%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    data: {
      headerProps: {
        title: '店铺动销率',
      },
      contentProps: {
        quantity: '4.72%',
        cycleQuantity: '3.95%',
        percentage: '19%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    data: {
      headerProps: {
        title: '铺货店铺数',
      },
      contentProps: {
        quantity: '72,976',
        cycleQuantity: '72,325',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU产出',
    data: {
      headerProps: {
        title: '日均动销SKU产出',
      },
      contentProps: {
        quantity: '39.98',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU数',
    data: {
      headerProps: {
        title: '日均动销SKU数',
      },
      contentProps: {
        quantity: '6,614 ',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均SKU动销率',
    data: {
      headerProps: {
        title: '日均SKU动销率',
      },
      contentProps: {
        quantity: '1.36%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    data: {
      headerProps: {
        title: '建议铺货店铺数',
      },
      contentProps: {
        quantity: '217,800',
        cycleQuantity: '219,045',
        percentage: '-1%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺铺货率',
    data: {
      headerProps: {
        title: '店铺铺货率',
      },
      contentProps: {
        quantity: '33.51%',
        cycleQuantity: '33.02%',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未铺货门店数',
    data: {
      headerProps: {
        title: '未铺货门店数',
      },
      contentProps: {
        quantity: '144,824',
        cycleQuantity: '146,720',
        percentage: '-1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU数',
    data: {
      headerProps: {
        title: '售罄SKU数',
      },
      contentProps: {
        quantity: '212,183',
        cycleQuantity: '208,342',
        percentage: '2%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '在售SKU数',
    data: {
      headerProps: {
        title: '在售SKU数',
      },
      contentProps: {
        quantity: '486,323',
        cycleQuantity: '564,153',
        percentage: '-14%',
        percentageChange: 'fall',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '在售SKU平均产出',
    data: {
      headerProps: {
        title: '在售SKU平均产出',
      },
      contentProps: {
        quantity: '16.86',
        cycleQuantity: '11.51',
        percentage: '46%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
      },
      contentProps: {
        quantity: '422,777',
        cycleQuantity: '366,421',
        percentage: '15%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
];
export const haEdges: any[] = [
  {
    id: '16大场景频次',
    data: {
      headerProps: {
        title: '16大场景频次',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '0%',
          },
          {
            name: '能量早午餐',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-3%',
          },
          {
            name: '独享下午茶',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-2%',
          },
          {
            name: '日间采购',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-1%',
          },
          {
            name: '本地玩乐',
            quantity: 1.1,
            '23年8月场景购买频次': 1.2,
            percentage: '-2%',
          },
          {
            name: '居家聚会',
            quantity: 1.0,
            '23年8月场景购买频次': 1.1,
            percentage: '-9%',
          },
          {
            name: '异地出行',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '0%',
          },
          {
            name: '礼赠关怀',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-3%',
          },
          {
            name: '周末补货',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-1%',
          },
          {
            name: '重物到家',
            quantity: 1.0,
            '23年8月场景购买频次': 1.1,
            percentage: '-5%',
          },
          {
            name: '职场共餐',
            quantity: 1.2,
            '23年8月场景购买频次': 1.2,
            percentage: '-2%',
          },
          {
            name: '买菜做饭',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '-0%',
          },
          {
            name: '突发应急',
            quantity: 1.1,
            '23年8月场景购买频次': 1.1,
            percentage: '-2%',
          },
          {
            name: '家庭陪伴',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '2%',
          },
          {
            name: '夜间置物',
            quantity: 1.0,
            '23年8月场景购买频次': 1.0,
            percentage: '0%',
          },
          {
            name: '甜蜜约会',
            quantity: 1.0,
            '23年8月场景购买频次': 1.2,
            percentage: '-18%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景单次购买金额',
    data: {
      headerProps: {
        title: '16大场景单次购买金额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 69.85,
            '23年8月场景人数': 68.31,
            percentage: '2%',
          },
          {
            name: '能量早午餐',
            quantity: 79.41,
            '23年8月场景人数': 76.67,
            percentage: '4%',
          },
          {
            name: '独享下午茶',
            quantity: 77.43,
            '23年8月场景人数': 75.61,
            percentage: '2%',
          },
          {
            name: '日间采购',
            quantity: 77.85,
            '23年8月场景人数': 75.55,
            percentage: '3%',
          },
          {
            name: '本地玩乐',
            quantity: 75.08,
            '23年8月场景人数': 74.58,
            percentage: '1%',
          },
          {
            name: '居家聚会',
            quantity: 73.52,
            '23年8月场景人数': 76.3,
            percentage: '-4%',
          },
          {
            name: '异地出行',
            quantity: 64.5,
            '23年8月场景人数': 60.72,
            percentage: '6%',
          },
          {
            name: '礼赠关怀',
            quantity: 101.69,
            '23年8月场景人数': 96.91,
            percentage: '5%',
          },
          {
            name: '周末补货',
            quantity: 79.06,
            '23年8月场景人数': 75.73,
            percentage: '4%',
          },
          {
            name: '重物到家',
            quantity: 70.08,
            '23年8月场景人数': 72.28,
            percentage: '-3%',
          },
          {
            name: '职场共餐',
            quantity: 104.59,
            '23年8月场景人数': 97.89,
            percentage: '7%',
          },
          {
            name: '买菜做饭',
            quantity: 66.97,
            '23年8月场景人数': 69.1,
            percentage: '-3%',
          },
          {
            name: '突发应急',
            quantity: 75.47,
            '23年8月场景人数': 70.96,
            percentage: '6%',
          },
          {
            name: '家庭陪伴',
            quantity: 66.06,
            '23年8月场景人数': 74.27,
            percentage: '-11%',
          },
          {
            name: '夜间置物',
            quantity: 58.06,
            '23年8月场景人数': 60.89,
            percentage: '-5%',
          },
          {
            name: '甜蜜约会',
            quantity: 53.51,
            '23年8月场景人数': 67.75,
            percentage: '-21%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景订单数',
    data: {
      headerProps: {
        title: '16大场景订单数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 10333,
            '23年8月场景人数': 8615,
            percentage: '20%',
          },
          {
            name: '能量早午餐',
            quantity: 11787,
            '23年8月场景人数': 8698,
            percentage: '36%',
          },
          {
            name: '独享下午茶',
            quantity: 13257,
            '23年8月场景人数': 9095,
            percentage: '46%',
          },
          {
            name: '日间采购',
            quantity: 7596,
            '23年8月场景人数': 5862,
            percentage: '30%',
          },
          {
            name: '本地玩乐',
            quantity: 4017,
            '23年8月场景人数': 3362,
            percentage: '19%',
          },
          {
            name: '居家聚会',
            quantity: 1193,
            '23年8月场景人数': 1106,
            percentage: '8%',
          },
          {
            name: '异地出行',
            quantity: 3099,
            '23年8月场景人数': 2277,
            percentage: '36%',
          },
          {
            name: '礼赠关怀',
            quantity: 7071,
            '23年8月场景人数': 5488,
            percentage: '29%',
          },
          {
            name: '周末补货',
            quantity: 3184,
            '23年8月场景人数': 1953,
            percentage: '63%',
          },
          {
            name: '重物到家',
            quantity: 758,
            '23年8月场景人数': 435,
            percentage: '74%',
          },
          {
            name: '职场共餐',
            quantity: 1681,
            '23年8月场景人数': 1501,
            percentage: '12%',
          },
          {
            name: '买菜做饭',
            quantity: 1326,
            '23年8月场景人数': 791,
            percentage: '68%',
          },
          {
            name: '突发应急',
            quantity: 1010,
            '23年8月场景人数': 754,
            percentage: '34%',
          },
          {
            name: '家庭陪伴',
            quantity: 752,
            '23年8月场景人数': 474,
            percentage: '59%',
          },
          {
            name: '夜间置物',
            quantity: 264,
            '23年8月场景人数': 253,
            percentage: '4%',
          },
          {
            name: '甜蜜约会',
            quantity: 172,
            '23年8月场景人数': 187,
            percentage: '-8%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景销售额',
    data: {
      headerProps: {
        title: '16大场景销售额',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 721711,
            '23年8月场景人数': 588462,
            percentage: '22.64%',
          },
          {
            name: '能量早午餐',
            quantity: 935973,
            '23年8月场景人数': 666892,
            percentage: '40.35%',
          },
          {
            name: '独享下午茶',
            quantity: 1026555,
            '23年8月场景人数': 687628,
            percentage: '49.29%',
          },
          {
            name: '日间采购',
            quantity: 591361,
            '23年8月场景人数': 442902,
            percentage: '33.52%',
          },
          {
            name: '本地玩乐',
            quantity: 301601,
            '23年8月场景人数': 250730,
            percentage: '20.29%',
          },
          {
            name: '居家聚会',
            quantity: 87712,
            '23年8月场景人数': 84383,
            percentage: '3.95%',
          },
          {
            name: '异地出行',
            quantity: 199898,
            '23年8月场景人数': 138262,
            percentage: '44.58%',
          },
          {
            name: '礼赠关怀',
            quantity: 719076,
            '23年8月场景人数': 531815,
            percentage: '35.21%',
          },
          {
            name: '周末补货',
            quantity: 251718,
            '23年8月场景人数': 147901,
            percentage: '70.19%',
          },
          {
            name: '重物到家',
            quantity: 53119,
            '23年8月场景人数': 31440,
            percentage: '68.95%',
          },
          {
            name: '职场共餐',
            quantity: 175812,
            '23年8月场景人数': 146933,
            percentage: '19.65%',
          },
          {
            name: '买菜做饭',
            quantity: 88797,
            '23年8月场景人数': 54659,
            percentage: '62.46%',
          },
          {
            name: '突发应急',
            quantity: 76224,
            '23年8月场景人数': 53506,
            percentage: '42.46%',
          },
          {
            name: '家庭陪伴',
            quantity: 49680,
            '23年8月场景人数': 35204,
            percentage: '41.12%',
          },
          {
            name: '夜间置物',
            quantity: 15327,
            '23年8月场景人数': 15404,
            percentage: '-0.50%',
          },
          {
            name: '甜蜜约会',
            quantity: 9203,
            '23年8月场景人数': 12670,
            percentage: '-27.36%',
          },
        ],
        contentType: 'long',
      },
    },
  },
  {
    id: '16大场景人数',
    data: {
      headerProps: {
        title: '16大场景人数',
      },
      contentProps: {
        baseData: [
          {
            name: '夜宵解馋',
            quantity: 9228,
            '23年8月场景人数': 7705,
            percentage: '20%',
          },
          {
            name: '能量早午餐',
            quantity: 10859,
            '23年8月场景人数': 7811,
            percentage: '39%',
          },
          {
            name: '独享下午茶',
            quantity: 12388,
            '23年8月场景人数': 8363,
            percentage: '48%',
          },
          {
            name: '日间采购',
            quantity: 7127,
            '23年8月场景人数': 5460,
            percentage: '31%',
          },
          {
            name: '本地玩乐',
            quantity: 3564,
            '23年8月场景人数': 2914,
            percentage: '22%',
          },
          {
            name: '居家聚会',
            quantity: 1139,
            '23年8月场景人数': 962,
            percentage: '18%',
          },
          {
            name: '异地出行',
            quantity: 2890,
            '23年8月场景人数': 2132,
            percentage: '36%',
          },
          {
            name: '礼赠关怀',
            quantity: 6565,
            '23年8月场景人数': 4946,
            percentage: '33%',
          },
          {
            name: '周末补货',
            quantity: 3077,
            '23年8月场景人数': 1867,
            percentage: '65%',
          },
          {
            name: '重物到家',
            quantity: 739,
            '23年8月场景人数': 405,
            percentage: '82%',
          },
          {
            name: '职场共餐',
            quantity: 1461,
            '23年8月场景人数': 1275,
            percentage: '15%',
          },
          {
            name: '买菜做饭',
            quantity: 1301,
            '23年8月场景人数': 775,
            percentage: '68%',
          },
          {
            name: '突发应急',
            quantity: 924,
            '23年8月场景人数': 679,
            percentage: '36%',
          },
          {
            name: '家庭陪伴',
            quantity: 720,
            '23年8月场景人数': 464,
            percentage: '55%',
          },
          {
            name: '夜间置物',
            quantity: 257,
            '23年8月场景人数': 247,
            percentage: '4%',
          },
          {
            name: '甜蜜约会',
            quantity: 172,
            '23年8月场景人数': 153,
            percentage: '12%',
          },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB-场景联动' }, { text: '全域管销建立心智' }],
      },
    },
  },
  {
    id: '十大人群人数',
    data: {
      headerProps: {
        title: '十大人群人数',
      },
      contentProps: {
        baseData: [
          { name: '中年大众', quantity: '71,511', percentage: '0.8%' },
          { name: '在校学生', quantity: '998,104', percentage: '11.8%' },
          { name: '精致妈妈', quantity: '334,916', percentage: '4.0%' },
          { name: '都市银发', quantity: '226,743', percentage: '2.7%' },
          { name: '都市白领', quantity: '324,593', percentage: '3.9%' },
          { name: '都市中产', quantity: '3,162,545', percentage: '37.5%' },
          { name: '都市监领', quantity: '23,244', percentage: '0.3%' },
          { name: '小镇青年', quantity: '1,404,059', percentage: '16.7%' },
          { name: '小镇中老年', quantity: '682,021', percentage: '8.1%' },
          { name: '其他', quantity: '1,202,060', percentage: '14.3%' },
        ],
        contentType: 'long',
      },
      footerProps: {
        buttons: [{ text: 'RTB-人群国选' }, { text: '全域管销' }],
      },
    },
  },
  {
    id: '品牌',
    data: {
      headerProps: {
        title: '品牌',
      },
      contentProps: {
        quantity: '通用磨坊',
        cycleQuantity: '通用磨坊',
        percentage: '通用磨坊',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '年月',
    data: {
      headerProps: {
        title: '年月',
      },
      contentProps: {
        quantity: 'Aug-24',
        cycleQuantity: 'Aug-23',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '子品牌',
    data: {
      headerProps: {
        title: '子品牌',
      },
      contentProps: {
        quantity: '哈根达斯',
        cycleQuantity: '哈根达斯',
        percentage: '哈根达斯',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '销售额',
    data: {
      headerProps: {
        title: '销售额',
      },
      contentProps: {
        quantity: '5,303,767',
        cycleQuantity: '4,470,335',
        percentage: '19%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '订单数',
    data: {
      headerProps: {
        title: '订单数',
      },
      contentProps: {
        quantity: '67,500',
        cycleQuantity: '57,188',
        percentage: '18%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景数量',
    data: {
      headerProps: {
        title: '场景数量',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景人数',
    data: {
      headerProps: {
        title: '场景人数',
      },
      contentProps: {
        quantity: '1.0863',
        cycleQuantity: '1.0912',
        percentage: '0%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '场景花费',
    data: {
      headerProps: {
        title: '场景花费',
      },
      contentProps: {
        quantity: '见场景人数数据',
        cycleQuantity: '见场景人数数据',
        percentage: '见场景人数数据',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客数',
    data: {
      headerProps: {
        title: '新客数',
      },
      contentProps: {
        quantity: '40,947',
        cycleQuantity: '32,065',
        percentage: '28%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [
          { text: '品牌渠道双会员联动' },
          { text: '全域营销建立心智' },
          { text: 'RTB-新客' },
        ],
      },
    },
  },
  {
    id: '老客数',
    data: {
      headerProps: {
        title: '老客数',
      },
      contentProps: {
        quantity: '16,499',
        cycleQuantity: '15,683',
        percentage: '5%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '交易用户数',
    data: {
      headerProps: {
        title: '交易用户数',
      },
      contentProps: {
        quantity: '57,446',
        cycleQuantity: '47,748',
        percentage: '20%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '单次购买金额',
    data: {
      headerProps: {
        title: '单次购买金额',
      },
      contentProps: {
        quantity: '78.57',
        cycleQuantity: '78.17',
        percentage: '1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '购买频次',
    data: {
      headerProps: {
        title: '购买频次',
      },
      contentProps: {
        quantity: '1.18',
        cycleQuantity: '1.20',
        percentage: '-2%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客人均花费',
    data: {
      headerProps: {
        title: '新客人均花费',
      },
      contentProps: {
        quantity: '79.34',
        cycleQuantity: '80.36',
        percentage: '-1%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客人均花费',
    data: {
      headerProps: {
        title: '老客人均花费',
      },
      contentProps: {
        quantity: '124.55',
        cycleQuantity: '120.74',
        percentage: '3%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '新客销售额',
    data: {
      headerProps: {
        title: '新客销售额',
      },
      contentProps: {
        quantity: '3,248,856',
        cycleQuantity: '2,576,823',
        percentage: '26%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客销售额',
    data: {
      headerProps: {
        title: '老客销售额',
      },
      contentProps: {
        quantity: '2,054,911',
        cycleQuantity: '1,893,512',
        percentage: '9%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '老客占比',
    data: {
      headerProps: {
        title: '老客占比',
      },
      contentProps: {
        quantity: '29%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '动销店铺数',
    data: {
      headerProps: {
        title: '动销店铺数',
      },
      contentProps: {
        quantity: '1,808',
        cycleQuantity: '1,478',
        percentage: '22%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺产出',
    data: {
      headerProps: {
        title: '店铺产出',
      },
      contentProps: {
        quantity: '2,932.9',
        cycleQuantity: '3,024.5',
        percentage: '-3%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺动销率',
    data: {
      headerProps: {
        title: '店铺动销率',
      },
      contentProps: {
        quantity: '3.69%',
        cycleQuantity: '3.0%',
        percentage: '23%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: 'RTB' }, { text: 'TS发劵' }, { text: '渠道私域' }],
      },
    },
  },
  {
    id: '铺货店铺数',
    data: {
      headerProps: {
        title: '铺货店铺数',
      },
      contentProps: {
        quantity: '49,008',
        cycleQuantity: '49,268',
        percentage: '-1%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU产出',
    data: {
      headerProps: {
        title: '日均动销SKU产出',
      },
      contentProps: {
        quantity: '60.33',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均动销SKU数',
    data: {
      headerProps: {
        title: '日均动销SKU数',
      },
      contentProps: {
        quantity: '2,836 ',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '日均SKU动销率',
    data: {
      headerProps: {
        title: '日均SKU动销率',
      },
      contentProps: {
        quantity: '1.04%',
        cycleQuantity: 'NA',
        percentage: '-',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '建议铺货店铺数',
    data: {
      headerProps: {
        title: '建议铺货店铺数',
      },
      contentProps: {
        quantity: '276,487',
        cycleQuantity: '288,666',
        percentage: '-4%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '店铺铺货率',
    data: {
      headerProps: {
        title: '店铺铺货率',
      },
      contentProps: {
        quantity: '17.73%',
        cycleQuantity: '17.07%',
        percentage: '4%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '未铺货门店数',
    data: {
      headerProps: {
        title: '未铺货门店数',
      },
      contentProps: {
        quantity: '227,479',
        cycleQuantity: '239,398',
        percentage: '-5%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU数',
    data: {
      headerProps: {
        title: '售罄SKU数',
      },
      contentProps: {
        quantity: '121,530',
        cycleQuantity: '110,414',
        percentage: '10%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
  {
    id: '在售SKU数',
    data: {
      headerProps: {
        title: '在售SKU数',
      },
      contentProps: {
        quantity: '272,673',
        cycleQuantity: '251,054',
        percentage: '9%',
        percentageChange: 'rise',
        contentType: 'short',
      },
      footerProps: {
        buttonColor: '#38D6A1',
        topBorderColor: '#38D6A1',
        buttons: [{ text: '供需罗盘' }, { text: 'BC一体化' }],
      },
    },
  },
  {
    id: '在售SKU平均产出',
    data: {
      headerProps: {
        title: '在售SKU平均产出',
      },
      contentProps: {
        quantity: '19.45',
        cycleQuantity: '17.81',
        percentage: '9%',
        percentageChange: 'rise',
        contentType: 'short',
      },
    },
  },
  {
    id: '售罄SKU预估损失',
    data: {
      headerProps: {
        title: '售罄SKU预估损失',
      },
      contentProps: {
        quantity: '510,712',
        cycleQuantity: '399,036',
        percentage: '28%',
        percentageChange: 'fall',
        contentType: 'short',
      },
    },
  },
];
