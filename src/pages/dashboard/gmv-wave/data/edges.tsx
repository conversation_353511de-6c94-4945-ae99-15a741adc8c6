export const initialEdges = [
  {
    source: '销售额',
    target: '客单价',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '订单数',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '场景数量',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '场景人数',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '场景花费',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '动销店铺数',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '店铺产出',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '客单价',
    target: '新客客单价',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '客单价',
    target: '老客客单价',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  //
  {
    source: '平均件单价',
    target: '单件商品SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '平均件单价',
    target: '单件商品SKU金额',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '订单数',
    target: '购买频次',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '订单数',
    target: '交易用户数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '动销店铺数',
    target: '店铺动销率',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '动销店铺数',
    target: '铺货店铺数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '店铺产出',
    target: '平均SKU产出',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '店铺产出',
    target: '动销SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },

  {
    source: '动销SKU数',
    target: '未动销SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '动销SKU数',
    target: '售罄SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },

  {
    source: '动销SKU数',
    target: '在售SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '十大人群',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '老客数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '新客数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '购买频次',
    target: '新客频次',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '购买频次',
    target: '老客频次',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '铺货店铺数',
    target: '建议铺货店铺数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '铺货店铺数',
    target: '未铺货门店数',
    startAnchor: 'left',
    endAnchor: 'right',
  },

  {
    source: '老客数',
    target: '历史用户数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '老客数',
    target: '老客复购率',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '在售SKU数',
    target: '售罄SKU预估损失',
    startAnchor: 'left',
    endAnchor: 'right',
  },
].map((item) => {
  return {
    ...item,
    id: `${item.source}-${item.target}`,
    type: 'smoothstep',
  };
});

export const newInitialEdges = [
  {
    source: '销售额',
    target: '订单数',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '单次购买金额',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '16大场景销售额',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '动销店铺数',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '销售额',
    target: '店铺产出',
    startAnchor: 'left',
    endAnchor: 'left',
  },
  {
    source: '订单数',
    target: '交易用户数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '订单数',
    target: '购买频次',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '新客数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '老客数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '交易用户数',
    target: '十大人群人数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '16大场景销售额',
    target: '16大场景订单数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '16大场景销售额',
    target: '16大场景单次购买金额',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '16大场景订单数',
    target: '16大场景人数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '16大场景订单数',
    target: '16大场景频次',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '动销店铺数',
    target: '店铺动销率',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '动销店铺数',
    target: '铺货店铺数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '铺货店铺数',
    target: '建议铺货店铺数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '铺货店铺数',
    target: '店铺铺货率',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '店铺产出',
    target: '在售SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '店铺产出',
    target: '在售SKU平均产出',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '店铺产出',
    target: '售罄SKU预估损失',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '在售SKU平均产出',
    target: '日均动销SKU产出',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '在售SKU平均产出',
    target: '日均动销SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
  {
    source: '售罄SKU预估损失',
    target: '售罄SKU数',
    startAnchor: 'left',
    endAnchor: 'right',
  },
].map((item) => {
  return {
    ...item,
    id: `${item.source}-${item.target}`,
    type: 'smoothstep',
  };
});
