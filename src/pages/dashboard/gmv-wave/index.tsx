import { useEffect, useMemo, useRef, useState } from 'react';
import Xarrow, { Xwrapper, useXarrow } from 'react-xarrows';

import { useModel } from '@umijs/max';
import { useSize } from 'ahooks';

import AttributionAnalyzer from './components/AttributionAnalyzer/AttributionAnalyzer';
import LayoutContent from './components/LayoutContent';
import SearchHeader from './components/SearchHeader';

export default function Index() {
  const { initialState } = useModel('@@initialState');

  const brandDemo = useMemo(() => {
    if (initialState?.currentBrandInfo?.brandNameCn === '通用磨坊') {
      return false;
    } else if (initialState?.currentBrandInfo?.brandNameCn === '百事食品') {
      return false;
    }
    return true;
  }, [initialState]);

  const ref = useRef(null);

  const size = useSize(ref);

  const onChangeXarrow = useXarrow();

  useEffect(() => {
    onChangeXarrow();
  }, [initialState]);
  return (
    <div
      ref={ref}
      className="relative flex w-full min-w-[960px] flex-col gap-y-2 xl:overflow-x-hidden"
    >
      <Xwrapper>
        <SearchHeader />

        {brandDemo ? (
          <LayoutContent key={Math.random()} />
        ) : (
          <AttributionAnalyzer key={Math.random()} />
        )}
        {/* <AttributionAnalyzer /> */}
      </Xwrapper>
    </div>
  );
}
