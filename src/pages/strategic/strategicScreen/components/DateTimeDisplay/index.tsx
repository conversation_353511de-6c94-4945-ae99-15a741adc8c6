import React, { useEffect, useState } from 'react';

const DateTimeDisplay: React.FC = () => {
  const [time, setTime] = useState<string>();
  const [date, setDate] = useState<string>();

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date().toLocaleTimeString());
      setDate(new Date().toLocaleDateString().replace(/\//g, '-'));
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);
  return (
    <div className="time flex items-center gap-x-3 text-white">
      <span>{date}</span>
      <span>{time}</span>
    </div>
  );
};
export default DateTimeDisplay;
