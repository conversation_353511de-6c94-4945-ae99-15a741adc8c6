import { useEffect, useMemo, useState } from 'react';

import { Map } from '@/components/Charts';
import api from '@/services';
import { ScrollBoard } from '@jiaminghi/data-view-react';
import { useRequest } from '@umijs/max';
import numeral from 'numeral';

type MapAndTableProps = {
  date: string;
  radioValue: '美团' | '饿了么' | '京东到家' | '多点' | '淘鲜达' | '其他';
};
export default function MapAndTable(props: MapAndTableProps) {
  const { date, radioValue } = props;

  const { run: postGmvArea, data: postGmvAreaData } = useRequest(
    api.omnipilot.bigScreen.postGmvArea,
    {
      manual: true,
    },
  );
  const [areaType, setAreaType] = useState(0);
  const [areaName, setAreaName] = useState('全国');

  useEffect(() => {
    postGmvArea({
      areaCode: 0,
      areaName: areaName,
      areaType: areaType,
      brand: '亿滋',
      platform: radioValue,
      queryDate: date,
    });
  }, [areaType, areaName, postGmvArea, radioValue, date]);

  const formattingThousands = (num: number | undefined) => {
    if (num === undefined || num === null) {
      return '--';
    } else {
      if (Number.isInteger(num)) {
        return numeral(num).format('0,0');
      } else {
        return numeral(num).format('0,0.00');
      }
    }
  };

  const formattingPercentages = (num: number | undefined) => {
    if (num === undefined || num === null) {
      return '--';
    } else {
      return numeral(num).format('0.00%');
    }
  };
  // table数据
  const config = useMemo(() => {
    const tableData = postGmvAreaData?.mapFormatList?.map((item, index) => {
      return [
        index + 1,
        item.areaName,
        formattingThousands(item.areaGmv),
        formattingThousands(item.areaDistributionVolume),
        formattingPercentages(item.areaDistributionRate),
      ];
    });
    return {
      indexHeader: '序号',
      waitTime: 1000,
      header: ['序号', '地区', 'GMV', '铺货数', '铺货率'],
      align: ['center', 'center', 'center', 'center'],
      rowNum: 10,
      data: tableData,
      headerHeight: 40,
      headerBGC: '#241C57',
      oddRowBGC: '#241C57',
      evenRowBGC: '',
      columnWidth: [50, 80, 100, 100, 80],
    };
  }, [postGmvAreaData]);

  return (
    <div className="flex h-full w-full justify-between">
      <div className="h-full w-[68%]">
        <Map
          type="heatmap"
          colorField="areaGmv"
          data={postGmvAreaData?.mapFormatList}
          popUpFields={[
            // {
            //   field: 'type',
            //   formatField(field, feature) {
            //     return '类型';
            //   },
            // },
            {
              field: 'areaGmv',
              formatField(field, feature) {
                return 'GMV';
              },
            },
          ]}
          onPolygonLayerClick={(data) => {
            const levelMap = {
              china: 0,
              province: 1,
              city: 2,
            };
            setAreaName(data.feature.properties.areaName);
            setAreaType(levelMap[data.feature.properties.level]);
          }}
        />
      </div>
      <div className="table">
        <ScrollBoard config={config} />
      </div>
    </div>
  );
}
