import { useEffect, useMemo, useRef, useState } from 'react';

import { DualAxes, Pie, Radar } from '@/components/Charts';
import api from '@/services';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  MinusOutlined,
} from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { useFullscreen, useSize } from 'ahooks';
import type {
  CheckboxOptionType,
  DatePickerProps,
  RadioChangeEvent,
} from 'antd';
import { Carousel, ConfigProvider, DatePicker, Radio, Spin } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { set } from 'lodash';
import numeral from 'numeral';

import { DateTimeDisplay, MapAndTable } from './components';
import './index.less';
import './index.rem.less';

const radioArr = [
  {
    value: '美团',
    label: '美团',
    style: { color: '#F9F9F9' },
  },
  {
    value: '饿了么',
    label: '饿了么',
    style: { color: '#F9F9F9' },
  },
  {
    value: '京东到家',
    label: '京东到家',
    style: { color: '#F9F9F9' },
  },
  {
    value: '多点',
    label: '多点',
    style: { color: '#F9F9F9' },
  },
  {
    value: '淘鲜达',
    label: '淘鲜达',
    style: { color: '#F9F9F9' },
  },
];
export default function Index() {
  const wrapRef = useRef<HTMLDivElement>(null);

  const [isFullscreen, { enterFullscreen, exitFullscreen, toggleFullscreen }] =
    useFullscreen(wrapRef);

  const size = useSize(wrapRef);

  const [loading, setLoading] = useState(true);

  // useEffect(() => {
  //   setTimeout(() => {
  //     setLoading(false);
  //   }, 10000);
  // }, []);

  const [chartSize, setChartSize] = useState(10);

  const [radioSize, setRadioSize] = useState(16);

  const [fontSize, setFontSize] = useState(0);

  useEffect(() => {
    function setRootFontSize() {
      // 获取视口宽度
      const viewportWidth = size?.width || 1920;

      // 基准设计稿宽度，假设是 1920px
      const baseWidth = 1920;

      // 计算字体大小
      const fontSize = (viewportWidth / baseWidth) * 100;

      setFontSize(fontSize);

      setChartSize(fontSize / 10);

      setRadioSize(fontSize / 5.1);

      // 设置根字体大小
      document.documentElement.style.fontSize = `${fontSize}px`;
    }
    setRootFontSize();
  }, [size]);

  useEffect(() => {
    return () => {
      document.documentElement.style.cssText = '';
    };
  }, []);

  const [radioValue, setRadioValue] = useState('美团');

  const [date, setDate] = useState<string>(
    dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  );

  const radioOnChange = (e: RadioChangeEvent) => {
    setRadioValue(e.target.value);
  };

  const onChange: DatePickerProps['onChange'] = (date, dateString) => {
    if (date) {
      const formattedDate = date.format('YYYY-MM-DD');
      setDate(formattedDate);
    }
  };

  // function preloadImg(path: string) {
  //   return new Promise((resolve) => {
  //     const img = new Image();
  //     img.src = path;
  //     img.onload = () => resolve(true);
  //     img.onerror = () => resolve(true);
  //   });
  // }

  // const carouselParentRef = useRef<HTMLDivElement | undefined>(undefined);

  // const size = useSize(carouselParentRef);

  const { run: postGmvPlatforms, data: postGmvData } = useRequest(
    api.omnipilot.bigScreen.postGmvPlatforms,
    {
      manual: true,
    },
  );
  const { run: postGmvAccumulated, data: postAccumulatedGmvData } = useRequest(
    api.omnipilot.bigScreen.postGmvAccumulated,
    {
      manual: true,
    },
  );

  const { run: postGmvRecentSevenDays, data: postGmvRecentSevenDaysData } =
    useRequest(api.omnipilot.bigScreen.postGmvRecentSevenDays, {
      manual: true,
    });

  const { run: postGmvThreeDimension, data: postGmvThreeDimensionData } =
    useRequest(api.omnipilot.bigScreen.postGmvThreeDimension, {
      manual: true,
    });
  // 大区上翻率
  const { run: postRegionOnlineRate, data: postRegionOnlineRateData } =
    useRequest(api.omnipilot.bigScreen.postRegionOnlineRate, {
      manual: true,
    });

  const RegionOnlineRateData = useMemo(() => {
    return postRegionOnlineRateData?.map((item) => {
      return {
        regionName: item.regionName,
        铺货数: item.regionProduceOnlineCount,
        上翻率: item.regionProduceOnlineRate,
      };
    });
  }, [postRegionOnlineRateData]);

  // 分销品达成率
  const {
    run: postRegionAchievementRate,
    data: postRegionAchievementRateData,
  } = useRequest(api.omnipilot.bigScreen.postRegionAchievementRate, {
    manual: true,
  });

  const RegionAchievementRateData = useMemo(() => {
    return postRegionAchievementRateData?.map((item) => {
      return {
        regionName: item.regionName,
        铺货数: item.regionDistributionCount,
        达成率: item.regionDistributionAchievementRate,
      };
    });
  }, [postRegionAchievementRateData]);

  // 重点客户上翻率
  const {
    run: postKeyCustomerOnlineRate,
    data: postKeyCustomerOnlineRateData,
  } = useRequest(api.omnipilot.bigScreen.postKeyCustomerOnlineRate, {
    manual: true,
  });

  const KeyCustomerOnlineRateData = useMemo(() => {
    return postKeyCustomerOnlineRateData?.map((item) => {
      return {
        keyCustomerName: item.keyCustomerName,
        重点客户铺货数: item.keyCustomerCount,
        上翻率: item.keyCustomerOnlineRate,
      };
    });
  }, [postKeyCustomerOnlineRateData]);

  // 重点子品牌上翻率
  const {
    run: postKeySubBrandOnlineRate,
    data: postKeySubBrandOnlineRateData,
  } = useRequest(api.omnipilot.bigScreen.postKeySubBrandOnlineRate, {
    manual: true,
  });

  const KeySubBrandOnlineRateData = useMemo(() => {
    return postKeySubBrandOnlineRateData?.map((item) => {
      return {
        keySubBrandName: item.keySubBrandName,
        铺货数: item.keySubBrandCount,
        上翻率: item.keySubBrandOnlineRate,
      };
    });
  }, [postKeySubBrandOnlineRateData]);

  // 大区上翻率 分销品达成率 重点客户上翻率 重点子品牌上翻率
  useEffect(() => {
    setLoading(true);
    Promise.all([
      // 重点客户上翻率
      postKeyCustomerOnlineRate({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),
      // 重点子品牌上翻率
      postKeySubBrandOnlineRate({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),
      // 大区上翻率
      postRegionOnlineRate({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),

      postRegionAchievementRate({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),

      postGmvThreeDimension({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),

      postGmvRecentSevenDays({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),

      postGmvPlatforms({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),

      postGmvAccumulated({
        brand: '亿滋',
        platform: radioValue,
        queryDate: date,
      }),
    ])
      .then(() => {
        setLoading(false); // 所有请求成功后设置 loading 为 false
      })
      .catch((error) => {
        setLoading(false); // 即使有请求失败也设置 loading 为 false
      });
  }, [radioValue, date]);

  const formattingThousands = (num: number | undefined) => {
    if (num === undefined) {
      return '--';
    } else {
      return numeral(num).format('0,0.00');
    }
  };

  const formattingPercentages = (num: number | undefined) => {
    if (num === undefined) {
      return '--';
    } else {
      return numeral(num).format('0.00%');
    }
  };

  const GmvRecentSevenDaysData = useMemo(() => {
    return postGmvRecentSevenDaysData?.map((item) => {
      if (item.gmvDate) {
        const parts = item.gmvDate.split('-');
        return {
          gmvDate: `${parts[0]}/${parts[1]}/${parts[2]}`,
          GMV: item.gmv,
          同比: item.gmvYearOnYear,
        };
      }
      return null;
    });
  }, [postGmvRecentSevenDaysData]);

  const disabledDate = (current: any) => {
    // 禁用一年之前的日期和今天及今天之后的日期
    return (
      current &&
      (current < dayjs().subtract(1, 'year').endOf('day') ||
        current >= dayjs().endOf('day'))
    );
  };
  const carouselRef = useRef<any>(null);

  // console.log(
  //   '%c [ carouselRefSize ]-335',
  //   'font-size:13px; background:pink; color:#bf2c9f;',
  //   carouselRefSize,
  // );
  // useEffect(() => {
  //   if (carouselRef.current.innerSlider) {
  //     console.log(
  //       '%c [ carouselRef.current.innerSlider ]-336',
  //       'font-size:13px; background:pink; color:#bf2c9f;',
  //       carouselRef.current.innerSlider,
  //     );
  //     carouselRef.current.innerSlider.pause();
  //     console.log('11');
  //   }
  // }, [isFullscreen, fontSize]);

  // useEffect(() => {
  //   if (carouselRef.current.innerSlider) {
  //     carouselRef.current.innerSlider.render();
  //     console.log('22');
  //   }
  // }, [isFullscreen]);
  return (
    <div
      className={classNames('h-full w-full')}
      // style={transform}
      ref={wrapRef}
    >
      <ConfigProvider
        theme={{
          components: {
            Radio: {
              /* 这里是你的组件 token */
              colorPrimary: '#7C4FF4',
              // buttonCheckedBg: '#7C4FF4',
              radioSize: radioSize,
            },
            DatePicker: {
              colorPrimary: '#7C4FF4',
              cellHoverBg: 'rgba(124, 79, 244, 0.3)',
              cellBgDisabled: 'rgba(124, 79, 244, 0.1)',
              // inputFontSize: dataSize,
              // inputFontSize: 10,
            },
            Button: {
              colorPrimary: '#7C4FF4',
              defaultHoverColor: '#7C4FF4',
            },
          },
        }}
      >
        <div
          className="screenContainer flex flex-col justify-between bg-[#140122]"
          // style={transform}
        >
          <div className="text-white scHeader">
            <div className="flex justify-between h-full">
              <div className="flex headerLeft">
                <div className="flex justify-between headerLeftContent">
                  <DateTimeDisplay />
                  <div className="flex items-center gap-x-3">
                    <span>日期</span>
                    <DatePicker
                      getPopupContainer={(triggerNode) =>
                        wrapRef.current || document.body
                      }
                      className="datePicker"
                      onChange={onChange}
                      size="small"
                      defaultValue={dayjs().subtract(1, 'day')}
                      disabledDate={disabledDate}
                    />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-center headerMiddle">
                {/* <span className="title">可视化大屏</span> */}
              </div>
              <div className="flex items-center justify-end headerRight">
                <div className="flex items-center gap-x-3">
                  <span>平台 :</span>
                  <Radio.Group
                    className=""
                    onChange={radioOnChange}
                    value={radioValue}
                    defaultValue={'美团'}
                    options={radioArr}
                  />
                </div>
                <div
                  className="flex items-center justify-center cursor-pointer icon"
                  onClick={() => {
                    toggleFullscreen();
                  }}
                >
                  {isFullscreen ? (
                    <div className="FullscreenExitOutlined" />
                  ) : (
                    <div className="FullscreenOutlined" />
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-between flex-shrink-0 text-white contentMiddle gap-x-3">
            <div className="flex contentMiddleLeft">
              <div className="h1Title">各平台GMV表现</div>
              <div className="flex flex-col items-center justify-center GMVPerformanceEachPlatform">
                {loading ? (
                  <Spin />
                ) : (
                  <div className="flex flex-col items-center justify-between mt-8 gap-y-5">
                    <span className="dayGmv">DAY GMV</span>
                    <span className="Gradient-h2">
                      {formattingThousands(postGmvData?.gmvValue)}
                    </span>
                    <div className="flex flex-row items-center justify-center">
                      <div className="text-base_1">同比 </div>

                      <div
                        className={`${postGmvData?.yearOnYearGrowth ? (postGmvData?.yearOnYearGrowth > 0 ? `text-red-600` : `text-green-500`) : null} `}
                      >
                        <div className="tongbi">
                          {postGmvData?.yearOnYearGrowth ? (
                            postGmvData?.yearOnYearGrowth > 0 ? (
                              <ArrowUpOutlined />
                            ) : (
                              <ArrowDownOutlined />
                            )
                          ) : (
                            <MinusOutlined />
                          )}
                          {formattingPercentages(postGmvData?.yearOnYearGrowth)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex items-center justify-center gmvRatio">
                {loading ? (
                  <Spin />
                ) : (
                  <Radar
                    data={postGmvData?.gmvRatio}
                    chartSize={chartSize}
                    x={'iconFormatName'}
                    y={'iconFormatValue'}
                    isFullscreen={isFullscreen}
                    colorFiled={'iconFormatType'}
                  />
                )}
              </div>
            </div>
            <div className="flex flex-col MTDCon">
              <div className="flex flex-col justify-between w-full h-full">
                <div className="flex justify-between w-full h-90px">
                  <div className="flex flex-col justify-start MTD">
                    <span className="h2Title">MTD GMV</span>
                    <div className="flex flex-col items-center justify-center w-full">
                      {loading ? (
                        <Spin />
                      ) : (
                        <div className="flex flex-col items-center justify-center">
                          <span className="Gradient-h2">
                            {formattingThousands(
                              postAccumulatedGmvData?.mtdGmv,
                            )}
                          </span>
                          <span>
                            达成率
                            {formattingPercentages(
                              postAccumulatedGmvData?.mtdAchievementRater,
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col justify-start YTD">
                    <span className="h2Title">YTD GMV</span>
                    <div className="flex flex-col items-center justify-center w-full">
                      {loading ? (
                        <Spin />
                      ) : (
                        <div className="flex flex-col items-center justify-center">
                          <span className="Gradient-h2">
                            {formattingThousands(
                              postAccumulatedGmvData?.ytdGmv,
                            )}
                          </span>
                          <span>
                            达成率
                            {formattingPercentages(
                              postAccumulatedGmvData?.ytdAchievementRater,
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-center GmvRecentSevenDays">
                  <div className="h1Title">GMV趋势分析</div>
                  {loading ? (
                    <Spin />
                  ) : (
                    <div className="SevenDays-GMV">
                      <DualAxes
                        data={GmvRecentSevenDaysData}
                        chartSize={chartSize}
                        isFullscreen={isFullscreen}
                        xField={'gmvDate'}
                        intervalYField={'GMV'}
                        lineYField={'同比'}
                        legendPosition={'top'}
                        alignItems={'flex-end'}
                        yLabelFormatter={(v: number) =>
                          numeral(v / 10000).format('0') + '万'
                        }
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center justify-center GmvThreeDimension">
              <div className="h1Title">GMV</div>
              {loading ? (
                <Spin />
              ) : (
                <div className="carousel_wrap">
                  <Carousel
                    key={`${isFullscreen}_${fontSize}_${Date.now()}`}
                    autoplay
                    // adaptiveHeight
                    // rootClassName="w-full h-full"
                    ref={carouselRef}
                  >
                    <Pie
                      height={260}
                      fontSize={fontSize}
                      isFullscreen={isFullscreen}
                      chartSize={chartSize}
                      data={postGmvThreeDimensionData?.customerGroupGmv}
                      angleField="iconFormatValue"
                      colorField="iconFormatName"
                    />

                    <Pie
                      data={postGmvThreeDimensionData?.areaGmv}
                      fontSize={fontSize}
                      height={260}
                      isFullscreen={isFullscreen}
                      chartSize={chartSize}
                      angleField="iconFormatValue"
                      colorField="iconFormatName"
                    />
                    <Pie
                      data={postGmvThreeDimensionData?.mainSubBrands}
                      fontSize={fontSize}
                      height={260}
                      isFullscreen={isFullscreen}
                      chartSize={chartSize}
                      angleField="iconFormatValue"
                      colorField="iconFormatName"
                    />
                  </Carousel>
                </div>
              )}
            </div>
          </div>
          <div className="flex justify-between contentMiddleBottom h-670px w-1980px gap-x-3">
            <div className="flex flex-col justify-between w-760px h-670px">
              <div className="flex justify-between h-205px w-760px">
                <div className="flex items-center justify-center RegionOnlineRate">
                  <div className="h1Title">大区上翻率</div>
                  {loading ? (
                    <Spin />
                  ) : (
                    <div className="RegionOnlineRateData-dual">
                      <DualAxes
                        id={'RegionOnlineRate'}
                        data={RegionOnlineRateData}
                        isFullscreen={isFullscreen}
                        chartSize={chartSize}
                        xField={'regionName'}
                        intervalYField={'铺货数'}
                        lineYField={'上翻率'}
                        alignItems={'flex-end'}
                        yLabelFormatter={(v: number) =>
                          numeral(v / 1000).format('0.0') + 'k'
                        }
                      />
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-center RegionAchievementRate">
                  <div className="h1Title">必分销品达成率</div>

                  {loading ? (
                    <Spin />
                  ) : (
                    <div className="regionDistributionCount-dual">
                      <DualAxes
                        data={RegionAchievementRateData}
                        chartSize={chartSize}
                        xField={'regionName'}
                        isFullscreen={isFullscreen}
                        intervalYField={'铺货数'}
                        alignItems={'flex-end'}
                        lineYField={'达成率'}
                        yLabelFormatter={(v: number) =>
                          numeral(v / 1000).format('0.0') + 'k'
                        }
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-center KeyCustomerOnlineRate">
                <div className="h1Title">重点客户上翻率</div>
                {loading ? (
                  <Spin className="KeyCustomerOnlineRateLoading" />
                ) : (
                  <div className="KeyCustomerOnlineRateData-dual">
                    <DualAxes
                      data={KeyCustomerOnlineRateData}
                      xField={'keyCustomerName'}
                      isFullscreen={isFullscreen}
                      chartSize={chartSize}
                      intervalYField={'重点客户铺货数'}
                      lineYField={'上翻率'}
                      alignItems={'flex-end'}
                      yLabelFormatter={(v: number) =>
                        numeral(v / 1000).format('0.0') + 'k'
                      }
                    />
                  </div>
                )}
              </div>

              <div className="flex items-center justify-center KeySubBrandOnlineRate">
                <div className="h1Title">重点子品牌上翻率</div>
                {loading ? (
                  <Spin />
                ) : (
                  <div className="KeySubBrandOnlineRateData-dual">
                    <DualAxes
                      data={KeySubBrandOnlineRateData}
                      xField={'keySubBrandName'}
                      isFullscreen={isFullscreen}
                      chartSize={chartSize}
                      intervalYField={'铺货数'}
                      lineYField={'上翻率'}
                      alignItems={'flex-end'}
                      yLabelFormatter={(v: number) =>
                        numeral(v / 1000).format('0.0') + 'k'
                      }
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center justify-center map w-1160px h-670px">
              {loading ? (
                <Spin />
              ) : (
                <MapAndTable date={date} radioValue={radioValue} />
              )}
            </div>
          </div>
        </div>
      </ConfigProvider>
    </div>
  );
}
