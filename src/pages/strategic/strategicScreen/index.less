.headerLeft {
  background: url('./../../../assets/images/bigScreen/dingbuzuotixing.png')
    no-repeat center center;
  background-size: 100% 100%;
}
.headerMiddle {
  background: url('./../../../assets/images/bigScreen/dingbuzhongjian.png')
    no-repeat center center;
  background-size: 100% 100%;
}
.headerRight {
  background: url('./../../../assets/images/bigScreen/dingbuyoujuxing.png')
    no-repeat center center;
  background-size: 100% 100%;
}
.contentMiddleLeft {
  background: url('./../../../assets/images/bigScreen/beiban.png') center center;
  background-size: 100% 100%;
}
.MTD {
  background: url('./../../../assets/images/bigScreen/beiban1.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.YTD {
  background: url('./../../../assets/images/bigScreen/beiban2.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.GmvRecentSevenDays {
  background: url('./../../../assets/images/bigScreen/beiban9.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.GmvThreeDimension {
  background: url('./../../../assets/images/bigScreen/beiban3.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.RegionOnlineRate {
  background: url('./../../../assets/images/bigScreen/beiban4.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.RegionAchievementRate {
  background: url('./../../../assets/images/bigScreen/beiban4.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.KeyCustomerOnlineRate {
  background: url('./../../../assets/images/bigScreen/beiban6.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.KeySubBrandOnlineRate {
  background: url('./../../../assets/images/bigScreen/beiban7.png') no-repeat
    center center;
  background-size: 100% 100%;
}
.map {
  background: url('./../../../assets/images/bigScreen/beiban8.png') no-repeat
    center center;
  background-size: 100% 100%;
}

* {
  font-family: PingFangSC;
}
