.FullscreenOutlined {
  background: url('./../../../assets/images/bigScreen/big.png') no-repeat center
    center;
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
}
.FullscreenExitOutlined {
  background: url('./../../../assets/images/bigScreen/small.png') no-repeat
    center center;
  background-size: 100% 100%;
  width: 50px;
  height: 50px;
}
.SevenDays-GMV {
  position: absolute;
  bottom: 0;
  width: 675px;
  height: 140px;
}

.screenContainer {
  height: 1080px;
  width: 1920px;
}
.GmvRecentSevenDays {
  height: 156px;
  width: 675px;
  position: relative;
}
.scHeader {
  height: 76px;
  span {
    font-family: Helvetica;
    font-size: 22px;
    color: #ffffff;
    line-height: 29px;
    text-align: left;
    font-style: normal;
  }

  .headerLeft {
    width: 750px;
    height: 76px;

    .headerLeftContent {
      width: 600px;
      height: 76px;

      .time {
        margin-left: 56px;
        margin-right: 12px;
      }

      // .datePicker {
      //   width: 200px;
      //   height: 32px;
      // }
    }
  }
  .headerMiddle {
    width: 580px;
    height: 76px;
    margin-left: -55px;
    margin-right: -55px;

    .title {
      font-size: 48px;
    }
  }
  .headerRight {
    width: 750px;
    height: 76px;
  }
}
.icon {
  width: 50px;
  height: 50px;
  margin-right: 10px;
}
.contentMiddle {
  width: 1920px;
  height: 260px;
  padding-left: 36px;
  padding-right: 36px;
}
.contentMiddleLeft {
  height: 260px;
  width: 548px;
  padding-left: 20px;
  position: relative;
}
.gmvRatio {
  width: 380px;
  height: 260px;
}

.GMVPerformanceEachPlatform {
  width: 148px;
  height: 260px;
}
.h1Title {
  font-size: 22px;
  color: #ffffff;
  line-height: 33px;
  // text-decoration: underline;
  position: absolute;
  left: 14px;
  top: 8px;
}

.h2Title {
  font-size: 18px;
  color: #ffffff;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  margin-top: 7px;
  margin-left: 9px;
  // text-decoration: underline;
}
.Gradient {
  font-family:
    PingFangSC,
    PingFang SC;

  font-size: 28px;

  // line-height: 42px;

  background: linear-gradient(to top, #00cafa, #f8feff);
  -webkit-background-clip: text;
  color: transparent;
}
.Gradient-h2 {
  font-family:
    PingFangSC,
    PingFang SC;

  font-size: 24px;

  // line-height: 42px;

  background: linear-gradient(to top, #00cafa, #f8feff);
  -webkit-background-clip: text;
  color: transparent;
}
.dayGmv {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 22px;
  color: #ffffff;
  line-height: 30px;
  text-align: left;
  font-style: normal;
}
.text-base_1 {
  font-size: 16px;
}
.text-xl_1 {
  font-size:
    20px,
    line-height 28px;
}
.gap-y-5 {
  row-gap: 20px /* 20px */;
}
.mt-8 {
  margin-top: 32px /* 32px */;
}
.YTD {
  width: 330px;
  font-size: 16px;
}
.GmvThreeDimension {
  width: 600px;
  height: 260px;
  position: relative;
}
.pie-container {
  height: 260px;
}
.gap-x-3 {
  column-gap: 12px /* 12px */;
}
.MTDCon {
  height: 260px;
  width: 675px;
}
.h-90px {
  height: 90px;
}
.MTD {
  width: 330px;
  font-size: 16px;
}
.w-675px {
  width: 675px;
}
.h-670px {
  height: 670px;
}

.h-205px {
  height: 205px;
}
.w-760px {
  width: 760px;
}

.h-210px {
  height: 210px;
}
.w-1160px {
  width: 1160px;
}
.w-1980px {
  width: 1980px;
}
.contentMiddleBottom {
  width: 1920px;
  height: 670px;
  padding-left: 36px;
  padding-right: 36px;
  margin-bottom: 25px;
}
.map {
  padding: 20px;
}
.table {
  height: 550px;
  width: 410px;
  margin-top: 44px;
}
.header {
  background: linear-gradient(
    90deg,
    rgba(136, 51, 236, 0.35) 0%,
    rgba(17, 0, 23, 0) 100%
  );
  height: 40px;
}
.header-item {
  line-height: 40px !important;
  font-size: 14px !important;
}
.rows {
  height: 510px !important;
  width: 410px !important;
}
.row-item {
  height: 51px !important;
  width: 410px !important;
  font-size: 14px !important;
  line-height: 51px !important;
}
.dv-scroll-board {
  height: 550px !important;
  width: 410px !important;
}
.w-24px {
  width: 24px;
}
.h-96px {
  height: 96px;
}
.carousel_wrap {
  width: 600px;
  height: 260px;
}

.RegionOnlineRate {
  width: 365px;
  height: 205px;
  position: relative;

  .RegionOnlineRateData-dual {
    position: absolute;
    bottom: 0;
    height: 190px;
    width: 356px;
  }
}

.RegionAchievementRate {
  width: 365px;
  height: 205px;
  position: relative;

  .regionDistributionCount-dual {
    position: absolute;
    bottom: 0;
    height: 190px;
    width: 356px;
  }
}

.KeyCustomerOnlineRate {
  width: 760px;
  height: 210px;
  position: relative;

  .KeyCustomerOnlineRateData-dual {
    position: absolute;
    bottom: 0;
    width: 756px;
    height: 195px;
  }
}
.KeySubBrandOnlineRate {
  width: 760px;
  height: 210px;
  position: relative;

  .KeySubBrandOnlineRateData-dual {
    position: absolute;
    bottom: 0;
    width: 756px;
    height: 195px;
  }
}
.tongbi {
  font-size: 20px /* 20px */;
  line-height: 28px /* 28px */;
}
