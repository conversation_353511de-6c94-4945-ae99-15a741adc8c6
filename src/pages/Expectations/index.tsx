import { useMemo } from 'react';

import { useLocation } from '@umijs/max';

const IMG_SRC_MAP: Record<string, string> = {
  // 供给大盘
  '/fullAmountBI/supplyMarket': require('@/assets/images/expectations/gongji.png'),
  // 营销大盘
  '/fullAmountBI/marketing': require('@/assets/images/expectations/yingxiao.png'),
};

const Index = () => {
  const { pathname } = useLocation();

  const src = useMemo(() => {
    return IMG_SRC_MAP[pathname];
  }, [pathname]);

  return (
    <div className="flex items-center justify-center h-full">
      <div>
        <img
          height={500}
          width={500}
          className="block object-contain mx-auto"
          alt=""
          src={src}
        />
      </div>
    </div>
  );
};

export default Index;
