import { SYSTEMS } from '@/constants';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { request, useModel, useRequest } from '@umijs/max';
import dayjs from 'dayjs';

export const columns: ProColumns<any>[] = [
  {
    title: '用户名',
    dataIndex: 'userName',
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '访问日期',
    dataIndex: 'statisticDate',
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '访问日期',
    dataIndex: 'date',
    hideInTable: true,
    valueType: 'dateRange',
    formItemProps: {
      initialValue: [
        dayjs().add(-7, 'day').format('YYYY-MM-DD'),
        dayjs().add(1, 'day').format('YYYY-MM-DD'),
      ],
    },
    fieldProps: {
      disabledDate: (current) => current > dayjs().add(1, 'day'),
      presets: [
        {
          label: '昨天',
          value: [
            dayjs().add(-1, 'day').format('YYYY-MM-DD'),
            dayjs().add(1, 'day').format('YYYY-MM-DD'),
          ],
        },
        {
          label: '最近7天',
          value: [
            dayjs().add(-7, 'day').format('YYYY-MM-DD'),
            dayjs().add(1, 'day').format('YYYY-MM-DD'),
          ],
        },
        {
          label: '最近30天',
          value: [
            dayjs().add(-30, 'day').format('YYYY-MM-DD'),
            dayjs().add(1, 'day').format('YYYY-MM-DD'),
          ],
        },
      ],
    },
  },
  {
    title: '系统名称',
    dataIndex: 'sysName',
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '访问总数',
    dataIndex: 'sumCount',
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '设备',
    dataIndex: 'model',
    ellipsis: true,
    hideInSearch: true,
  },
];

const UserStatistics = () => {
  const { run } = useRequest(
    (data) =>
      request('/auth/api/v1/open/tracking/search', {
        method: 'POST',
        data,
      }),
    {
      manual: true,
    },
  );
  const { initialState } = useModel('@@initialState');
  return (
    <ProTable
      columns={columns}
      cardBordered
      request={async (params) => {
        const res = await run({
          ...params,
          pageNum: params.current,
          // brandId: initialState?.currentBrandInfo?.id,
          // brandName: initialState?.currentBrandInfo?.brandNameCn,
          startDate: params.date?.[0],
          endDate: params.date?.[1],
          // sysId: SYSTEMS.全域驾驶舱,
          // sysName: '全域驾驶舱',
        });
        return {
          total: res?.total,
          data: res?.list,
        };
      }}
      pagination={{
        pageSize: 10,
      }}
    />
  );
};

export default UserStatistics;
