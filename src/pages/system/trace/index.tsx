import { useEffect, useState } from 'react';

import { SYSTEMS } from '@/constants';
import { useModel } from '@umijs/max';

export default () => {
  const { initialState } = useModel('@@initialState');
  const [src, setSrc] = useState('');
  useEffect(() => {
    const url = new URL(
      'https://data.mpsmedia.com.cn/shareDashboard/eb468ab7edbb47af9e57e5d0890cc2a7?type=NONE',
    );

    url.searchParams.set('brandId', `${initialState?.currentBrandInfo?.id}`);
    url.searchParams.set('sysId', SYSTEMS.全域驾驶舱.toString());
    url.searchParams.set('userId', `${initialState?.userInfo?.id}`);
    setSrc(url.href);
  }, [initialState?.currentBrandInfo?.id, initialState?.userInfo?.id]);

  return <iframe src={src} />;
};
