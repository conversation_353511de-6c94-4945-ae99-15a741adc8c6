import { useEffect, useRef, useState } from 'react';
import React from 'react';

import {
  CheckCircleFilled,
  DownloadOutlined,
  InboxOutlined,
  InfoCircleFilled,
  PlusOutlined,
  QuestionCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Segmented,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import XLSX from 'xlsx';

import type { CampaignPlanItem, GetListResponse, SelectDataResponse } from './data.d';

interface BrandResponse {
  status: string;
  message?: string;
  data: {
    brands: Array<{ brand: string }>;
    subBrands: Array<{ subBrand: string }>;
  };
}

export default function CampaignPlanManagement() {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState<CampaignPlanItem[]>([]);
  const [brandOptions, setBrandOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [subBrandOptions, setSubBrandOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [isMaintained, setIsMaintained] = useState(true);
  const [pageSize, setPageSize] = useState<number>(20);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 添加通用请求配置
  const fetchConfig = {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    mode: 'cors' as RequestMode,
  };

  // 构建请求参数
  const buildRequestParams = (additionalParams: { filter_list?: string[] | string } & Record<string, any> = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: Array.isArray(additionalParams.filter_list) 
        ? JSON.stringify(additionalParams.filter_list)
        : (additionalParams.filter_list || ''),
      upload_type: 'campaign_plan',
    };

    // 移除filter_list，因为它已经被处理到brand参数中
    const { filter_list, ...restParams } = additionalParams;
    const finalParams = { ...params, ...restParams };

    return Object.entries(finalParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
      .join('&');
  };

  // 获取品牌列表
  const fetchBrandOptions = async () => {
    try {
      const searchValues = await searchForm.validateFields().catch(() => ({}));
      const params = buildRequestParams({
        is_maintained: isMaintained ? 'true' : 'false',
        filter_list: searchValues.keyword || '',
      });

      const url = `/bims/get_list?${params}`;
      console.log('Fetching brand options with URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        ...fetchConfig,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      let data: BrandResponse;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        throw new Error('Invalid JSON response from server');
      }

      if (data.status === 'success') {
        if (data.data.brands) {
          const options = data.data.brands.map((item: { brand: string }) => ({
            label: item.brand,
            value: item.brand,
          }));
          setBrandOptions(options);
        }
      } else {
        throw new Error(data.message || '获取品牌列表失败');
      }
    } catch (error) {
      console.error('Error fetching brand options:', error);
      message.error(
        '获取品牌列表失败：' +
          (error instanceof Error ? error.message : '未知错误'),
      );
    }
  };

  // 获取品牌和子品牌列表
  const fetchBrands = async () => {
    try {
      const params = buildRequestParams({
        is_maintained: isMaintained ? 'true' : 'false',
      });

      const url = `/bims/get_list?${params}`;
      console.log('Fetching brands with URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        ...fetchConfig,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      let data: BrandResponse;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        throw new Error('Invalid JSON response from server');
      }

      if (data.status === 'success') {
        if (data.data.brands) {
          const options = data.data.brands.map((item: { brand: string }) => ({
            label: item.brand,
            value: item.brand,
          }));
          setBrandOptions(options);
        }
        if (data.data.subBrands) {
          const options = data.data.subBrands.map((item: { subBrand: string }) => ({
            label: item.subBrand,
            value: item.subBrand,
          }));
          setSubBrandOptions(options);
        }
      } else {
        throw new Error(data.message || '获取品牌列表失败');
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
      message.error(
        '获取品牌列表失败：' +
          (error instanceof Error ? error.message : '未知错误'),
      );
    }
  };

  useEffect(() => {
    fetchBrands();
    fetchBrandOptions();
  }, []);

  // 渲染标题和提示
  const renderTitle = (title: string, tooltip?: string) => {
    return (
      <Space>
        {title}
        {tooltip && (
          <Tooltip title={tooltip}>
            <QuestionCircleOutlined style={{ color: '#999' }} />
          </Tooltip>
        )}
      </Space>
    );
  };

  // 为表单项添加提示
  const createFormItemLabel = (label: string, tooltip?: string) => {
    if (!tooltip) return label;
    return (
      <>
        {label}
        <Tooltip title={tooltip}>
          <QuestionCircleOutlined style={{ color: '#999', marginLeft: 4 }} />
        </Tooltip>
      </>
    );
  };

  const columns: ProColumns<CampaignPlanItem>[] = [
    {
      title: '品牌',
      dataIndex: 'brand',
      align: 'center',
      valueType: 'select',
      fieldProps: {
        options: brandOptions,
      },
    },
    {
      title: '计划ID',
      dataIndex: 'plan_id',
      align: 'center',
    },
    {
      title: '计划名称',
      dataIndex: 'plan_name',
      align: 'center',
    },
    {
      title: renderTitle('子品牌', '存有多子品牌，用"/"隔开或填写全品牌'),
      dataIndex: 'sub_brand',
      align: 'center',
      valueType: 'select',
      fieldProps: {
        options: subBrandOptions,
      },
    },
    {
      title: renderTitle('品线', '存有多品线，用"/"隔开或填写全品线'),
      dataIndex: 'product_line',
      align: 'center',
    },
    {
      title: renderTitle('城市', '存有多城市，用"/"隔开或填写全国'),
      dataIndex: 'city',
      align: 'center',
    },
    {
      title: renderTitle('出价模式', 'CPM、最大化拿量、CPC、其他'),
      dataIndex: 'price_type',
      align: 'center',
    },
    {
      title: '日预算',
      dataIndex: 'budget',
      align: 'center',
    },
    {
      title: '费用类型',
      dataIndex: 'cost_type',
      align: 'center',
    },
    {
      title: renderTitle('计划类型', 'GMV提升、渠道加强、洞察人群、拉新客、老客促活'),
      dataIndex: 'plan_type',
      align: 'center',
    },
    {
      title: '投放场景',
      dataIndex: 'delivery_scene',
      align: 'center',
    },
    {
      title: '投放人群',
      dataIndex: 'delivery_audience',
      align: 'center',
    },
    {
      title: renderTitle('渠道定向', '存有多渠道，用"/"隔开或无'),
      dataIndex: 'channel_orientation',
      align: 'center',
    },
    {
      title: renderTitle('一方人群', '是或否'),
      dataIndex: 'one_party_crowd',
      align: 'center',
    },
    {
      title: renderTitle('精准配券', '是或否'),
      dataIndex: 'precise_coupon',
      align: 'center',
    },
    {
      title: renderTitle('投放时段类型', '周中或周末或节假日或通投'),
      dataIndex: 'delivery_time_type',
      align: 'center',
    },
    {
      title: renderTitle('投放时段', '输入格式需遵循 "HHMM - HHMM" 的标准格式，示例为 "1600 - 1800"'),
      dataIndex: 'delivery_time',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      valueType: 'option',
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center gap-x-3">
            {isMaintained && (
              <a key="edit" onClick={() => handleEdit(record)}>
                编辑
              </a>
            )}
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </div>
        );
      },
    },
  ];

  const handleEdit = (record: CampaignPlanItem) => {
    const editData = {
      ...record,
      id: record.id,
    };
    console.log('Editing record:', editData);
    editForm.setFieldsValue(editData);
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      
      // 构建请求数据
      const requestData = {
        user: initialState?.userInfo?.username || '',
        role: initialState?.userInfo?.type || '',
        brand: values.brand || '',
        upload_type: 'campaign_plan',
        id: values.id,
        plan_name: values.plan_name,
        plan_id: values.plan_id,
        sub_brand: values.sub_brand,
        product_line: values.product_line,
        city: values.city,
        price_type: values.price_type,
        budget: values.budget,
        cost_type: values.cost_type,
        plan_type: values.plan_type,
        delivery_scene: values.delivery_scene,
        delivery_audience: values.delivery_audience,
        channel_orientation: values.channel_orientation,
        one_party_crowd: values.one_party_crowd,
        precise_coupon: values.precise_coupon,
        delivery_time_type: values.delivery_time_type,
        delivery_time: values.delivery_time
      };

      const response = await fetch('/bims/edit_data_campaign_plan', {
        method: 'POST',
        ...fetchConfig,
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        message.success('更新成功');
        setEditModalVisible(false);
        actionRef.current?.reload();
      } else {
        throw new Error(result.message || '更新失败');
      }
    } catch (error) {
      console.error('更新失败:', error);
      message.error(`更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleDelete = async (record: CampaignPlanItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 ${record.planName || record.planId} 吗？`,
      onOk: async () => {
        try {
          const params = {
            user: initialState?.userInfo?.username || '',
            role: initialState?.userInfo?.type || '',
            brand: record.brand || '',
            upload_type: 'campaign_plan',
            is_maintained: isMaintained ? 'true' : 'false',
            id: record.id,
            is_all_delete: 'false'
          };

          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            ...fetchConfig,
            body: JSON.stringify(params),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          if (result.status === 'success') {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            throw new Error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },
    });
  };

  const handleDeleteAll = async () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的记录');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRows.length} 条记录吗？`,
      onOk: async () => {
        try {
          // 收集所有选中行的品牌
          const brands = [...new Set(selectedRows.map(row => row.brand))];
          
          const params = {
            user: initialState?.userInfo?.username || '',
            role: initialState?.userInfo?.type || '',
            brand: brands.length === 1 ? brands[0] : JSON.stringify(brands),
            upload_type: 'campaign_plan',
            is_maintained: isMaintained ? 'true' : 'false',
            id: JSON.stringify(selectedRows.map((row) => row.id)),
            is_all_delete: 'false'
          };

          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            ...fetchConfig,
            body: JSON.stringify(params),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          if (result.status === 'success') {
            message.success('批量删除成功');
            setSelectedRows([]);
            setSelectedRowKeys([]);
            if (actionRef.current?.clearSelected) {
              actionRef.current.clearSelected();
            }
            actionRef.current?.reload();
          } else {
            throw new Error(result.message || '批量删除失败');
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },
    });
  };

  const handleDeleteAllData = async () => {
    Modal.confirm({
      title: '确认删除全部',
      content: '确定要删除全部数据吗？此操作不可恢复！',
      onOk: async () => {
        try {
          // 获取表单中选择的品牌
          const searchValues = await searchForm.validateFields().catch(() => ({}));
          let brandValue = searchValues.brand || '';
          
          // 如果没有指定品牌并且有选中行，使用选中行的品牌
          if (!brandValue && selectedRows.length > 0) {
            const tableBrands = [...new Set(selectedRows.map(row => row.brand))];
            if (tableBrands.length > 1) {
              brandValue = JSON.stringify(tableBrands);
            } else if (tableBrands.length === 1) {
              brandValue = tableBrands[0];
            }
          }

          const params = {
            user: initialState?.userInfo?.username || '',
            role: initialState?.userInfo?.type || '',
            brand: brandValue,
            upload_type: 'campaign_plan',
            is_maintained: isMaintained ? 'true' : 'false',
            is_all_delete: 'true'
          };

          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            ...fetchConfig,
            body: JSON.stringify(params),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          if (result.status === 'success') {
            message.success('全部数据删除成功');
            setSelectedRows([]);
            setSelectedRowKeys([]);
            if (actionRef.current?.clearSelected) {
              actionRef.current.clearSelected();
            }
            actionRef.current?.reload();
          } else {
            throw new Error(result.message || '删除全部数据失败');
          }
        } catch (error) {
          console.error('删除全部数据失败:', error);
          message.error(`删除全部数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      },
    });
  };

  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      // 检查文件类型和大小
      const isExcel =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只支持 Excel 文件');
        setImportLoading(false);
        return;
      }

      if (file.size > 50 * 1024 * 1024) {
        // 50MB
        message.error('文件大小不能超过50MB');
        setImportLoading(false);
        return;
      }

      // 构建请求参数 - 使用专用接口参数
      const params = new URLSearchParams({
        user: initialState?.userInfo?.username || '',
        upload_type: 'campaign_plan',
        role: initialState?.userInfo?.type || '',
        brand: initialState?.currentBrandInfo?.brandNameCn || ''
      }).toString();
      
      // 构建FormData
      const formData = new FormData();
      formData.append('file', file);

      // 发送请求到专用接口
      const response = await fetch(`/bims/upload_unmaintained_campaign?${params}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.status === 'success') {
        if (
          result.data?.success_count !== undefined ||
          result.data?.failure_count !== undefined
        ) {
          message.open({
            type: 'info',
            content: (
              <div>
                <div>{`成功：${result.data.success_count}条，失败：${result.data.failure_count}条`}</div>
                <div
                  style={{ fontSize: '12px', marginTop: '4px', color: '#666' }}
                >
                  可前往上传记录模块查看详情
                </div>
              </div>
            ),
            icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
          });
        } else {
          message.success('导入成功');
        }

        if (result.data?.message) {
          message.warning(result.data.message);
        }

        setImportModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error(`导入失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setImportLoading(false);
    }
  };

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    actionRef.current?.reload();
  };

  return (
    <Card>
      <Space
        direction="vertical"
        size="middle"
        style={{ width: '100%', marginBottom: 16 }}
      >
        <Space direction="vertical" size={4}>
          <Segmented
            value={isMaintained ? 'maintained' : 'unmaintained'}
            onChange={(value) => {
              setIsMaintained(value === 'maintained');
              if (actionRef.current?.reset) {
                actionRef.current.reset();
              }
              actionRef.current?.reload();
            }}
            options={[
              {
                label: '已维护',
                value: 'maintained',
              },
              {
                label: '待维护',
                value: 'unmaintained',
              },
            ]}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '8px',
              fontSize: '16px',
            }}
            block
          />
          <Typography.Text type="danger" style={{ fontSize: '13px' }}>
            已维护：展示必填项不缺失的数据信息
            <br />
            待维护：展示必填项缺失的数据信息
          </Typography.Text>
        </Space>
        
        <Form form={searchForm} layout="inline">
          <Form.Item name="brand" label="品牌">
            <Select
              style={{ width: 200 }}
              options={brandOptions}
              placeholder="请选择品牌"
              allowClear
              showSearch
              mode="multiple"
              maxTagCount={2}
              filterOption={false}
              onFocus={fetchBrandOptions}
              onDropdownVisibleChange={(open) => {
                if (open) {
                  fetchBrandOptions();
                }
              }}
            />
          </Form.Item>
          <Form.Item name="keyword" label="计划ID">
            <Input
              style={{ width: 300 }}
              placeholder="请输入计划ID"
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  searchForm.resetFields();
                  actionRef.current?.reload();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>

      <ProTable<CampaignPlanItem>
        headerTitle="投放计划管理"
        actionRef={actionRef}
        rowKey="id"
        search={false}
        options={false}
        scroll={{
          x: 'max-content',
        }}
        pagination={{
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          defaultCurrent: 1,
          defaultPageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, size) => {
            setPageSize(size);
            actionRef.current?.reload();
          },
        }}
        rowSelection={{
          selectedRowKeys,
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          preserveSelectedRowKeys: false,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
          },
        }}
        toolBarRender={() => [
          selectedRows.length > 0 && (
            <Button
              key="delete"
              danger
              onClick={handleDeleteAll}
            >
              批量删除
            </Button>
          ),
          <Button key="deleteAll" danger onClick={handleDeleteAllData}>
            删除全部
          </Button>,
          !isMaintained && (
            <Button
              key="import"
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
          ),
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={async () => {
              try {
                if (
                  selectedRows.length === 0 &&
                  actionRef.current?.pageInfo?.total === 0
                ) {
                  message.warning('暂无数据');
                  return;
                }

                const searchValues = await searchForm.validateFields();
                
                // 准备请求参数
                const requestParams: Record<string, string> = {
                  is_maintained: isMaintained ? 'true' : 'false',
                  filter_list: searchValues.brand || '',
                  custom_filter_data: searchValues.keyword || ''
                };
                
                // 如果有选中行，添加id参数
                if (selectedRows.length > 0) {
                  requestParams.id = JSON.stringify(selectedRows.map(row => row.id));
                }
                
                const params = buildRequestParams(requestParams);
                const response = await fetch(`/bims/download_data?${params}`, {
                  method: 'GET',
                  headers: {
                    Accept: '*/*',
                  },
                });

                if (!response.ok) {
                  throw new Error('下载失败');
                }

                // 获取文件名
                const contentDisposition = response.headers.get(
                  'content-disposition',
                );
                let filename = 'download.xlsx';
                if (contentDisposition) {
                  const filenameMatch = contentDisposition.match(
                    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
                  );
                  if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                  }
                }

                // 获取二进制数据并创建下载
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                message.success(
                  selectedRows.length > 0
                    ? '已选数据下载成功'
                    : '全部数据下载成功',
                );
              } catch (error) {
                console.error('Download error:', error);
                message.error(
                  '下载失败: ' +
                    (error instanceof Error ? error.message : '未知错误'),
                );
              }
            }}
          >
            {selectedRows.length > 0 ? '下载所选' : '下载全部'}
          </Button>,
        ]}
        request={async (params) => {
          const searchValues = await searchForm.validateFields().catch(() => ({}));
          const queryParams = buildRequestParams({
            ...params,
            page: params.current || 1,
            page_size: params.pageSize || 20,
            is_maintained: isMaintained ? 'true' : 'false',
            filter_list: searchValues.brand ? (Array.isArray(searchValues.brand) ? searchValues.brand : [searchValues.brand]) : '',
            custom_filter_data: searchValues.keyword || '',
          });

          try {
            const response = await fetch(
              `/bims/select_data?${queryParams}`,
              {
                method: 'GET',
                ...fetchConfig,
              },
            );

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result: SelectDataResponse = await response.json();
            if (result.status === 'success') {
              return {
                data: result.data.list || [],
                success: true,
                total: result.data.total || 0,
              };
            } else {
              message.error(result.message || '获取数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('获取数据失败:', error);
            message.error(
              `获取数据失败: ${error instanceof Error ? error.message : '未知错误'}`,
            );
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
      />

      {/* 创建模态框 */}
      <Modal
        title="新建投放计划"
        open={createModalVisible}
        onOk={() => {
          form.validateFields().then((values) => {
            // 处理创建逻辑
            setCreateModalVisible(false);
          });
        }}
        onCancel={() => setCreateModalVisible(false)}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text type="danger">注：带 * 号的为必填项（计划名称、计划ID、品牌）</Typography.Text>
        </div>
        <Form 
          form={form} 
          layout="vertical"
          style={{ maxHeight: '60vh', overflowY: 'auto' }}
        >
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item name="planName" label="计划名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="brand" label="品牌" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="planId" label="计划ID" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            {/* 添加更多表单项 */}
          </div>
        </Form>
      </Modal>

      {/* 编辑模态框 */}
      <Modal
        title="编辑投放计划"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text type="danger">注：带 * 号的为必填项（计划名称、计划ID、品牌）</Typography.Text>
        </div>
        <Form 
          form={editForm} 
          layout="vertical"
          style={{ maxHeight: '60vh', overflowY: 'auto' }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item name="plan_name" label="计划名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="brand" label="品牌" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="plan_id" label="计划ID" rules={[{ required: true }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item 
              name="sub_brand"
              label="子品牌"
              tooltip={"存有多子品牌，用\"/\"隔开或填写全品牌"}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item 
              name="product_line"
              label="品线"
              tooltip={"存有多品线，用\"/\"隔开或填写全品线"}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item 
              name="city"
              label="城市"
              tooltip={"存有多城市，用\"/\"隔开或填写全国"}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="price_type" label={createFormItemLabel("出价模式", "CPM、最大化拿量、CPC、其他")}>
              <Select 
                placeholder="请选择" 
                options={[
                  { label: 'CPM', value: 'CPM' },
                  { label: '最大化拿量', value: '最大化拿量' },
                  { label: 'CPC', value: 'CPC' },
                  { label: '其他', value: '其他' },
                ]}
              />
            </Form.Item>
            <Form.Item name="budget" label="日预算">
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="cost_type" label="费用类型">
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="plan_type" label={createFormItemLabel("计划类型", "GMV提升、渠道加强、洞察人群、拉新客、老客促活")}>
              <Select 
                placeholder="请选择" 
                options={[
                  { label: 'GMV提升', value: 'GMV提升' },
                  { label: '渠道加强', value: '渠道加强' },
                  { label: '洞察人群', value: '洞察人群' },
                  { label: '拉新客', value: '拉新客' },
                  { label: '老客促活', value: '老客促活' },
                ]}
              />
            </Form.Item>
            <Form.Item name="delivery_scene" label="投放场景">
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="delivery_audience" label="投放人群">
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item 
              name="channel_orientation"
              label="渠道定向"
              tooltip={"存有多渠道，用\"/\"隔开或无"}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="one_party_crowd" label={createFormItemLabel("一方人群", "是或否")}>
              <Select 
                placeholder="请选择" 
                options={[
                  { label: '是', value: '是' },
                  { label: '否', value: '否' },
                ]}
              />
            </Form.Item>
            <Form.Item name="precise_coupon" label={createFormItemLabel("精准配券", "是或否")}>
              <Select 
                placeholder="请选择" 
                options={[
                  { label: '是', value: '是' },
                  { label: '否', value: '否' },
                ]}
              />
            </Form.Item>
            <Form.Item name="delivery_time_type" label={createFormItemLabel("投放时段类型", "周中或周末或节假日或通投")}>
              <Select 
                placeholder="请选择" 
                options={[
                  { label: '周中', value: '周中' },
                  { label: '周末', value: '周末' },
                  { label: '节假日', value: '节假日' },
                  { label: '通投', value: '通投' },
                ]}
              />
            </Form.Item>
            <Form.Item name="delivery_time" label={createFormItemLabel("投放时段", "输入格式需遵循 \"HHMM - HHMM\" 的标准格式，示例为 \"1600 - 1800\"")}>
              <Input placeholder="请输入，示例：1600 - 1800" />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 导入模态框 */}
      <Modal
        title="导入投放计划"
        open={importModalVisible}
        footer={null}
        onCancel={() => setImportModalVisible(false)}
      >
        <Spin spinning={importLoading}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Upload.Dragger
              name="file"
              accept=".xlsx,.xls"
              showUploadList={false}
              beforeUpload={(file) => {
                handleImport(file);
                return false;
              }}
              disabled={importLoading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持 .xlsx, .xls 格式的 Excel 文件
              </p>
              {importLoading && (
                <div style={{ marginTop: 8 }}>
                  <Typography.Text type="secondary">
                    <Space>正在导入中，请稍候...</Space>
                  </Typography.Text>
                </div>
              )}
            </Upload.Dragger>
          </Space>
        </Spin>
      </Modal>
    </Card>
  );
} 