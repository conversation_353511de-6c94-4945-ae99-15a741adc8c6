export interface CampaignPlanItem {
  id: string;
  date: string;
  brand: string;
  planId: string;
  planName: string;
  subBrandType: string;
  budget: string;
  bidType: string;
  bidAmount: string;
  cost: string;
  impression: string;
  click: string;
  ctr: string;
  cpc: string;
  cpm: string;
  newCustomerCount: string;
  newCustomerCost: string;
  newCustomerLongTermRoi: string;
  oldCustomerCount: string;
  activationCost: string;
  oldCustomerLongTermRoi: string;
  oldCustomerRepurchaseFreq: string;
  oldCustomerARPU: string;
  t1DirectOrderCount: string;
  t1DirectSaleAmount: string;
  t1DirectConversionRate: string;
  t1DirectROI: string;
  t1TotalOrderCount: string;
  t1TotalSaleAmount: string;
  planType: string;
  cityTargeting: string;
  bidMode: string;
  dailyBudget: string;
  costTarget: string;
  campaignType: string;
  campaignScene: string;
  targetAudience: string;
  channelTargeting: string;
  firstPartyAudience: string;
  preciseVoucher: string;
  scheduleType: string;
  schedule: string;
}

export interface GetListResponse {
  status: string;
  message?: string;
  data: {
    brands: Array<{ brand: string }>;
    subBrands: Array<{ subBrand: string }>;
  };
}

export interface SelectDataResponse {
  status: string;
  message?: string;
  data: {
    list: CampaignPlanItem[];
    total: number;
  };
} 