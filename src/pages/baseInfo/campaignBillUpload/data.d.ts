export interface CampaignBillUploadItem {
  id: number;
  original_id: string;
  file_name: string;
  file_size: string;
  campaign_period: string;
  bill_type: 'media_cost' | 'service_fee' | 'promotion_cost' | 'other_cost';
  status: 'processing' | 'success' | 'partial_success' | 'failed';
  upload_date: string;
  uploader: string;
  total_amount: number;
  total_records: number;
  success_records: number;
  failed_records: number;
  error_message?: string;
  brand: string;
  campaign_start: string;
  campaign_end: string;
}

export interface CampaignBillDetail {
  id: number;
  campaign_name: string;
  media_platform: string;
  cost_type: string;
  amount: number;
  date: string;
  description?: string;
}

export interface CampaignBillUploadResponse {
  status: string;
  message?: string;
  data: {
    upload_id: string;
    file_name: string;
    total_amount: number;
    total_records: number;
    success_records: number;
    failed_records: number;
  };
}

export interface GetCampaignBillHistoryResponse {
  status: string;
  message?: string;
  data: {
    list: CampaignBillUploadItem[];
    total: number;
  };
}

export interface GetCampaignBillDetailsResponse {
  status: string;
  message?: string;
  data: {
    upload_info: CampaignBillUploadItem;
    details: CampaignBillDetail[];
  };
} 