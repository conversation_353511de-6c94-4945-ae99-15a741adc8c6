import { useEffect, useRef, useState } from 'react';

import {
  AuditOutlined,
  DownloadOutlined,
  InboxOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Progress,
  Select,
  Space,
  Spin,
  Table,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadProps } from 'antd';

import './index.less';

const { Dragger } = Upload;
const { Option } = Select;

export default function CampaignBillUpload() {
  const { initialState } = useModel('@@initialState');
  const [approvalForm] = Form.useForm();

  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [canUpload, setCanUpload] = useState(false);
  const [approvalMessage, setApprovalMessage] = useState('');
  const [refreshingApproval, setRefreshingApproval] = useState(false);

  // 获取平台对应的表格配置
  const getPlatformTableConfig = () => {
    if (!selectedPlatform) {
      return { columns: [], sampleData: [] };
    }

    switch (selectedPlatform) {
      case 'meituan':
        return {
          columns: [
            {
              title: '*结算主体',
              dataIndex: 'settlementEntity',
              key: 'settlementEntity',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*活动来源',
              dataIndex: 'activitySource',
              key: 'activitySource',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*活动类型',
              dataIndex: 'activityType',
              key: 'activityType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*活动名称',
              dataIndex: 'activityName',
              key: 'activityName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*招商方案名称',
              dataIndex: 'merchantScheme',
              key: 'merchantScheme',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*档期活动名称',
              dataIndex: 'periodActivity',
              key: 'periodActivity',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*下单日期',
              dataIndex: 'orderDate',
              key: 'orderDate',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*下单时间',
              dataIndex: 'orderTime',
              key: 'orderTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*结算时间',
              dataIndex: 'settlementTime',
              key: 'settlementTime',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单ID',
              dataIndex: 'orderId',
              key: 'orderId',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*结算类型',
              dataIndex: 'settlementType',
              key: 'settlementType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*门店名称',
              dataIndex: 'storeName',
              key: 'storeName',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*渠道',
              dataIndex: 'channel',
              key: 'channel',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*优惠券门槛（满）',
              dataIndex: 'couponThreshold',
              key: 'couponThreshold',
              width: 130,
              align: 'center' as const,
            },
            {
              title: '*优惠券面额（减）',
              dataIndex: 'couponAmount',
              key: 'couponAmount',
              width: 130,
              align: 'center' as const,
            },
            {
              title: '*品牌商名称',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品条码',
              dataIndex: 'productBarcode',
              key: 'productBarcode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品原价',
              dataIndex: 'originalPrice',
              key: 'originalPrice',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*优惠分摊销量',
              dataIndex: 'discountQuantity',
              key: 'discountQuantity',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品订单销量',
              dataIndex: 'orderQuantity',
              key: 'orderQuantity',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌补贴金额（未税）',
              dataIndex: 'subsidyAmountNoTax',
              key: 'subsidyAmountNoTax',
              width: 160,
              align: 'center' as const,
            },
            {
              title: '*品牌补贴金额（含税）',
              dataIndex: 'subsidyAmountWithTax',
              key: 'subsidyAmountWithTax',
              width: 160,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              settlementEntity: '示例组织名称',
              activitySource: '闪购品牌提报',
              activityType: '商品券',
              activityName: '大牌零食联合券',
              merchantScheme: '（休食-超市便利KA加强）25年6月新系统',
              periodActivity: '休食品牌联合-周末档',
              orderDate: '2025-06-22',
              orderTime: '2025-06-22 23:28:51',
              settlementTime: '2025-06-23',
              orderId: '2101654520869672529',
              settlementType: '正向',
              province: '福建省',
              city: '宁德市',
              storeName: '万嘉便利（宁德澳风大厦店）',
              channel: '万嘉便利',
              couponThreshold: '59.0',
              couponAmount: '30.0',
              brandName: '示例品牌商',
              productBarcode: '6924743920000',
              productName: '示例商品名称',
              originalPrice: '15.80',
              discountQuantity: '2',
              orderQuantity: '2',
              subsidyAmountNoTax: '3.99',
              subsidyAmountWithTax: '4.229400',
            },
          ],
        };

      case 'eleme':
        return {
          columns: [
            {
              title: '*品牌商Id',
              dataIndex: 'brandId',
              key: 'brandId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌名称',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*活动ID',
              dataIndex: 'activityId',
              key: 'activityId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*活动名称',
              dataIndex: 'activityName',
              key: 'activityName',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*订单来源',
              dataIndex: 'orderSource',
              key: 'orderSource',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单时间',
              dataIndex: 'orderTime',
              key: 'orderTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*饿了么订单号',
              dataIndex: 'elemeOrderNo',
              key: 'elemeOrderNo',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*业务类型',
              dataIndex: 'businessType',
              key: 'businessType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单类型',
              dataIndex: 'orderType',
              key: 'orderType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*结算类型',
              dataIndex: 'settlementType',
              key: 'settlementType',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商户名称',
              dataIndex: 'merchantName',
              key: 'merchantName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*供应商名称',
              dataIndex: 'supplierName',
              key: 'supplierName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*sku_id',
              dataIndex: 'skuId',
              key: 'skuId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*upc_code',
              dataIndex: 'upcCode',
              key: 'upcCode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*是否组套品',
              dataIndex: 'isBundle',
              key: 'isBundle',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品单价',
              dataIndex: 'unitPrice',
              key: 'unitPrice',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品数量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌单品补贴',
              dataIndex: 'brandSubsidy',
              key: 'brandSubsidy',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌补贴总额',
              dataIndex: 'totalSubsidy',
              key: 'totalSubsidy',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品原价总额',
              dataIndex: 'originalTotal',
              key: 'originalTotal',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*活动类型名称',
              dataIndex: 'activityTypeName',
              key: 'activityTypeName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*业务发生时间',
              dataIndex: 'businessTime',
              key: 'businessTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*账单时间',
              dataIndex: 'billTime',
              key: 'billTime',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*核销状态',
              dataIndex: 'verificationStatus',
              key: 'verificationStatus',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*核销金额(元)',
              dataIndex: 'verificationAmount',
              key: 'verificationAmount',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*核销时间',
              dataIndex: 'verificationTime',
              key: 'verificationTime',
              width: 150,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              brandId: '示例品牌商Id',
              brandName: '示例品牌商',
              activityId: '6000002442160000',
              activityName: '【超便】5月全国49减15',
              orderSource: '到家零售',
              orderTime: '2025-05-20 16:10:39',
              elemeOrderNo: '4078020192933630000',
              businessType: '外卖订单',
              orderType: '逆向',
              settlementType: '营销账单-线上结算',
              merchantName: '大润发(东海店)',
              supplierName: '华东大润发',
              province: '浙江省',
              city: '台州市',
              skuId: '1727262364220000',
              upcCode: '6936430810000',
              isBundle: '否',
              unitPrice: '33.9',
              quantity: '-1',
              brandSubsidy: '-3.32',
              totalSubsidy: '-3.32',
              originalTotal: '-33.9',
              activityTypeName: '超级品牌券',
              businessTime: '2025-05-20 18:25:33',
              billTime: '20250521',
              verificationStatus: '已结算',
              verificationAmount: '-3.32',
              verificationTime: '2025-05-22 03:00:13',
            },
          ],
        };

      case 'jingdong':
        return {
          columns: [
            {
              title: <span style={{ color: '#ff4d4f' }}>*账单月份</span>,
              dataIndex: 'billMonth',
              key: 'billMonth',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单完成时间',
              dataIndex: 'orderCompleteTime',
              key: 'orderCompleteTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单号ID',
              dataIndex: 'orderId',
              key: 'orderId',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*营销订单id',
              dataIndex: 'marketingOrderId',
              key: 'marketingOrderId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单创建时间',
              dataIndex: 'orderCreateTime',
              key: 'orderCreateTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单总金额',
              dataIndex: 'orderTotalAmount',
              key: 'orderTotalAmount',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*订单实际支付金额',
              dataIndex: 'actualPayAmount',
              key: 'actualPayAmount',
              width: 140,
              align: 'center' as const,
            },
            {
              title: '*订单城市',
              dataIndex: 'orderCity',
              key: 'orderCity',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*K1',
              dataIndex: 'k1',
              key: 'k1',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*K2',
              dataIndex: 'k2',
              key: 'k2',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*档期名称',
              dataIndex: 'periodName',
              key: 'periodName',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*提报方案名称',
              dataIndex: 'proposalName',
              key: 'proposalName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*补贴名称',
              dataIndex: 'subsidyName',
              key: 'subsidyName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*补贴类型',
              dataIndex: 'subsidyType',
              key: 'subsidyType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*补贴门槛',
              dataIndex: 'subsidyThreshold',
              key: 'subsidyThreshold',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*补贴力度',
              dataIndex: 'subsidyAmount',
              key: 'subsidyAmount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌名称',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品SKU',
              dataIndex: 'productSku',
              key: 'productSku',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品SKU名称',
              dataIndex: 'skuName',
              key: 'skuName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品UPC',
              dataIndex: 'productUpc',
              key: 'productUpc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品UPC名称',
              dataIndex: 'upcName',
              key: 'upcName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品子品牌名称',
              dataIndex: 'subBrandName',
              key: 'subBrandName',
              width: 130,
              align: 'center' as const,
            },
            {
              title: '*商品售卖价',
              dataIndex: 'sellPrice',
              key: 'sellPrice',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*实际价格',
              dataIndex: 'actualPrice',
              key: 'actualPrice',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*实际享受补贴个数',
              dataIndex: 'actualSubsidyCount',
              key: 'actualSubsidyCount',
              width: 140,
              align: 'center' as const,
            },
            {
              title: '*商品GOV',
              dataIndex: 'productGov',
              key: 'productGov',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌分摊比例',
              dataIndex: 'brandRatio',
              key: 'brandRatio',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌计费金额-未税',
              dataIndex: 'brandAmountNoTax',
              key: 'brandAmountNoTax',
              width: 140,
              align: 'center' as const,
            },
            {
              title: '*品牌计费金额-含税',
              dataIndex: 'brandAmountWithTax',
              key: 'brandAmountWithTax',
              width: 140,
              align: 'center' as const,
            },
            {
              title: '*税额（元）',
              dataIndex: 'taxAmount',
              key: 'taxAmount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*提报秒杀价',
              dataIndex: 'proposalPrice',
              key: 'proposalPrice',
              width: 120,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              billMonth: '202505',
              orderCompleteTime: '2025-05-01 07:24:00',
              orderId:
                'qsh61oYykZO5J2A67KDyWbyNE5++cHjamfKssQ94NEx9bk5bM3oVPmImsldP2YsV',
              marketingOrderId: 'ORDER_0V2OK2LDYFDYJ3MN',
              orderCreateTime: '2025-05-01 06:17:24',
              orderTotalAmount: '149',
              actualPayAmount: '110.2',
              orderCity: '莆田市',
              k1: 'A2D3A4CA7EDD6CDFEB5BDAC355E6336E',
              k2: '08404D15B09BEFE6C77A8513DEB3C9E9',
              periodName: '【2025年-品类发展组】常规5月',
              proposalName: '5月品牌券',
              subsidyName: '30409202505永辉99-20',
              subsidyType: '优惠券',
              subsidyThreshold: '99',
              subsidyAmount: '20',
              brandName: '示例品牌商',
              productSku: '2204520000',
              skuName: '示例SKU名称',
              productUpc: '6936430820000',
              upcName: '示例UPC名称',
              subBrandName: '示例子品牌',
              sellPrice: '29.8',
              actualPrice: '29.8',
              actualSubsidyCount: '5',
              productGov: '149',
              brandRatio: '100',
              brandAmountNoTax: '20',
              brandAmountWithTax: '21.2',
              taxAmount: '1.2',
              proposalPrice: '0',
            },
          ],
        };

      case 'duodian':
        return {
          columns: [
            {
              title: <span style={{ color: '#ff4d4f' }}>*账单月份</span>,
              dataIndex: 'billMonth',
              key: 'billMonth',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*skuId',
              dataIndex: 'skuId',
              key: 'skuId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商家id',
              dataIndex: 'merchantId',
              key: 'merchantId',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商家名称',
              dataIndex: 'merchantName',
              key: 'merchantName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*承担方id',
              dataIndex: 'bearerId',
              key: 'bearerId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*承担方名称',
              dataIndex: 'bearerName',
              key: 'bearerName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*门店id',
              dataIndex: 'storeId',
              key: 'storeId',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*门店名称',
              dataIndex: 'storeName',
              key: 'storeName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*用户id',
              dataIndex: 'userId',
              key: 'userId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*订单id',
              dataIndex: 'orderId',
              key: 'orderId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单状态',
              dataIndex: 'orderStatus',
              key: 'orderStatus',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单创建时间',
              dataIndex: 'orderCreateTime',
              key: 'orderCreateTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单完成时间',
              dataIndex: 'orderCompleteTime',
              key: 'orderCompleteTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*结算类型',
              dataIndex: 'settlementType',
              key: 'settlementType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品类型',
              dataIndex: 'productType',
              key: 'productType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*国条码',
              dataIndex: 'barcode',
              key: 'barcode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*物料码',
              dataIndex: 'materialCode',
              key: 'materialCode',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品销售数量',
              dataIndex: 'salesQuantity',
              key: 'salesQuantity',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*单价',
              dataIndex: 'unitPrice',
              key: 'unitPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*原价',
              dataIndex: 'originalPrice',
              key: 'originalPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*促销金额',
              dataIndex: 'promotionAmount',
              key: 'promotionAmount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*true GMV',
              dataIndex: 'trueGmv',
              key: 'trueGmv',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*优惠券批次id',
              dataIndex: 'couponBatchId',
              key: 'couponBatchId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*优惠券批次名称',
              dataIndex: 'couponBatchName',
              key: 'couponBatchName',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*优惠券批次描述',
              dataIndex: 'couponBatchDesc',
              key: 'couponBatchDesc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*优惠券金额',
              dataIndex: 'couponAmount',
              key: 'couponAmount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*优惠券类型',
              dataIndex: 'couponType',
              key: 'couponType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*活动id',
              dataIndex: 'activityId',
              key: 'activityId',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*承担比例（%）',
              dataIndex: 'bearRatio',
              key: 'bearRatio',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*承担金额未税',
              dataIndex: 'bearAmountNoTax',
              key: 'bearAmountNoTax',
              width: 120,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              billMonth: '202502',
              skuId: '*********',
              merchantId: '1',
              merchantName: '北京物美',
              bearerId: '示例品牌商Id',
              bearerName: '示例品牌商',
              storeId: '339',
              storeName: '三旗百汇店',
              userId: '140000',
              orderId: '************',
              orderStatus: '1024',
              orderCreateTime: '2025-02-24 19:41:38',
              orderCompleteTime: '2025-02-24 20:16:20',
              settlementType: 'O2O订单',
              productType: '标品',
              barcode: '6907376820000',
              materialCode: '834380',
              productName: '示例商品名称',
              salesQuantity: '3',
              unitPrice: '21.90',
              originalPrice: '65.70',
              promotionAmount: '0.00',
              trueGmv: '65.70',
              couponBatchId: '5500561',
              couponBatchName:
                '38档-品牌联合75折券（满59元可用，最高优惠100元）',
              couponBatchDesc: '7.5折券',
              couponAmount: '16.43',
              couponType: '折扣券',
              city: '北京城区',
              activityId: '5893001',
              bearRatio: '100',
              bearAmountNoTax: '16.43',
            },
          ],
        };

      case 'taoxianda':
        return {
          columns: [
            {
              title: '*账单日期',
              dataIndex: 'billDate',
              key: 'billDate',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*二级作业实体ID',
              dataIndex: 'entityId',
              key: 'entityId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*二级作业实体名称',
              dataIndex: 'entityName',
              key: 'entityName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*活动ID',
              dataIndex: 'activityId',
              key: 'activityId',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*活动名称',
              dataIndex: 'activityName',
              key: 'activityName',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*资源位ID',
              dataIndex: 'resourceId',
              key: 'resourceId',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*资源位名称',
              dataIndex: 'resourceName',
              key: 'resourceName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*渠道',
              dataIndex: 'channel',
              key: 'channel',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*订单下单时间',
              dataIndex: 'orderTime',
              key: 'orderTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*交易主单号',
              dataIndex: 'mainOrderNo',
              key: 'mainOrderNo',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*交易子单号',
              dataIndex: 'subOrderNo',
              key: 'subOrderNo',
              width: 180,
              align: 'center' as const,
            },
            {
              title: '*业务类型',
              dataIndex: 'businessType',
              key: 'businessType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*订单类型',
              dataIndex: 'orderType',
              key: 'orderType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*结算类型',
              dataIndex: 'settlementType',
              key: 'settlementType',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商户名称',
              dataIndex: 'merchantName',
              key: 'merchantName',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*sku code',
              dataIndex: 'skuCode',
              key: 'skuCode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*条形码',
              dataIndex: 'barcode',
              key: 'barcode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品单价',
              dataIndex: 'unitPrice',
              key: 'unitPrice',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品原价总额',
              dataIndex: 'originalTotal',
              key: 'originalTotal',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品数量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*补贴总额',
              dataIndex: 'subsidyTotal',
              key: 'subsidyTotal',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*活动类型名称',
              dataIndex: 'activityTypeName',
              key: 'activityTypeName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*业务发生时间',
              dataIndex: 'businessTime',
              key: 'businessTime',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*开票类型',
              dataIndex: 'invoiceType',
              key: 'invoiceType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*参与公司code',
              dataIndex: 'companyCode',
              key: 'companyCode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*参与公司名称',
              dataIndex: 'companyName',
              key: 'companyName',
              width: 150,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              billDate: '20250531',
              entityId: '1000000000310000',
              entityName: '示例组织名称',
              activityId: '204250430242780000',
              activityName: '5月淘鲜达品牌券（除大润发）',
              resourceId: '603',
              resourceName: '单品牌满减券2023',
              channel: '淘宝买菜｜小时到家',
              orderTime: '2025-05-31 14:54:38',
              mainOrderNo: '436404816148410000',
              subOrderNo: '4364048161486140000',
              businessType: '淘宝小时达',
              orderType: '正向',
              settlementType: '单品牌满减券',
              merchantName: '永辉台州市仙居新城吾悦店',
              province: '浙江省',
              city: '台州市',
              skuCode: 'R-750000',
              barcode: '6924766600000',
              unitPrice: '27.8',
              originalTotal: '55.6',
              quantity: '2',
              subsidyTotal: '7',
              activityTypeName: '淘鲜达新商品券',
              businessTime: '2025-05-31 14:55:02',
              invoiceType: '推广服务费',
              companyCode: 'YHZJ001',
              companyName: '永辉浙江分公司',
            },
          ],
        };

      default:
        return { columns: [], sampleData: [] };
    }
  };

  const { columns, sampleData } = getPlatformTableConfig() as {
    columns: any[];
    sampleData: any[];
  };

  // 平台选项
  const platformOptions = [
    { label: '美团', value: 'meituan' },
    { label: '饿了么', value: 'eleme' },
    { label: '京东到家', value: 'jingdong' },
    { label: '多点', value: 'duodian' },
    { label: '淘鲜达', value: 'taoxianda' },
  ];

  // 数据源类型选项
  const dataTypeOptions = [
    { label: '新增数据源', value: 'add' },
    { label: '替换数据源', value: 'replace' },
  ];

  // 获取原因选项
  const getReasonOptions = () => {
    if (selectedDataType === 'add') {
      return [
        { label: '平台无下载权限', value: 'no_download_permission' },
        { label: '有下载权限，无法分享', value: 'cannot_share' },
        { label: '其他原因', value: 'other_add' },
      ];
    } else if (selectedDataType === 'replace') {
      const baseOptions = [
        { label: '平台刷数', value: 'platform_refresh' },
        { label: '品牌方更改数据源', value: 'brand_change_source' },
        { label: '其他原因', value: 'other_replace' },
      ];
      // 如果不是美团或饿了么，添加档期活动账单选项
      if (!['meituan', 'eleme'].includes(selectedPlatform)) {
        baseOptions.splice(2, 0, {
          label: '档期活动账单，按结算月替换',
          value: 'campaign_settlement',
        });
      }
      return baseOptions;
    }
    return [];
  };

  // 添加通用请求配置
  const fetchConfig = {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    mode: 'cors' as RequestMode,
  };

  // 构建请求参数
  const buildRequestParams = (additionalParams: Record<string, any> = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: initialState?.currentBrandInfo?.brandNameCn || '',
      upload_type: 'campaign_bill',
      ...additionalParams,
    };

    return Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
      .join('&');
  };

  // 检查用户审批状态
  const checkUserApproval = async () => {
    setRefreshingApproval(true);
    try {
      const params = buildRequestParams({
        platform:
          platformOptions.find((p) => p.value === selectedPlatform)?.label ||
          '',
      });

      const response = await fetch(`/bims/check_user_approval?${params}`, {
        method: 'GET',
        ...fetchConfig,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        const canUpload = result.data?.can_upload || false;
        setCanUpload(canUpload);
        setApprovalMessage(result.data?.message || result.message || '');

        if (canUpload) {
          message.success('审批状态已更新');
        } else {
          message.info(
            result.data?.message || result.message || '审批状态已更新',
          );
        }
      }
    } catch (error) {
      console.error('检查审批状态失败:', error);
      message.error('获取审批状态失败');
      setCanUpload(false);
    } finally {
      setRefreshingApproval(false);
    }
  };

  useEffect(() => {
    // 首次检查审批状态
    checkUserApproval();
  }, []);

  // 上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file) => {
      if (!selectedPlatform) {
        message.error('请先选择平台！');
        return false;
      }
      if (!selectedDataType) {
        message.error('请先选择数据源类型！');
        return false;
      }
      if (!selectedReason) {
        message.error('请先选择原因！');
        return false;
      }

      const isValidType =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'text/csv';
      if (!isValidType) {
        message.error('只支持上传 Excel 文件 (.xlsx, .xls) 或 CSV 文件！');
        return false;
      }
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件大小不能超过 100MB！');
        return false;
      }
      return true;
    },
    customRequest: async ({ file, onProgress, onSuccess, onError }) => {
      try {
        setUploading(true);
        const formData = new FormData();
        formData.append('file', file as File);
        formData.append('upload_type', 'campaign_bill');

        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(percent);
            onProgress?.({ percent });
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.status === 'success') {
              // 如果有message且包含错误信息，显示message；否则显示成功消息
              if (
                response.message &&
                (response.message.includes('数据有误') ||
                  response.message.includes('失败'))
              ) {
                message.warning(response.message);
              } else {
                message.success('活动账单上传成功！');
              }
              onSuccess?.(response);
              setTimeout(() => {
                setSelectedPlatform('');
                setSelectedDataType('');
                setSelectedReason('');
              }, 1500);
            } else {
              message.error(response.message || '上传失败');
              onError?.(new Error(response.message));
            }
          } else {
            message.error('上传失败');
            onError?.(new Error('Upload failed'));
          }
          setUploading(false);
          setUploadProgress(0);
          setUploadModalVisible(false);
        });

        xhr.addEventListener('error', () => {
          message.error('上传失败');
          onError?.(new Error('Upload failed'));
          setUploading(false);
          setUploadProgress(0);
          setUploadModalVisible(false);
        });

        const platformLabel =
          platformOptions.find((p) => p.value === selectedPlatform)?.label ||
          '';
        const dataTypeLabel =
          dataTypeOptions.find((d) => d.value === selectedDataType)?.label ||
          '';
        const reasonLabel =
          getReasonOptions().find((r) => r.value === selectedReason)?.label ||
          '';
        const params = new URLSearchParams({
          user: initialState?.userInfo?.username || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          platform: platformLabel,
          data_type: dataTypeLabel,
          reason: reasonLabel,
        });

        xhr.open('POST', `/bims/upload_campaign_bill?${params.toString()}`);
        xhr.send(formData);
      } catch (error) {
        message.error('上传失败');
        onError?.(error as Error);
        setUploading(false);
        setUploadProgress(0);
        setUploadModalVisible(false);
      }
    },
    showUploadList: false,
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    if (!selectedPlatform) {
      message.error('请先选择平台！');
      return;
    }

    try {
      const platformLabel =
        platformOptions.find((p) => p.value === selectedPlatform)?.label || '';
      const params = new URLSearchParams({
        platform: platformLabel,
      });

      const response = await fetch(
        `/bims/download_activity_bill_template?${params}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成与上传成功文件相同格式的文件名
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
      const fileName = `campaign_bill_template_${platformLabel}_${dateStr}_${timeStr}.xlsx`;

      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('模板下载成功');
    } catch (error) {
      message.error('模板下载失败');
    }
  };

  // 审批处理
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();

      const response = await fetch('/bims/submit_approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: initialState?.userInfo?.username || '',
          role: initialState?.userInfo?.type || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          upload_type: 'campaign_bill',
          platform:
            platformOptions.find((p) => p.value === selectedPlatform)?.label ||
            '',
          data_type:
            dataTypeOptions.find((d) => d.value === selectedDataType)?.label ||
            '',
          reason:
            getReasonOptions().find((r) => r.value === selectedReason)?.label ||
            '',
          applicant: values.applicant,
          upload_info_type: values.upload_info_type,
          reason_description: values.reason,
        }),
      });

      const result = await response.json();
      if (result.status === 'success') {
        message.success('审批提交成功');
        setApprovalModalVisible(false);
        approvalForm.resetFields();
      } else {
        message.error(result.message || '审批提交失败');
      }
    } catch (error) {
      message.error('审批提交失败');
    }
  };

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <Form layout="inline" style={{ marginBottom: 16 }}>
          <Form.Item label="平台" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择平台"
              value={selectedPlatform}
              onChange={(value) => setSelectedPlatform(value)}
              style={{ width: 200 }}
              allowClear
            >
              {platformOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="数据源类型" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择数据源类型"
              value={selectedDataType}
              onChange={(value) => {
                setSelectedDataType(value);
                setSelectedReason(''); // 重置原因选择
              }}
              style={{ width: 200 }}
              allowClear
            >
              {dataTypeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="原因" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择原因"
              value={selectedReason}
              onChange={(value) => setSelectedReason(value)}
              style={{ width: 250 }}
              disabled={!selectedDataType}
              allowClear
            >
              {getReasonOptions().map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Space>

      {/* Excel数据展示区域 */}
      <Card
        title="Excel导入示例"
        style={{ marginTop: 16 }}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<AuditOutlined />}
              onClick={() => {
                const reasonLabel =
                  getReasonOptions().find((r) => r.value === selectedReason)
                    ?.label || '';
                const dataTypeLabel =
                  dataTypeOptions.find((d) => d.value === selectedDataType)
                    ?.label || '';
                approvalForm.setFieldsValue({
                  reason: `${reasonLabel}，`,
                  upload_info_type: `活动账单数据${dataTypeLabel}`,
                });
                setApprovalModalVisible(true);
              }}
              disabled={
                !selectedPlatform || !selectedDataType || !selectedReason
              }
            >
              审批
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={checkUserApproval}
              loading={refreshingApproval}
              title="刷新审批状态"
            >
              刷新状态
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
              disabled={!selectedPlatform}
              title={!selectedPlatform ? '请先选择平台' : ''}
            >
              下载模板
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              disabled={
                !canUpload ||
                !selectedPlatform ||
                !selectedDataType ||
                !selectedReason
              }
              title={!canUpload ? approvalMessage : ''}
            >
              导入
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Text type="secondary">
              *每次导入的数据不能超过100万行，超过部分格被截断，请缩小筛选范国后导入
            </Typography.Text>
            <Typography.Text type="secondary">*必填</Typography.Text>
            <Typography.Text type="secondary">
              *导入前检查格式，平台下载格式导入
            </Typography.Text>
          </Space>
        </div>

        {selectedPlatform ? (
          <Table
            columns={columns}
            dataSource={sampleData}
            pagination={false}
            scroll={{ x: 'max-content' }}
            size="small"
            bordered
            style={{
              backgroundColor: '#f8f9fa',
            }}
          />
        ) : (
          <div
            style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
          >
            请先选择平台查看相应的数据格式
          </div>
        )}
      </Card>

      <Modal
        title="导入活动账单"
        open={uploadModalVisible}
        onCancel={() => {
          if (!uploading) {
            setUploadModalVisible(false);
            setUploadProgress(0);
          }
        }}
        footer={null}
        closable={!uploading}
        maskClosable={!uploading}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {uploading ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin tip="正在导入..." />
              <Progress
                percent={uploadProgress}
                status="active"
                style={{ marginTop: 16 }}
              />
            </div>
          ) : (
            <Upload.Dragger {...uploadProps} accept=".xls,.xlsx,.csv">
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">将文件拖到此处，或点击选择文件</p>
              <p className="ant-upload-hint">
                请上传小于等于100M的.xls, .xlsx, .csv格式文件
              </p>
            </Upload.Dragger>
          )}
        </Space>
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title="维表审批申请"
        open={approvalModalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人姓名" />
          </Form.Item>

          <Form.Item
            name="upload_info_type"
            label="上传类型"
            initialValue={
              dataTypeOptions.find((d) => d.value === selectedDataType)?.label
                ? `活动账单数据${dataTypeOptions.find((d) => d.value === selectedDataType)?.label}`
                : ''
            }
          >
            <Input disabled style={{ backgroundColor: '#f5f5f5' }} />
          </Form.Item>

          <Form.Item
            name="reason"
            label="上传信息说明"
            rules={[{ required: true, message: '请输入上传信息说明' }]}
          >
            <Input.TextArea placeholder="请输入上传信息说明" rows={4} />
          </Form.Item>

          <div
            style={{
              marginTop: 16,
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
            }}
          >
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              请详细描述本次上传活动账单数据的原因，新增或者替换。若涉及替换明确描述替换内容。
              <br />
              其余备注：维表上传后，默认回刷 2024 年 1 月 1
              日至今的数据。若需回刷其他日期范围的数据，请在此备注说明
            </Typography.Text>
          </div>
        </Form>
      </Modal>
    </Card>
  );
}
