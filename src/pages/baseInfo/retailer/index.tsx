import { useEffect, useRef, useState } from 'react';

import {
  AuditOutlined,
  DownloadOutlined,
  InboxOutlined,
  InfoCircleFilled,
  QuestionCircleOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { CheckCircleFilled } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Segmented,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';

import type { RetailerItem } from './data.d';

export default function RetailerManagement() {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [approvalForm] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState<RetailerItem[]>([]);
  const [platformOptions, setPlatformOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [isMaintained, setIsMaintained] = useState(true);
  const [pageSize, setPageSize] = useState<number>(20);
  const [canUpload, setCanUpload] = useState(false);
  const [approvalMessage, setApprovalMessage] = useState('');
  const [refreshingApproval, setRefreshingApproval] = useState(false);

  // 构建请求参数
  const buildRequestParams = (additionalParams = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: initialState?.currentBrandInfo?.brandNameCn || '',
      upload_type: 'retailer',
      ...additionalParams,
    };

    return Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
  };

  // 获取平台选项
  const fetchPlatformOptions = async (searchText: string) => {
    try {
      const searchValues = await searchForm.validateFields();
      const params = buildRequestParams({
        is_maintained: isMaintained ? 'true' : 'false',
        filter_list: searchText || '',
        custom_filter_data: searchValues.searchText || '',
      });
      const response = await fetch(`/bims/get_list?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'success' && result.data.platforms) {
          // 确保platforms是字符串数组
          const platforms = Array.isArray(result.data.platforms)
            ? result.data.platforms.map(
                (platform: { platform: string } | string) =>
                  typeof platform === 'object' ? platform.platform : platform,
              )
            : [];

          const options = platforms
            .filter(
              (platform: string) =>
                !searchText ||
                platform.toLowerCase().includes(searchText.toLowerCase()),
            )
            .map((platform: string) => ({
              value: platform,
              label: platform,
            }));
          setPlatformOptions(options);
        }
      }
    } catch (error) {
      console.error('获取平台选项失败:', error);
    }
  };

  // 检查用户审批状态
  const checkUserApproval = async (showMessage = false) => {
    setRefreshingApproval(true);
    try {
      const params = buildRequestParams({
        upload_type: 'retailer',
      });

      const response = await fetch(`/bims/check_user_approval?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        mode: 'cors',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        const canUpload = result.data?.can_upload || false;
        setCanUpload(canUpload);
        setApprovalMessage(result.data?.message || result.message || '');

        if (showMessage) {
          if (canUpload) {
            message.success('审批状态已更新');
          } else {
            message.info(
              result.data?.message || result.message || '审批状态已更新',
            );
          }
        }
      }
    } catch (error) {
      console.error('检查审批状态失败:', error);
      if (showMessage) {
        message.error('获取审批状态失败');
      }
      setCanUpload(false);
    } finally {
      setRefreshingApproval(false);
    }
  };

  useEffect(() => {
    // 首次检查审批状态
    checkUserApproval();
  }, []);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    actionRef.current?.reload();
  };

  // 处理审批提交
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();

      // 构建请求数据
      const requestData = {
        user: initialState?.userInfo?.username || '',
        brand: initialState?.currentBrandInfo?.brandNameCn || '',
        upload_type: 'retailer',
        applicant: values.applicant,
        upload_info_type: values.upload_info_type,
        reason: values.reason,
      };

      const response = await fetch('/bims/submit_approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        mode: 'cors',
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        message.success('审批申请提交成功');
        setApprovalModalVisible(false);
        approvalForm.resetFields();
      } else {
        throw new Error(result.message || '审批申请提交失败');
      }
    } catch (error) {
      console.error('审批申请提交失败:', error);
      message.error(
        `审批申请提交失败: ${error instanceof Error ? error.message : '未知错误'}`,
      );
    }
  };

  const columns: ProColumns<RetailerItem>[] = [
    {
      title: '采集平台',
      dataIndex: 'platform',
      align: 'center',
      width: 120,
    },
    {
      title: '平台_采集零售商名称',
      dataIndex: 'collect_vender_name',
      align: 'center',
      width: 150,
    },
    {
      title: '品牌_标准零售商名称',
      dataIndex: 'vender_name',
      align: 'center',
      width: 150,
    },
    {
      title: '品牌_一级店铺类型',
      dataIndex: 'shop_type',
      align: 'center',
      width: 130,
    },
    {
      title: '品牌_二级店铺类型',
      dataIndex: 'shop_type_1',
      align: 'center',
      width: 130,
    },
    {
      title: '品牌_三级店铺类型',
      dataIndex: 'tier1',
      align: 'center',
      width: 130,
    },
    {
      title: '品牌_四级店铺类型',
      dataIndex: 'tier2',
      align: 'center',
      width: 130,
    },
    {
      title: '品牌_五级店铺类型',
      dataIndex: 'tier3',
      align: 'center',
      width: 130,
    },
    {
      title: '美团_渠道类型',
      dataIndex: 'mt_channel_type',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_标准零售商名称
          <Tooltip
            placement="top"
            title={`使用关键词进行标准零售商名称的归一，如采集零售商名称含"大润发"则标准零售商名称为"大润发"`}
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'standard_vender_name',
      align: 'center',
      width: 150,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_标准业态类型
          <Tooltip
            placement="top"
            title="各标准业态类型：1. 新零售（盒马、美团买菜等指定零售商）；2. 闪电仓（含常见闪电仓渠道关键词及美团新供给零售商）；3. 各专营店（对应美团渠道类型类目如零食店、酒水店等）；4. CVS（含常见CVS渠道关键词及美团渠道类型为便利店且不含超市关键词）；5. H&Super（含常见大KA渠道关键词）；6. Mini（美团渠道类型=小型超市及不含大KA关键词，且美团渠道类型=大型超市/卖场）；7. 其他"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'standard_business_format',
      align: 'center',
      width: 130,
    },
    {
      title: '品牌_是否重点渠道',
      dataIndex: 'is_key_channel_brand',
      align: 'center',
      width: 130,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_全部品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_all_category',
      align: 'center',
      width: 180,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_休闲食品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_snack_food',
      align: 'center',
      width: 200,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_酒类品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_alcohol',
      align: 'center',
      width: 180,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_饮料品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_beverages',
      align: 'center',
      width: 180,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_调味品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_condiments',
      align: 'center',
      width: 190,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_家清个护品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_home_personal_care',
      align: 'center',
      width: 200,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_乳品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_dairy',
      align: 'center',
      width: 180,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_冷鲜冻品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_frozen_products',
      align: 'center',
      width: 200,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_生鲜类品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_fresh_products',
      align: 'center',
      width: 190,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_宠物用品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_pet_supplies',
      align: 'center',
      width: 200,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_母婴用品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_maternal_infant_supplies',
      align: 'center',
      width: 200,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          合印_是否重点渠道_方便素食用品品类
          <Tooltip
            placement="top"
            title="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
          >
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_key_channel_instant_veg',
      align: 'center',
      width: 220,
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      valueType: 'option',
      // render: (_, record) => [
      //   isMaintained && (
      //     <a key="edit" onClick={() => handleEdit(record)}>
      //       编辑
      //     </a>
      //   ),
      //   <a key="delete" onClick={() => handleDelete(record)}>
      //     删除
      //   </a>,
      // ],
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center gap-x-3">
            {isMaintained && (
              <a key="edit" onClick={() => handleEdit(record)}>
                编辑
              </a>
            )}
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </div>
        );
      },
    },
  ];

  const handleEdit = (record: RetailerItem) => {
    const editData = {
      ...record,
      id: record.id,
    };
    console.log('Editing record:', editData);
    editForm.setFieldsValue(editData);
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      const currentId = editForm.getFieldValue('id');

      console.log('Submitting edit with id:', currentId);

      const response = await fetch('/bims/edit_data_retailer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: initialState?.userInfo?.username || '',
          role: initialState?.userInfo?.type || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          upload_type: 'retailer',
          id: currentId,
          platform: values.platform || '',
          collect_vender_name: values.collect_vender_name || '',
          vender_name: values.vender_name || '',
          shop_type: values.shop_type || '',
          shop_type_1: values.shop_type_1 || '',
          tier1: values.tier1 || '',
          tier2: values.tier2 || '',
          tier3: values.tier3 || '',
          mt_channel_type: values.mt_channel_type || '',
          standard_vender_name: values.standard_vender_name || '',
          standard_business_format: values.standard_business_format || '',
          is_key_channel_brand: values.is_key_channel_brand || '',
          is_key_channel_all_category: values.is_key_channel_all_category || '',
          is_key_channel_snack_food: values.is_key_channel_snack_food || '',
          is_key_channel_alcohol: values.is_key_channel_alcohol || '',
          is_key_channel_beverages: values.is_key_channel_beverages || '',
          is_key_channel_condiments: values.is_key_channel_condiments || '',
          is_key_channel_home_personal_care:
            values.is_key_channel_home_personal_care || '',
          is_key_channel_dairy: values.is_key_channel_dairy || '',
          is_key_channel_frozen_products:
            values.is_key_channel_frozen_products || '',
          is_key_channel_fresh_products:
            values.is_key_channel_fresh_products || '',
          is_key_channel_pet_supplies: values.is_key_channel_pet_supplies || '',
          is_key_channel_maternal_infant_supplies:
            values.is_key_channel_maternal_infant_supplies || '',
          is_key_channel_instant_veg: values.is_key_channel_instant_veg || '',
        }),
      });

      const result = await response.json();

      if (result.status === 'success') {
        message.success('编辑成功');
        setEditModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '编辑失败');
      }
    } catch (error) {
      console.error('Edit error:', error);
      message.error(
        '编辑失败: ' + (error instanceof Error ? error.message : '未知错误'),
      );
    }
  };

  const handleDelete = async (record: RetailerItem) => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除这条数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'retailer',
              id: [record.id],
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'false',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('Delete error:', error);
          message.error(
            '删除失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  // 添加删除全部数据的处理函数
  const handleDeleteAll = async () => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除全部数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'retailer',
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'true',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除全部数据成功');
            setSelectedRows([]);
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除全部数据失败');
          }
        } catch (error) {
          console.error('Delete all error:', error);
          message.error(
            '删除全部数据失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      const params = buildRequestParams();
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`/bims/upload_data?${params}`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.status === 'success') {
        if (
          result.data.success_count !== undefined ||
          result.data.failure_count !== undefined
        ) {
          message.open({
            type: 'info',
            content: (
              <div>
                <div>{`成功：${result.data.success_count}条，失败：${result.data.failure_count}条`}</div>
                <div
                  style={{ fontSize: '12px', marginTop: '4px', color: '#666' }}
                >
                  可前往上传记录模块查看详情
                </div>
              </div>
            ),
            icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
          });
        }

        if (result.data.message) {
          message.warning(result.data.message);
        }

        setImportModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '上传失败');
      }
    } catch (error) {
      console.error('Import error:', error);
      message.error(
        '上传失败: ' + (error instanceof Error ? error.message : '未知错误'),
      );
    } finally {
      setImportLoading(false);
    }
  };
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  return (
    <Card>
      <Space
        direction="vertical"
        size="middle"
        style={{ width: '100%', marginBottom: 16 }}
      >
        <Space direction="vertical" size={4}>
          <Segmented
            value={isMaintained ? 'maintained' : 'unmaintained'}
            onChange={(value) => {
              setIsMaintained(value === 'maintained');
              if (actionRef.current?.reset) {
                actionRef.current.reset();
              }
              actionRef.current?.reload();
            }}
            options={[
              {
                label: '已维护',
                value: 'maintained',
              },
              {
                label: '待维护',
                value: 'unmaintained',
              },
            ]}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '8px',
              fontSize: '16px',
            }}
            block
          />
          <Typography.Text type="danger" style={{ fontSize: '13px' }}>
            已维护：展示必填项不缺失的数据信息
            <br />
            待维护：展示必填项缺失的数据信息
          </Typography.Text>
        </Space>

        <Form form={searchForm} layout="inline">
          <Form.Item name="keyword" label="采集平台">
            <Select
              showSearch
              style={{ width: 300 }}
              options={platformOptions}
              allowClear
              placeholder="请输入或选择采集平台"
              onSearch={fetchPlatformOptions}
              onFocus={() => fetchPlatformOptions('')}
              filterOption={false}
              showArrow={true}
              defaultActiveFirstOption={false}
              onChange={(value) => {
                searchForm.setFieldsValue({ keyword: value });
              }}
              onInputKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const inputValue = (e.target as HTMLInputElement).value;
                  searchForm.setFieldsValue({ keyword: inputValue });
                }
              }}
              onBlur={(e) => {
                const inputValue = (e.target as HTMLInputElement).value;
                if (inputValue) {
                  searchForm.setFieldsValue({ keyword: inputValue });
                }
              }}
            />
          </Form.Item>
          <Form.Item name="searchText" label="零售商/店铺类型">
            <Input
              style={{ width: 300 }}
              placeholder="请输入零售商名称或店铺类型"
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  searchForm.resetFields();
                  actionRef.current?.reload();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>

      <ProTable<RetailerItem>
        headerTitle="零售商管理"
        actionRef={actionRef}
        rowKey="id"
        scroll={{
          x: 3500,
        }}
        search={false}
        options={false}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          preserveSelectedRowKeys: true,
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
            setSelectedRows(selectedRows);
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        toolBarRender={() => [
          selectedRows.length > 0 && (
            <Button
              key="delete"
              danger
              onClick={async () => {
                Modal.confirm({
                  title: '确认删除',
                  content: `是否确认删除选中的 ${selectedRowKeys.length} 条数据？`,
                  onOk: async () => {
                    try {
                      // const ids = selectedRows.map((row) => row.id);

                      const response = await fetch('/bims/delete_data', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          user: initialState?.userInfo?.username,
                          role: initialState?.userInfo?.type,
                          brand: initialState?.currentBrandInfo?.brandNameCn,
                          upload_type: 'retailer',
                          id: selectedRowKeys,
                          is_maintained: isMaintained ? 'true' : 'false',
                          is_all_delete: 'false',
                        }),
                      });

                      const result = await response.json();

                      if (result.status === 'success') {
                        message.success('批量删除成功');
                        setSelectedRows([]);
                        actionRef.current?.reload();
                        actionRef.current?.clearSelected?.();
                      } else {
                        message.error(result.message || '批量删除失败');
                      }
                    } catch (error) {
                      console.error('Batch delete error:', error);
                      message.error(
                        '批量删除失败: ' +
                          (error instanceof Error ? error.message : '未知错误'),
                      );
                    }
                  },
                });
              }}
            >
              批量删除
            </Button>
          ),
          !isMaintained && (
            <Button
              key="approval"
              type="primary"
              icon={<AuditOutlined />}
              onClick={() => setApprovalModalVisible(true)}
            >
              审批
            </Button>
          ),
          !isMaintained && (
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={() => checkUserApproval(true)}
              loading={refreshingApproval}
              title="刷新审批状态"
            >
              刷新状态
            </Button>
          ),
          <Button key="deleteAll" danger onClick={handleDeleteAll}>
            删除全部
          </Button>,
          !isMaintained && (
            <Button
              key="import"
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
          ),
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={async () => {
              try {
                if (
                  selectedRows.length === 0 &&
                  actionRef.current?.pageInfo?.total === 0
                ) {
                  message.warning('暂无数据');
                  return;
                }

                const searchValues = await searchForm.validateFields();
                const params = buildRequestParams({
                  is_maintained: isMaintained ? 'true' : 'false',
                  filter_list: searchValues.keyword || '',
                  custom_filter_data: searchValues.searchText || '',
                  ...(selectedRows.length > 0
                    ? { id: JSON.stringify(selectedRows.map((row) => row.id)) }
                    : {}),
                });
                const response = await fetch(`/bims/download_data?${params}`, {
                  method: 'GET',
                  headers: {
                    Accept: '*/*',
                  },
                });

                if (!response.ok) {
                  throw new Error('下载失败');
                }

                // 获取文件名
                const contentDisposition = response.headers.get(
                  'content-disposition',
                );
                let filename = 'download.xlsx';
                if (contentDisposition) {
                  const filenameMatch = contentDisposition.match(
                    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
                  );
                  if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                  }
                }

                // 获取二进制数据并创建下载
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                message.success(
                  selectedRows.length > 0
                    ? '已选数据下载成功'
                    : '全部数据下载成功',
                );
              } catch (error) {
                console.error('Download error:', error);
                message.error(
                  '下载失败: ' +
                    (error instanceof Error ? error.message : '未知错误'),
                );
              }
            }}
          >
            {selectedRows.length > 0 ? '下载所选' : '下载全部'}
          </Button>,
        ]}
        request={async (params) => {
          try {
            const searchValues = await searchForm.validateFields();
            const queryParams = buildRequestParams({
              is_maintained: isMaintained ? 'true' : 'false',
              page: String(params.current || 1),
              pageSize: String(params.pageSize || 20),
              filter_list: searchValues.keyword || '',
              custom_filter_data: searchValues.searchText || '',
            });

            const response = await fetch(`/bims/select_data?${queryParams}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
              },
            });

            if (!response.ok) {
              throw new Error(
                `Network response was not ok: ${response.status}`,
              );
            }

            const result = await response.json();

            if (result.status === 'success') {
              return {
                data: result.data.list,
                success: true,
                total: result.data.total,
              };
            }
            throw new Error(result.message || '获取数据失败');
          } catch (error) {
            console.error('Error in request:', error);
            message.error(
              '获取数据失败: ' +
                (error instanceof Error ? error.message : '未知错误'),
            );
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        pagination={{
          pageSize: pageSize,
          showSizeChanger: true,
          defaultCurrent: 1,
          defaultPageSize: 10,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, size) => {
            setPageSize(size);
            actionRef.current?.reload();
          },
        }}
      />

      <Modal
        title="导入零售商"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Upload.Dragger
            accept=".xls,.xlsx"
            beforeUpload={(file) => {
              handleImport(file);
              return false;
            }}
            disabled={importLoading}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">将文件拖到此处，或点击选择文件</p>
            <p className="ant-upload-hint">
              请上传小于等于50M的.xls, .xlsx格式文件
            </p>
            {importLoading && (
              <div style={{ marginTop: 8 }}>
                <Typography.Text type="secondary">
                  <Space>正在导入中，请稍候...</Space>
                </Typography.Text>
              </div>
            )}
          </Upload.Dragger>
        </Space>
      </Modal>

      <Modal
        title="编辑零售商"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        width={680}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text type="danger">注：带 * 号的为必填项</Typography.Text>
        </div>
        <Form
          form={editForm}
          layout="vertical"
          style={{ maxHeight: '60vh', overflowY: 'auto' }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item
              name="platform"
              label="采集平台"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="collect_vender_name"
              label="平台_采集零售商名称"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="vender_name"
              label="品牌_标准零售商名称"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入品牌_标准零售商名称' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="shop_type"
              label="品牌_一级店铺类型"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入品牌_一级店铺类型' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="shop_type_1"
              label="品牌_二级店铺类型"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="tier1"
              label="品牌_三级店铺类型"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="tier2"
              label="品牌_四级店铺类型"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="tier3"
              label="品牌_五级店铺类型"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="is_key_channel_brand"
              label="品牌_是否重点渠道"
              style={{ marginBottom: 12 }}
            >
              <Select placeholder="请选择">
                <Select.Option value="是">是</Select.Option>
                <Select.Option value="否">否</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="mt_channel_type"
              label="美团_渠道类型"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="standard_vender_name"
              label="合印_标准零售商名称"
              style={{ marginBottom: 12 }}
              tooltip={`使用关键词进行标准零售商名称的归一，如采集零售商名称含"大润发"则标准零售商名称为"大润发"`}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="standard_business_format"
              label="合印_标准业态类型"
              style={{ marginBottom: 12 }}
              tooltip="各标准业态类型：1. 新零售（盒马、美团买菜等指定零售商）；2. 闪电仓（美团新供给零售商）；3. 各专营店（对应美团渠道类型类目如零食店、酒水店等）；4. CVS（美团渠道类型为便利店且不含超市关键词）；5. H&Super（含大KA指定关键词）；6. Mini（美团渠道类型=小型超市）；7. 散店（剩余其他）"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_all_category"
              label="合印_是否重点渠道_全部品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_snack_food"
              label="合印_是否重点渠道_休闲食品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_alcohol"
              label="合印_是否重点渠道_酒类品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_beverages"
              label="合印_是否重点渠道_饮料品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_condiments"
              label="合印_是否重点渠道_调味品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_home_personal_care"
              label="合印_是否重点渠道_家清个护品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_dairy"
              label="合印_是否重点渠道_乳品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_frozen_products"
              label="合印_是否重点渠道_冷鲜冻品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_fresh_products"
              label="合印_是否重点渠道_生鲜类品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_pet_supplies"
              label="合印_是否重点渠道_宠物用品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_maternal_infant_supplies"
              label="合印_是否重点渠道_母婴用品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="is_key_channel_instant_veg"
              label="合印_是否重点渠道_方便素食用品品类"
              style={{ marginBottom: 12 }}
              tooltip="是：代表行业销售占比前80%的渠道&#10;否：代表另外20%的渠道"
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title="维表审批申请"
        open={approvalModalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人姓名" />
          </Form.Item>

          <Form.Item
            name="upload_info_type"
            label="上传类型"
            rules={[{ required: true, message: '请选择上传类型' }]}
          >
            <Select placeholder="请选择上传类型">
              <Select.Option value="待维护信息补全">
                待维护信息补全
              </Select.Option>
              <Select.Option value="已维护信息变更">
                已维护信息变更
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="reason"
            label="上传信息说明"
            rules={[{ required: true, message: '请输入上传信息说明' }]}
          >
            <Input.TextArea placeholder="请输入上传信息说明" rows={4} />
          </Form.Item>

          <div
            style={{
              marginTop: 16,
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
            }}
          >
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              请详细描述本次上传维表的原因，新增或者改动。若涉及改动明确描述改动内容。
              <br />
              其余备注：维表默认T+1日刷新，其他需求提需数仓。
            </Typography.Text>
          </div>
        </Form>
      </Modal>
    </Card>
  );
}
