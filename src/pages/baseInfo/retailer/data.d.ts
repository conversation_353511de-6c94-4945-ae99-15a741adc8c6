export interface RetailerItem {
  id: string;
  brand: string;
  platform: string;
  collect_vender_name: string;
  vender_name: string;
  shop_type: string;
  shop_type_1: string;
  tier1: string;
  tier2: string;
  tier3: string;
  mt_channel_type: string;
  standard_vender_name: string;
  standard_business_format: string;
  is_key_channel_brand: string;
  is_key_channel_all_category: string;
  is_key_channel_snack_food: string;
  is_key_channel_alcohol: string;
  is_key_channel_beverages: string;
  is_key_channel_condiments: string;
  is_key_channel_home_personal_care: string;
  is_key_channel_dairy: string;
  is_key_channel_frozen_products: string;
  is_key_channel_fresh_products: string;
  is_key_channel_pet_supplies: string;
  is_key_channel_maternal_infant_supplies: string;
  is_key_channel_instant_veg: string;
}
