import { useRef, useState } from 'react';

import type {
  ActionType,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import {
  ProFormDateTimeRangePicker,
  ProTable,
} from '@ant-design/pro-components';
import { request, useModel } from '@umijs/max';
import { Card, DatePicker, message, Button } from 'antd';
import dayjs from 'dayjs';

import './index.less';

interface UploadRecordItem {
  id: number;
  original_id: string;
  upload_type: string;
  uploader: string;
  role: string;
  file_name: string;
  success_count: number;
  failure_count: number;
  status: string;
  upload_date: string;
  brand: string;
}
const { RangePicker } = DatePicker;

// 平台值到中文标签的映射
const platformValueToLabelMap: { [key: string]: string } = {
  meituan: '美团',
  eleme: '饿了么',
  jingdong: '京东到家',
  duodian: '多点',
  taoxianda: '淘鲜达',
  // 添加中文标签作为key，以处理后端直接返回中文的情况
  '美团': '美团',
  '饿了么': '饿了么',
  '京东到家': '京东到家',
  '多点': '多点',
  '淘鲜达': '淘鲜达',
};

// 动态生成上传类型枚举
const generateUploadTypeValueEnum = () => {
  const enumMap: { [key: string]: { text: string } } = {
    full_data_add: { text: '全量数据上传_新增' },
    full_data_replace: { text: '全量数据上传_替换' },
    campaign_bill_add: { text: '活动账单上传_新增' },
    campaign_bill_replace: { text: '活动账单上传_替换' },
  };

  for (const value in platformValueToLabelMap) {
    if (Object.prototype.hasOwnProperty.call(platformValueToLabelMap, value)) {
      const label = platformValueToLabelMap[value];
      
      // 添加英文值作为key (e.g., full_data_taoxianda)
      enumMap[`full_data_${value}`] = { text: `全量数据上传_${label}` };
      enumMap[`campaign_bill_${value}`] = { text: `活动账单上传_${label}` };

      // 如果英文值和中文标签不同，则添加中文标签作为key (e.g., full_data_淘鲜达)
      if (value !== label) { // 避免重复添加 (如 '美团':'美团'的情况)
        enumMap[`full_data_${label}`] = { text: `全量数据上传_${label}` };
        enumMap[`campaign_bill_${label}`] = { text: `活动账单上传_${label}` };
      }
    }
  }
  return enumMap;
};

export default function UploadRecord() {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [downloadingIds, setDownloadingIds] = useState<Set<string>>(new Set());
  // 处理下载
  const handleDownload = async (record: UploadRecordItem) => {
    // 添加loading状态
    setDownloadingIds(prev => new Set(prev).add(record.original_id));
    
    try {
      // 检查必填参数
      const requestBody = {
        user: initialState?.userInfo?.username,
        role: initialState?.userInfo?.type,
        id: record.original_id,
        brand: initialState?.currentBrandInfo?.brandNameCn,
      };

      // 验证所有必填参数
      const missingParams = [];
      if (!requestBody.user) missingParams.push('用户名');
      if (!requestBody.id) missingParams.push('记录ID');
      if (!requestBody.brand) missingParams.push('品牌');

      if (missingParams.length > 0) {
        throw new Error(`缺少必填参数: ${missingParams.join(', ')}`);
      }

      console.log('Download request params:', requestBody);

      // 构造查询字符串
      const queryString = Object.entries(requestBody)
        .map(([key, value]) => `${key}=${encodeURIComponent(value || '')}`)
        .join('&');

      const response = await fetch(`/bims/download_error_data?${queryString}`, {
        method: 'POST',
        headers: {
          Accept:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '下载失败');
      }

      // 使用前端展示的文件名，确保下载文件名与展示一致
      let filename = record.file_name || 'error_data.xlsx';
      
      // 如果文件名没有扩展名，添加.xlsx扩展名
      if (!filename.toLowerCase().endsWith('.xlsx') && !filename.toLowerCase().endsWith('.xls')) {
        filename += '.xlsx';
      }

      // 直接使用响应数据创建下载
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('下载成功');
    } catch (error) {
      console.error('Download error:', error);
      message.error(
        '下载失败: ' + (error instanceof Error ? error.message : '未知错误'),
      );
    } finally {
      // 移除loading状态
      setDownloadingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(record.original_id);
        return newSet;
      });
    }
  };

  const columns: ProColumns<UploadRecordItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '上传类型',
      dataIndex: 'upload_type',
      valueType: 'select',
      valueEnum: generateUploadTypeValueEnum(),
      render: (text: any) => {
        const enumMap = generateUploadTypeValueEnum();
        return enumMap[text]?.text || text;
      },
      width: 120,
      align: 'center',
    },
    {
      title: '上传人',
      dataIndex: 'uploader',
      width: 100,
      align: 'center',
    },
    {
      title: '角色',
      dataIndex: 'role',
      width: 100,
      search: false,
      align: 'center',
    },
    {
      title: '文件名称',
      dataIndex: 'file_name',
      width: 200,
      ellipsis: true,
      search: false,
      align: 'center',
    },
    {
      title: '成功数量',
      dataIndex: 'success_count',
      width: 100,
      search: false,
      align: 'center',
    },
    {
      title: '失败数量',
      dataIndex: 'failure_count',
      width: 100,
      search: false,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        成功: { text: '成功', status: 'Success' },
        部分失败: { text: '部分失败', status: 'Warning' },
        失败: { text: '失败', status: 'Error' },
      },
      width: 100,
      align: 'center',
    },
    {
      title: '上传时间',
      dataIndex: 'upload_date',
      valueType: 'dateTime',
      width: 160,
      fieldProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      renderFormItem: (
        item,
        { type, defaultRender },
        form
      ) => {
        if (type === 'form') {
          return null;
        }
        const status = form.getFieldValue('state');
        if (status !== 'open') {
          return <RangePicker showTime />;
        }
        return defaultRender(item);
      },
      search: {
        transform: (value) => {
          if (value && Array.isArray(value)) {
            return {
              start_date: dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss'),
              end_date: dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss'),
            };
          }
          return {};
        },
      },
      align: 'center',
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <div>
          <Button
            key="download"
            type="link"
            size="small"
            loading={downloadingIds.has(record.original_id)}
            onClick={() => handleDownload(record)}
          >
            下载
          </Button>
        </div>
      ),
      align: 'center',
    },
  ];

  return (
    // <Card>
    <ProTable<UploadRecordItem>
      headerTitle="上传记录"
      actionRef={actionRef}
      formRef={formRef}
      scroll={{
        x: 1500,
      }}
      rowKey="id"
      search={{
        labelWidth: 80,
        defaultCollapsed: false,
        collapseRender: false,
        optionRender: (searchConfig, formProps, dom) => [dom[1], dom[0]],
      }}
      request={async (params) => {
        console.log(
          '%c [ params ]-194',
          'font-size:13px; background:pink; color:#bf2c9f;',
          params,
        );
        try {
          console.log('Request params:', params);
          const queryParams = {
            user: initialState?.userInfo?.username || '',
            uploader: params.uploader || '',
            role: initialState?.userInfo?.type || 'cs',
            brand: initialState?.currentBrandInfo?.brandNameCn || '',
            upload_type: params.upload_type || '',
            status: params.status || '',
            start_date: params.start_date || '',
            end_date: params.end_date || '',
            page: String(params.current || 1),
            pageSize: String(params.pageSize || 10),
          };

          // 构造查询字符串
          const queryString = Object.entries(queryParams)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&');

          const response = await fetch(
            `/bims/get_history_data?${queryString}`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
              },
            },
          );

          const data = await response.json();
          console.log('API response:', data);

          if (data.status !== 'success') {
            throw new Error(data.message || '获取数据失败');
          }

          // 为数据添加顺序 ID
          const current = Number(params.current || 1);
          const pageSize = Number(params.pageSize || 10);
          const listWithIndex = data.data.list.map(
            (item: any, index: number) => ({
              ...item,
              original_id: item.id, // 保存后端返回的原始ID
              id: (current - 1) * pageSize + index + 1, // 生成前端显示用的序号
            }),
          );

          return {
            data: listWithIndex,
            success: true,
            total: data.data.total,
          };
        } catch (err: any) {
          console.error('Error details:', err);
          message.error(err.message || '获取数据失败');
          return {
            data: [],
            success: false,
            total: 0,
          };
        }
      }}
      columns={columns}
      pagination={{
        defaultPageSize: 10,
        defaultCurrent: 1,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
    // </Card>
  );
}
