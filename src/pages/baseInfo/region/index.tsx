import { useEffect, useRef, useState } from 'react';

import {
  AuditOutlined,
  DownloadOutlined,
  InboxOutlined,
  InfoCircleFilled,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { CheckCircleFilled } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Segmented,
  Select,
  Space,
  Table,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { divide } from 'lodash';

import type { RegionItem } from './data.d';

export default function RegionManagement() {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [approvalForm] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState<RegionItem[]>([]);
  const [cityOptions, setCityOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [isMaintained, setIsMaintained] = useState(true);
  const [pageSize, setPageSize] = useState<number>(20);
  const [canUpload, setCanUpload] = useState(false);
  const [approvalMessage, setApprovalMessage] = useState('');
  const [refreshingApproval, setRefreshingApproval] = useState(false);

  // 构建请求参数
  const buildRequestParams = (additionalParams = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: initialState?.currentBrandInfo?.brandNameCn || '',
      upload_type: 'area',
      ...additionalParams,
    };

    return Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  };

  // 获取城市选项
  const fetchCityOptions = async (searchText: string) => {
    try {
      const params = buildRequestParams({
        is_maintained: isMaintained ? 'true' : 'false',
      });
      const response = await fetch(`/bims/get_list?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.status === 'success' && result.data.cities) {
          // 确保cities是字符串数组
          const cities = Array.isArray(result.data.cities)
            ? result.data.cities.map((city: { city: string } | string) =>
                typeof city === 'object' ? city.city : city,
              )
            : [];

          const options = cities
            .filter(
              (city: string) =>
                !searchText ||
                city.toLowerCase().includes(searchText.toLowerCase()),
            )
            .map((city: string) => ({
              value: city,
              label: city,
            }));
          setCityOptions(options);
        }
      }
    } catch (error) {
      console.error('获取城市选项失败:', error);
    }
  };

  // 检查用户审批状态
  const checkUserApproval = async (showMessage = false) => {
    setRefreshingApproval(true);
    try {
      const params = buildRequestParams({
        upload_type: 'area',
      });

      const response = await fetch(`/bims/check_user_approval?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        mode: 'cors',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        const canUpload = result.data?.can_upload || false;
        setCanUpload(canUpload);
        setApprovalMessage(result.data?.message || result.message || '');

        if (showMessage) {
          if (canUpload) {
            message.success('审批状态已更新');
          } else {
            message.info(
              result.data?.message || result.message || '审批状态已更新',
            );
          }
        }
      }
    } catch (error) {
      console.error('检查审批状态失败:', error);
      if (showMessage) {
        message.error('获取审批状态失败');
      }
      setCanUpload(false);
    } finally {
      setRefreshingApproval(false);
    }
  };

  useEffect(() => {
    // 首次检查审批状态
    checkUserApproval();
  }, []);

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    actionRef.current?.reload();
  };

  // 处理审批提交
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();

      // 构建请求数据
      const requestData = {
        user: initialState?.userInfo?.username || '',
        brand: initialState?.currentBrandInfo?.brandNameCn || '',
        upload_type: 'area',
        applicant: values.applicant,
        upload_info_type: values.upload_info_type,
        reason: values.reason,
      };

      const response = await fetch('/bims/submit_approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        mode: 'cors',
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        message.success('审批申请提交成功');
        setApprovalModalVisible(false);
        approvalForm.resetFields();
      } else {
        throw new Error(result.message || '审批申请提交失败');
      }
    } catch (error) {
      console.error('审批申请提交失败:', error);
      message.error(
        `审批申请提交失败: ${error instanceof Error ? error.message : '未知错误'}`,
      );
    }
  };

  const columns: ProColumns<RegionItem>[] = [
    {
      title: '采集城市',
      dataIndex: 'city',
      align: 'center',
    },
    {
      title: '标准城市',
      dataIndex: 'standard_city',
      align: 'center',
    },
    {
      title: '标准省份',
      dataIndex: 'province',
      align: 'center',
    },
    {
      title: '大区',
      dataIndex: 'area',
      align: 'center',
    },
    {
      title: '分公司',
      dataIndex: 'branch_name',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record) => {
        if (isMaintained) {
          return (
            <div className="flex items-center justify-center gap-x-3">
              <a key="edit" onClick={() => handleEdit(record)}>
                编辑
              </a>
              <a key="delete" onClick={() => handleDelete(record)}>
                删除
              </a>
            </div>
          );
        }
        return (
          <div>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </div>
        );
      },
    },
  ];

  const handleEdit = (record: RegionItem) => {
    const editData = {
      ...record,
      id: record.id,
    };
    console.log('Editing record:', editData);
    editForm.setFieldsValue(editData);
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      const currentId = editForm.getFieldValue('id');

      const response = await fetch('/bims/edit_data_area', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: initialState?.userInfo?.username || '',
          role: initialState?.userInfo?.type || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          upload_type: 'area',
          id: currentId,
          city: values.city || '',
          area: values.area || '',
          standard_city: values.standard_city || '',
          province: values.province || '',
          branch_name: values.branch_name || '',
        }),
      });

      const result = await response.json();

      if (result.status === 'success') {
        message.success('编辑成功');
        setEditModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '编辑失败，请检查数据是否正确');
      }
    } catch (error) {
      console.error('Edit error:', error);
      message.error('编辑失败，请检查数据是否正确');
    }
  };

  const handleDelete = async (record: RegionItem) => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除这条数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'area',
              id: [record.id],
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'false',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('Delete error:', error);
          message.error(
            '删除失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  // 添加删除全部数据的处理函数
  const handleDeleteAll = async () => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除全部数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'area',
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'true',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除全部数据成功');
            setSelectedRows([]);
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除全部数据失败');
          }
        } catch (error) {
          console.error('Delete all error:', error);
          message.error(
            '删除全部数据失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      const params = buildRequestParams();
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`/bims/upload_data?${params}`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.status === 'success') {
        if (
          result.data.success_count !== undefined ||
          result.data.failure_count !== undefined
        ) {
          message.open({
            type: 'info',
            content: (
              <div>
                <div>{`成功：${result.data.success_count}条，失败：${result.data.failure_count}条`}</div>
                <div
                  style={{ fontSize: '12px', marginTop: '4px', color: '#666' }}
                >
                  可前往上传记录模块查看详情
                </div>
              </div>
            ),
            icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
          });
        }

        if (result.data.message) {
          message.warning(result.data.message);
        }

        setImportModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(result.message || '上传失败');
      }
    } catch (error) {
      console.error('Import error:', error);
      message.error(
        '上传失败: ' + (error instanceof Error ? error.message : '未知错误'),
      );
    } finally {
      setImportLoading(false);
    }
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  return (
    <Card>
      <Space
        direction="vertical"
        size="middle"
        style={{ width: '100%', marginBottom: 16 }}
      >
        <Space direction="vertical" size={4}>
          <Segmented
            value={isMaintained ? 'maintained' : 'unmaintained'}
            onChange={(value) => {
              setIsMaintained(value === 'maintained');
              if (actionRef.current?.reset) {
                actionRef.current.reset();
              }
              actionRef.current?.reload();
            }}
            options={[
              {
                label: '已维护',
                value: 'maintained',
              },
              {
                label: '待维护',
                value: 'unmaintained',
              },
            ]}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '8px',
              fontSize: '16px',
            }}
            block
          />
          <Typography.Text type="danger" style={{ fontSize: '13px' }}>
            已维护：展示必填项不缺失的数据信息
            <br />
            待维护：展示必填项缺失的数据信息
          </Typography.Text>
        </Space>

        <Form form={searchForm} layout="inline">
          <Form.Item name="keyword" label="采集城市">
            <Select
              showSearch
              style={{ width: 300 }}
              options={cityOptions}
              allowClear
              placeholder="请输入或选择采集城市"
              onSearch={fetchCityOptions}
              onFocus={() => fetchCityOptions('')}
              filterOption={false}
              showArrow={true}
              defaultActiveFirstOption={false}
              onChange={(value) => {
                searchForm.setFieldsValue({ keyword: value });
              }}
              onInputKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const inputValue = (e.target as HTMLInputElement).value;
                  searchForm.setFieldsValue({ keyword: inputValue });
                }
              }}
              onBlur={(e) => {
                const inputValue = (e.target as HTMLInputElement).value;
                if (inputValue) {
                  searchForm.setFieldsValue({ keyword: inputValue });
                }
              }}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  searchForm.resetFields();
                  actionRef.current?.reload();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>

      <ProTable<RegionItem>
        headerTitle="地域管理"
        actionRef={actionRef}
        rowKey="id"
        // rowSelection={rowSelection}
        scroll={{
          x: 1000,
        }}
        search={false}
        options={false}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          // onChange: (selectedRowKeys, selectedRows) => {

          // },

          preserveSelectedRowKeys: true,
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
            setSelectedRows(selectedRows);
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        toolBarRender={() => [
          selectedRows.length > 0 && (
            <Button
              key="delete"
              danger
              onClick={async () => {
                Modal.confirm({
                  title: '确认删除',
                  content: `是否确认删除选中的 ${selectedRowKeys.length} 条数据？`,
                  onOk: async () => {
                    try {
                      // const ids = selectedRowKeys

                      const response = await fetch('/bims/delete_data', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          user: initialState?.userInfo?.username || '',
                          role: initialState?.userInfo?.type || '',
                          brand:
                            initialState?.currentBrandInfo?.brandNameCn || '',
                          upload_type: 'area',
                          id: selectedRowKeys,
                          is_maintained: isMaintained ? 'true' : 'false',
                          is_all_delete: 'false',
                        }),
                      });

                      const result = await response.json();

                      if (result.status === 'success') {
                        message.success('批量删除成功');
                        setSelectedRows([]);
                        if (actionRef.current?.clearSelected) {
                          actionRef.current.clearSelected();
                        }
                        actionRef.current?.reload();
                      } else {
                        message.error(result.message || '批量删除失败');
                      }
                    } catch (error) {
                      console.error('Batch delete error:', error);
                      message.error(
                        '批量删除失败: ' +
                          (error instanceof Error ? error.message : '未知错误'),
                      );
                    }
                  },
                });
              }}
            >
              批量删除
            </Button>
          ),
          !isMaintained && (
            <Button
              key="approval"
              type="primary"
              icon={<AuditOutlined />}
              onClick={() => setApprovalModalVisible(true)}
            >
              审批
            </Button>
          ),
          !isMaintained && (
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={() => checkUserApproval(true)}
              loading={refreshingApproval}
              title="刷新审批状态"
            >
              刷新状态
            </Button>
          ),
          <Button key="deleteAll" danger onClick={handleDeleteAll}>
            删除全部
          </Button>,
          !isMaintained && (
            <Button
              key="import"
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
          ),
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={async () => {
              try {
                if (
                  selectedRows.length === 0 &&
                  actionRef.current?.pageInfo?.total === 0
                ) {
                  message.warning('暂无数据');
                  return;
                }

                const searchValues = await searchForm.validateFields();
                const params = buildRequestParams({
                  is_maintained: isMaintained ? 'true' : 'false',
                  filter_list: searchValues.keyword || '',
                  custom_filter_data: '',
                  ...(selectedRows.length > 0
                    ? { id: JSON.stringify(selectedRows.map((row) => row.id)) }
                    : {}),
                });
                const response = await fetch(`/bims/download_data?${params}`, {
                  method: 'GET',
                  headers: {
                    Accept: '*/*',
                  },
                });

                if (!response.ok) {
                  throw new Error('下载失败');
                }

                // 获取文件名
                const contentDisposition = response.headers.get(
                  'content-disposition',
                );
                let filename = 'download.xlsx';
                if (contentDisposition) {
                  const filenameMatch = contentDisposition.match(
                    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
                  );
                  if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                  }
                }

                // 获取二进制数据并创建下载
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                message.success(
                  selectedRows.length > 0
                    ? '已选数据下载成功'
                    : '全部数据下载成功',
                );
              } catch (error) {
                console.error('Download error:', error);
                message.error(
                  '下载失败: ' +
                    (error instanceof Error ? error.message : '未知错误'),
                );
              }
            }}
          >
            {selectedRows.length > 0 ? '下载所选' : '下载全部'}
          </Button>,
        ]}
        request={async (params) => {
          try {
            const searchValues = await searchForm.validateFields();
            const queryParams = buildRequestParams({
              is_maintained: isMaintained ? 'true' : 'false',
              page: String(params.current || 1),
              pageSize: String(params.pageSize || 20),
              filter_list: searchValues.keyword || '',
            });

            const response = await fetch(`/bims/select_data?${queryParams}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
              },
            });

            if (!response.ok) {
              throw new Error(
                `Network response was not ok: ${response.status}`,
              );
            }

            const result = await response.json();

            if (result.status === 'success') {
              return {
                data: result.data.list,
                success: true,
                total: result.data.total,
              };
            }
            throw new Error(result.message || '获取数据失败');
          } catch (error) {
            console.error('Error in request:', error);
            message.error(
              '获取数据失败: ' +
                (error instanceof Error ? error.message : '未知错误'),
            );
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        pagination={{
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          defaultCurrent: 1,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, size) => {
            setPageSize(size);
            actionRef.current?.reload();
          },
        }}
      />

      <Modal
        title="导入地域数据"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Upload.Dragger
            accept=".xls,.xlsx"
            beforeUpload={(file) => {
              handleImport(file);
              return false;
            }}
            disabled={importLoading}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">将文件拖到此处，或点击选择文件</p>
            <p className="ant-upload-hint">
              请上传小于等于50M的.xls, .xlsx格式文件
            </p>
            {importLoading && (
              <div style={{ marginTop: 8 }}>
                <Typography.Text type="secondary">
                  <Space>正在导入中，请稍候...</Space>
                </Typography.Text>
              </div>
            )}
          </Upload.Dragger>
        </Space>
      </Modal>

      <Modal
        title="编辑地域"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        width={680}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text type="danger">注：带 * 号的为必填项</Typography.Text>
        </div>
        <Form
          form={editForm}
          layout="vertical"
          style={{ maxHeight: '60vh', overflowY: 'auto' }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item
              name="city"
              label="采集城市"
              style={{ marginBottom: 12 }}
              required
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="standard_city"
              label="标准城市"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入标准城市' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="province"
              label="标准省份"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入标准省份' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="area"
              label="大区"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入大区' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="branch_name"
              label="分公司"
              style={{ marginBottom: 12 }}
              required={
                initialState?.currentBrandInfo?.brandNameCn === '太太乐'
              }
              rules={[
                {
                  required:
                    initialState?.currentBrandInfo?.brandNameCn === '太太乐',
                  message: '请输入分公司',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title="维表审批申请"
        open={approvalModalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人姓名" />
          </Form.Item>

          <Form.Item
            name="upload_info_type"
            label="上传类型"
            rules={[{ required: true, message: '请选择上传类型' }]}
          >
            <Select placeholder="请选择上传类型">
              <Select.Option value="待维护信息补全">
                待维护信息补全
              </Select.Option>
              <Select.Option value="已维护信息变更">
                已维护信息变更
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="reason"
            label="上传信息说明"
            rules={[{ required: true, message: '请输入上传信息说明' }]}
          >
            <Input.TextArea placeholder="请输入上传信息说明" rows={4} />
          </Form.Item>

          <div
            style={{
              marginTop: 16,
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
            }}
          >
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              请详细描述本次上传维表的原因，新增或者改动。若涉及改动明确描述改动内容。
              <br />
              其余备注：维表上传后，默认回刷 2024 年 1 月 1
              日至今的数据。若需回刷其他日期范围的数据，请在此备注说明
            </Typography.Text>
          </div>
        </Form>
      </Modal>
    </Card>
  );
}
