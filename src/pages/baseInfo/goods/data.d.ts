export interface GoodsItem {
  id: number;
  brand: string;
  sub_sidiary: string;
  sub_brand: string;
  mt_sub_brand: string;
  collect_upc: string;
  upc: string;
  collect_product_name: string;
  product_name: string;
  brand_product_id: string;
  launch_date: string;
  category1: string;
  category2: string;
  category3: string;
  mt_category1: string;
  mt_category2: string;
  mt_category3: string;
  product_role: string;
  product_line: string;
  is_main_push_product: string;
  is_red_line_product: string;
  product_type: string;
  flavor: string;
  size: string;
  mt_size: string;
  packaging: string;
  brand_price: string;
  lowest_price: string;
  highest_price: string;
  promotion_price: string;
  rsp: string;
  product_gts: string;
  Qli: string;
}

export interface GetListResponse {
  status: string;
  message?: string;
  data: {
    sub_brands: Array<{ sub_brand: string }>;
  };
}

export interface SelectDataResponse {
  status: string;
  message?: string;
  data: {
    list: GoodsItem[];
    total: number;
  };
} 