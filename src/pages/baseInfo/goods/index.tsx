import { useEffect, useRef, useState } from 'react';

import {
  AuditOutlined,
  CheckCircleFilled,
  DownloadOutlined,
  InboxOutlined,
  InfoCircleFilled,
  PlusOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Segmented,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import XLSX from 'xlsx';

import type { GetListResponse, GoodsItem } from './data.d';

interface SubBrandResponse {
  status: string;
  message?: string;
  data: {
    sub_brands: Array<{ sub_brand: string }>;
  };
}

export default function GoodsManagement() {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [approvalForm] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState<GoodsItem[]>([]);
  const [subBrandOptions, setSubBrandOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [isMaintained, setIsMaintained] = useState(true);
  const [pageSize, setPageSize] = useState<number>(20);
  const [canUpload, setCanUpload] = useState(false);
  const [approvalMessage, setApprovalMessage] = useState('');
  const [refreshingApproval, setRefreshingApproval] = useState(false);

  // 添加通用请求配置
  const fetchConfig = {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    mode: 'cors' as RequestMode,
  };

  // 构建请求参数
  const buildRequestParams = (additionalParams = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: initialState?.currentBrandInfo?.brandNameCn || '',
      upload_type: 'product',
      ...additionalParams,
    };

    return Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
  };

  // 获取子品牌列表
  const fetchSubBrands = async () => {
    try {
      const params = buildRequestParams({
        is_maintained: isMaintained ? 'true' : 'false',
        upload_type: 'product',
      });

      const url = `/bims/get_list?${params}`;
      console.log('Fetching sub brands with URL:', url);
      console.log('isMaintained:', isMaintained);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        mode: 'cors',
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response text:', responseText);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      let data: GetListResponse;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        throw new Error('Invalid JSON response from server');
      }

      console.log('Parsed data:', data);

      if (data.status === 'success') {
        // 检查是否有子品牌数据
        if (
          data.data &&
          data.data.sub_brands &&
          Array.isArray(data.data.sub_brands)
        ) {
          const options = data.data.sub_brands.map(
            (item: { sub_brand: string }) => ({
              label: item.sub_brand,
              value: item.sub_brand,
            }),
          );
          setSubBrandOptions(options);
          console.log('Set sub brand options:', options);
        } else {
          // 如果没有子品牌数据，设置为空数组
          console.log('No sub_brands data in response, setting empty options');
          setSubBrandOptions([]);
        }
      } else {
        // 如果状态不是success，但不抛出错误，只是设置空选项
        console.log('Response status is not success:', data.message);
        setSubBrandOptions([]);
        // 只有在真正的错误情况下才显示错误消息
        if (data.message && data.message !== '获取子品牌失败') {
          message.warning(data.message);
        }
      }
    } catch (error) {
      console.error('Error fetching sub brands:', error);
      // 设置空选项，避免界面错误
      setSubBrandOptions([]);
      // 只在真正的网络错误时显示错误消息
      if (error instanceof Error && error.message.includes('HTTP error')) {
        message.error('获取子品牌列表失败：网络错误');
      }
    }
  };

  // 检查用户审批状态
  const checkUserApproval = async (showMessage = false) => {
    setRefreshingApproval(true);
    try {
      const params = buildRequestParams({
        upload_type: 'product',
      });

      const response = await fetch(`/bims/check_user_approval?${params}`, {
        method: 'GET',
        ...fetchConfig,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        const canUpload = result.data?.can_upload || false;
        setCanUpload(canUpload);
        setApprovalMessage(result.data?.message || result.message || '');

        if (showMessage) {
          if (canUpload) {
            message.success('审批状态已更新');
          } else {
            message.info(
              result.data?.message || result.message || '审批状态已更新',
            );
          }
        }
      }
    } catch (error) {
      console.error('检查审批状态失败:', error);
      if (showMessage) {
        message.error('获取审批状态失败');
      }
      setCanUpload(false);
    } finally {
      setRefreshingApproval(false);
    }
  };

  useEffect(() => {
    // 首次检查审批状态
    checkUserApproval();
    fetchSubBrands();
  }, [isMaintained]);

  const columns: ProColumns<GoodsItem>[] = [
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          品牌
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'brand',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          子公司
        </span>
      ),
      dataIndex: 'sub_sidiary',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          子品牌
        </span>
      ),
      dataIndex: 'sub_brand',
      align: 'center',
      width: 120,
      valueType: 'select',
      fieldProps: {
        options: subBrandOptions,
      },
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          美团子品牌
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'mt_sub_brand',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          采集UPC
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'collect_upc',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          标准UPC
          <Tooltip placement="top" title="经过品牌标准化处理的UPC 码">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'upc',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          采集商品名称
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'collect_product_name',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          标准商品名称
          <Tooltip placement="top" title="经过品牌标准化处理的商品名称">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_name',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          品牌商品ID
          <Tooltip placement="top" title="品牌体系中的唯一识别代码，非69码">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'brand_product_id',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          上市日期
          <Tooltip placement="top" title="格式为：yyyymmdd">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'launch_date',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          一级品类
        </span>
      ),
      dataIndex: 'category1',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          二级品类
        </span>
      ),
      dataIndex: 'category2',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          三级品类
        </span>
      ),
      dataIndex: 'category3',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          美团一级品类
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'mt_category1',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          美团二级品类
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'mt_category2',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          美团三级品类
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'mt_category3',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          产品角色
          <Tooltip placement="top" title="商品在产品线中的定位">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_role',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          产品线
          <Tooltip placement="top" title="商品所属的产品线">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_line',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          是否主推品
          <Tooltip placement="top" title="是或否">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_main_push_product',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          是否红线品
          <Tooltip placement="top" title="是或否">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'is_red_line_product',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          商品类型
          <Tooltip placement="top" title="品牌个性化的其他分类标准">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_type',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          口味
        </span>
      ),
      dataIndex: 'flavor',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          规格
          <Tooltip placement="top" title="分别是如70G*2/包、330ml*6罐等">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'size',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          美团规格
          <Tooltip placement="top" title="不用更改">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'mt_size',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          包装
          <Tooltip placement="top" title="如盒、瓶、组包等">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'packaging',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          建议零售价
          <Tooltip placement="top" title="精确到小数点后两位">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'brand_price',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          最低价
          <Tooltip placement="top" title="精确到小数点后两位">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'lowest_price',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          最高价
          <Tooltip placement="top" title="精确到小数点后两位">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'highest_price',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          活动价
          <Tooltip placement="top" title="精确到小数点后两位">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'promotion_price',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          商品折扣标签
          <Tooltip placement="top" title="商品折扣类型">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_discount_tag',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          科赴标准零售价
          <Tooltip placement="top" title="科赴商品 rsp">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'rsp',
      align: 'center',
      width: 100,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          科赴商品GTS
          <Tooltip placement="top" title="科赴商品 gts">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'product_gts',
      align: 'center',
      width: 120,
    },
    {
      title: (
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          费列罗重量
          <Tooltip placement="top" title="费列罗重量">
            <QuestionCircleOutlined
              style={{ marginLeft: 5, color: '#BFBFBF' }}
            />
          </Tooltip>
        </span>
      ),
      dataIndex: 'Qli',
      align: 'center',
      width: 100,
    },
    {
      title: '操作',
      align: 'center',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        if (isMaintained) {
          return (
            <div className="flex items-center justify-center gap-x-3">
              <a key="edit" onClick={() => handleEdit(record)}>
                编辑
              </a>

              <a key="delete" onClick={() => handleDelete(record)}>
                删除
              </a>
            </div>
          );
        }
        return (
          <div>
            <a key="delete" onClick={() => handleDelete(record)}>
              删除
            </a>
          </div>
        );
      },
    },
  ];

  const handleEdit = (record: GoodsItem) => {
    const editData = {
      ...record,
      id: record.id,
    };
    console.log('Editing record:', editData);
    editForm.setFieldsValue(editData);
    setEditModalVisible(true);
  };

  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      const currentId = editForm.getFieldValue('id');

      console.log('Submitting edit with id:', currentId);

      const response = await fetch('/bims/edit_data_product', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: initialState?.userInfo?.username || '',
          role: initialState?.userInfo?.type || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          upload_type: 'product',
          id: currentId,
          sub_sidiary: values.sub_sidiary || '',
          sub_brand: values.sub_brand || '',
          mt_sub_brand: values.mt_sub_brand || '',
          collect_upc: values.collect_upc || '',
          upc: values.upc || '',
          collect_product_name: values.collect_product_name || '',
          product_name: values.product_name || '',
          brand_product_id: values.brand_product_id || '',
          launch_date: values.launch_date || '',
          category1: values.category1 || '',
          category2: values.category2 || '',
          category3: values.category3 || '',
          mt_category1: values.mt_category1 || '',
          mt_category2: values.mt_category2 || '',
          mt_category3: values.mt_category3 || '',
          product_role: values.product_role || '',
          product_line: values.product_line || '',
          is_main_push_product: values.is_main_push_product || '',
          is_red_line_product: values.is_red_line_product || '',
          product_type: values.product_type || '',
          flavor: values.flavor || '',
          size: values.size || '',
          mt_size: values.mt_size || '',
          packaging: values.packaging || '',
          brand_price: values.brand_price || '',
          lowest_price: values.lowest_price || '',
          highest_price: values.highest_price || '',
          promotion_price: values.promotion_price || '',
          product_discount_tag: values.product_discount_tag || '',
          rsp: values.rsp || '',
          product_gts: values.product_gts || '',
          Qli: values.Qli || '',
        }),
      });

      // 先获取响应文本
      const responseText = await response.text();
      console.log('Response text:', responseText);

      let result;
      try {
        // 尝试解析JSON
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        message.error('服务器响应格式错误: ' + responseText);
        return;
      }

      if (result.status === 'success') {
        message.success('编辑成功');
        setEditModalVisible(false);
        actionRef.current?.reload();
      } else {
        // 显示具体的错误信息
        console.error('Edit failed:', result);
        let errorMsg;
        if (typeof result.message === 'object') {
          // 如果message是对象，尝试提取有用的信息
          errorMsg = Object.values(result.message).join(', ');
        } else {
          errorMsg = result.message || result.error || result.msg || '编辑失败';
        }
        message.error(errorMsg);
      }
    } catch (error) {
      // 显示更详细的错误信息
      console.error('Edit error:', error);
      if (error instanceof Error) {
        if (error.name === 'TypeError') {
          message.error('网络请求失败: ' + error.message);
        } else {
          // 处理表单验证错误
          const errorObj = JSON.parse(error.message);
          if (errorObj.errorFields) {
            // 只显示第一个错误信息
            message.error(errorObj.errorFields[0].errors[0]);
          } else {
            message.error('编辑失败: ' + error.message);
          }
        }
      } else {
        message.error('编辑失败，请检查输入是否正确');
      }
    }
  };

  const handleDelete = async (record: GoodsItem) => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除这条数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'product',
              id: [record.id],
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'false',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除成功');
            setSelectedRows([]); // 清除选中状态
            actionRef.current?.clearSelected?.(); // 清除表格选中状态
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('Delete error:', error);
          message.error(
            '删除失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  const handleImport = async (file: File) => {
    setImportLoading(true);
    try {
      // 检查文件类型和大小
      const isExcel =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只支持 Excel 文件');
        return;
      }

      if (file.size > 50 * 1024 * 1024) {
        // 50MB
        message.error('文件大小不能超过50MB');
        return;
      }

      // 构建FormData
      const formData = new FormData();
      formData.append('file', file);

      // 构建URL参数
      const params = new URLSearchParams({
        user: initialState?.userInfo?.username || '',
        role: initialState?.userInfo?.type || '',
        brand: initialState?.currentBrandInfo?.brandNameCn || '',
        upload_type: 'product',
      }).toString();

      const response = await fetch(`/bims/upload_data?${params}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.status === 'success') {
        // 显示成功和失败数量
        if (
          result.data.success_count !== undefined ||
          result.data.failure_count !== undefined
        ) {
          message.open({
            type: 'info',
            content: (
              <div>
                <div>{`成功：${result.data.success_count}条，失败：${result.data.failure_count}条`}</div>
                <div
                  style={{ fontSize: '12px', marginTop: '4px', color: '#666' }}
                >
                  可前往上传记录模块查看详情
                </div>
              </div>
            ),
            icon: <InfoCircleFilled style={{ color: '#1890ff' }} />,
          });
        }

        // 如果有错误消息则显示
        if (result.data.message) {
          message.warning(result.data.message);
        }

        setImportModalVisible(false);
        actionRef.current?.reload();
      } else {
        throw new Error(result.message || '上传失败');
      }
    } catch (error) {
      console.error('Import error:', error);
      message.error(
        '上传失败: ' + (error instanceof Error ? error.message : '未知错误'),
      );
    } finally {
      setImportLoading(false);
    }
  };

  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    actionRef.current?.reload();
  };

  // 处理审批提交
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();

      // 构建请求数据
      const requestData = {
        user: initialState?.userInfo?.username || '',
        brand: initialState?.currentBrandInfo?.brandNameCn || '',
        upload_type: 'product',
        applicant: values.applicant,
        upload_info_type: values.upload_info_type,
        reason: values.reason,
      };

      const response = await fetch('/bims/submit_approval', {
        method: 'POST',
        ...fetchConfig,
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        message.success('审批申请提交成功');
        setApprovalModalVisible(false);
        approvalForm.resetFields();
      } else {
        throw new Error(result.message || '审批申请提交失败');
      }
    } catch (error) {
      console.error('审批申请提交失败:', error);
      message.error(
        `审批申请提交失败: ${error instanceof Error ? error.message : '未知错误'}`,
      );
    }
  };

  // 添加删除全部数据的处理函数
  const handleDeleteAll = async () => {
    Modal.confirm({
      title: '确认删除',
      content: '是否确认删除全部数据？',
      onOk: async () => {
        try {
          const response = await fetch('/bims/delete_data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user: initialState?.userInfo?.username || '',
              role: initialState?.userInfo?.type || '',
              brand: initialState?.currentBrandInfo?.brandNameCn || '',
              upload_type: 'product',
              is_maintained: isMaintained ? 'true' : 'false',
              is_all_delete: 'true',
            }),
          });

          const result = await response.json();

          if (result.status === 'success') {
            message.success('删除全部数据成功');
            setSelectedRows([]);
            actionRef.current?.reload();
          } else {
            message.error(result.message || '删除全部数据失败');
          }
        } catch (error) {
          console.error('Delete all error:', error);
          message.error(
            '删除全部数据失败: ' +
              (error instanceof Error ? error.message : '未知错误'),
          );
        }
      },
    });
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  return (
    <Card>
      <Space
        direction="vertical"
        size="middle"
        style={{ width: '100%', marginBottom: 16 }}
      >
        <Space direction="vertical" size={4}>
          <Segmented
            value={isMaintained ? 'maintained' : 'unmaintained'}
            onChange={(value) => {
              setIsMaintained(value === 'maintained');
              if (actionRef.current?.reset) {
                actionRef.current.reset();
              }
              actionRef.current?.reload();
            }}
            options={[
              {
                label: '已维护',
                value: 'maintained',
              },
              {
                label: '待维护',
                value: 'unmaintained',
              },
            ]}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '8px',
              borderRadius: '8px',
              fontSize: '16px',
            }}
            block
          />
          <Typography.Text type="danger" style={{ fontSize: '13px' }}>
            已维护：展示必填项不缺失的数据信息
            <br />
            待维护：展示必填项缺失的数据信息
          </Typography.Text>
        </Space>

        <Form form={searchForm} layout="inline">
          {isMaintained && (
            <Form.Item name="sub_brand" label="子品牌">
              <Select
                placeholder="请选择"
                style={{ width: 200 }}
                options={subBrandOptions}
                allowClear
                onDropdownVisibleChange={async (open) => {
                  if (open) {
                    try {
                      const searchValues = await searchForm.validateFields();
                      const params = buildRequestParams({
                        is_maintained: isMaintained ? 'true' : 'false',
                        filter_list: searchValues.sub_brand || '',
                        custom_filter_data: searchValues.keyword || '',
                        upload_type: 'product',
                      });
                      const response = await fetch(`/bims/get_list?${params}`, {
                        method: 'GET',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                      });

                      if (!response.ok) {
                        console.error(
                          '获取子品牌列表失败，状态码:',
                          response.status,
                        );
                        return;
                      }

                      const result: GetListResponse = await response.json();
                      console.log('Dropdown received sub brands:', result);

                      if (
                        result.status === 'success' &&
                        result.data?.sub_brands &&
                        Array.isArray(result.data.sub_brands)
                      ) {
                        const options = result.data.sub_brands.map((item) => ({
                          label: item.sub_brand,
                          value: item.sub_brand,
                        }));
                        console.log('Dropdown transformed options:', options);
                        setSubBrandOptions(options);
                      } else {
                        // 不显示错误消息，只是设置空选项
                        console.log('No sub brands data in dropdown response');
                        setSubBrandOptions([]);
                      }
                    } catch (error) {
                      console.error(
                        'Error fetching sub brands in dropdown:',
                        error,
                      );
                      // 不显示错误消息，避免重复提示
                      setSubBrandOptions([]);
                    }
                  }
                }}
              />
            </Form.Item>
          )}
          <Form.Item name="keyword" label="搜索">
            <Input
              placeholder="商品名称/UPC"
              style={{ width: 300 }}
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button
                onClick={() => {
                  searchForm.resetFields();
                  actionRef.current?.reload();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>
      <ProTable<GoodsItem>
        headerTitle="商品管理"
        actionRef={actionRef}
        rowKey="id"
        scroll={{
          x: 3800,
        }}
        search={false}
        options={false}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          preserveSelectedRowKeys: true,
          selectedRowKeys: selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
            setSelectedRows(selectedRows);
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        toolBarRender={() => [
          selectedRows.length > 0 && (
            <Button
              key="delete"
              danger
              onClick={async () => {
                Modal.confirm({
                  title: '确认删除',
                  content: `是否确认删除选中的 ${selectedRowKeys.length} 条数据？`,
                  onOk: async () => {
                    try {
                      // 收集所有选中行的id
                      // const ids = selectedRows.map((row) => row.id);

                      const response = await fetch('/bims/delete_data', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          user: initialState?.userInfo?.username || '',
                          role: initialState?.userInfo?.type || '',
                          brand:
                            initialState?.currentBrandInfo?.brandNameCn || '',
                          upload_type: 'product',
                          id: selectedRowKeys,
                          is_maintained: isMaintained ? 'true' : 'false',
                          is_all_delete: 'false',
                        }),
                      });

                      const result = await response.json();

                      if (result.status === 'success') {
                        message.success('批量删除成功');
                        setSelectedRows([]);
                        actionRef.current?.reload();
                        actionRef.current?.clearSelected?.();
                      } else {
                        message.error(result.message || '批量删除失败');
                      }
                    } catch (error) {
                      console.error('Batch delete error:', error);
                      message.error(
                        '批量删除失败: ' +
                          (error instanceof Error ? error.message : '未知错误'),
                      );
                    }
                  },
                });
              }}
            >
              批量删除
            </Button>
          ),
          !isMaintained && (
            <Button
              key="approval"
              type="primary"
              icon={<AuditOutlined />}
              onClick={() => setApprovalModalVisible(true)}
            >
              审批
            </Button>
          ),
          !isMaintained && (
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={() => checkUserApproval(true)}
              loading={refreshingApproval}
              title="刷新审批状态"
            >
              刷新状态
            </Button>
          ),
          <Button key="deleteAll" danger onClick={handleDeleteAll}>
            删除全部
          </Button>,
          !isMaintained && (
            <Button
              key="import"
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
          ),
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={async () => {
              try {
                // 如果有选中的行，但是选中行数为0，说明当前页面没有数据
                if (
                  selectedRows.length === 0 &&
                  actionRef.current?.pageInfo?.total === 0
                ) {
                  message.warning('暂无数据');
                  return;
                }

                const searchValues = await searchForm.validateFields();
                const params = buildRequestParams({
                  is_maintained: isMaintained ? 'true' : 'false',
                  filter_list: searchValues.sub_brand || '',
                  custom_filter_data: searchValues.keyword || '',
                  ...(selectedRows.length > 0
                    ? { id: JSON.stringify(selectedRows.map((row) => row.id)) }
                    : {}),
                });
                const response = await fetch(`/bims/download_data?${params}`, {
                  method: 'GET',
                  headers: {
                    Accept: '*/*',
                  },
                });

                if (!response.ok) {
                  throw new Error('下载失败');
                }

                // 获取文件名
                const contentDisposition = response.headers.get(
                  'content-disposition',
                );
                let filename = 'download.xlsx';
                if (contentDisposition) {
                  const filenameMatch = contentDisposition.match(
                    /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
                  );
                  if (filenameMatch && filenameMatch[1]) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                  }
                }

                // 获取二进制数据并创建下载
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                message.success(
                  selectedRows.length > 0
                    ? '已选数据下载成功'
                    : '全部数据下载成功',
                );
              } catch (error) {
                console.error('Download error:', error);
                message.error(
                  '下载失败: ' +
                    (error instanceof Error ? error.message : '未知错误'),
                );
              }
            }}
          >
            {selectedRows.length > 0 ? '下载所选' : '下载全部'}
          </Button>,
        ]}
        request={async (params, sorter, filter) => {
          // console.log('Request params:', params);
          try {
            const searchValues = await searchForm.validateFields();
            // console.log('Search form values:', searchValues);

            const queryParams = buildRequestParams({
              is_maintained: isMaintained ? 'true' : 'false',
              page: String(params.current || 1),
              pageSize: String(params.pageSize || pageSize),
              filter_list: searchValues.sub_brand || '',
              custom_filter_data: searchValues.keyword || '',
            });

            // console.log('Query URL:', `/bims/select_data?${queryParams}`);

            const response = await fetch(`/bims/select_data?${queryParams}`, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
              },
              mode: 'cors',
            });

            // console.log('Response status:', response.status);
            const responseText = await response.text();
            // console.log('Response text:', responseText);

            if (!response.ok) {
              throw new Error(
                `Network response was not ok: ${response.status}`,
              );
            }

            const result = JSON.parse(responseText);
            // console.log('Parsed result:', result);

            if (result.status === 'success') {
              return {
                data: result.data.list,
                success: true,
                total: result.data.total,
              };
            }
            throw new Error(result.message || '获取数据失败');
          } catch (error) {
            console.error('Error in request:', error);
            message.error(
              '获取数据失败: ' +
                (error instanceof Error ? error.message : '未知错误'),
            );
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        pagination={{
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          defaultCurrent: 1,
          defaultPageSize: 10,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (page, size) => {
            setPageSize(size);
            actionRef.current?.reload();
          },
        }}
        dateFormatter="string"
      />

      <Modal
        title="导入商品"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Upload.Dragger
            accept=".xls,.xlsx"
            beforeUpload={(file) => {
              handleImport(file);
              return false;
            }}
            disabled={importLoading}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">将文件拖到此处，或点击选择文件</p>
            <p className="ant-upload-hint">
              请上传小于等于50M的.xls, .xlsx格式文件
            </p>
            {importLoading && (
              <div style={{ marginTop: 8 }}>
                <Spin tip="正在导入..." />
              </div>
            )}
          </Upload.Dragger>
        </Space>
      </Modal>

      <Modal
        title="编辑商品"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Text type="danger">注：带 * 号的为必填项</Typography.Text>
        </div>
        <Form
          form={editForm}
          layout="vertical"
          style={{ maxHeight: '70vh', overflowY: 'auto' }}
        >
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '16px',
            }}
          >
            <Form.Item
              name="sub_sidiary"
              label="子公司"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="sub_brand"
              label="子品牌"
              style={{ marginBottom: 12 }}
              required
              rules={[{ required: true, message: '请输入子品牌' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="mt_sub_brand"
              label="美团子品牌"
              style={{ marginBottom: 12 }}
              tooltip="不用更改"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="collect_upc"
              label="采集UPC"
              style={{ marginBottom: 12 }}
              required
              tooltip="不用更改"
              rules={[{ required: true, message: '请输入采集UPC' }]}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="upc"
              label="标准UPC"
              style={{ marginBottom: 12 }}
              required
              tooltip="经过品牌标准化处理的UPC 码"
              rules={[
                { required: true, message: '请输入UPC' },
                {
                  pattern: /^\d*$/,
                  message: 'UPC只能输入数字',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="collect_product_name"
              label="采集商品名称"
              style={{ marginBottom: 12 }}
              required
              tooltip="不用更改"
              rules={[{ required: true, message: '请输入采集商品名称' }]}
            >
              <Input placeholder="请输入" disabled />
            </Form.Item>
            <Form.Item
              name="product_name"
              label="标准商品名称"
              style={{ marginBottom: 12 }}
              required
              tooltip="经过品牌标准化处理的商品名称"
              rules={[{ required: true, message: '请输入商品名称' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="brand_product_id"
              label="品牌商品ID"
              style={{ marginBottom: 12 }}
              required={
                initialState?.currentBrandInfo?.brandNameCn === '太太乐'
              }
              tooltip="品牌体系中的唯一识别代码，非69码"
              rules={[
                {
                  required:
                    initialState?.currentBrandInfo?.brandNameCn === '太太乐',
                  message: '请输入产品ID',
                },
                {
                  pattern: /^\d+$/,
                  message: '产品ID只能输入数字',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="launch_date"
              label="上市日期"
              style={{ marginBottom: 12 }}
              tooltip="格式为：yyyymmdd"
            >
              <Input placeholder="请输入(yyyymmdd)" />
            </Form.Item>
            <Form.Item
              name="category1"
              label="一级品类"
              style={{ marginBottom: 12 }}
              required={
                initialState?.currentBrandInfo?.brandNameCn !== '上好佳'
              }
              rules={[
                {
                  required:
                    initialState?.currentBrandInfo?.brandNameCn !== '上好佳',
                  message: '请输入品类一',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="category2"
              label="二级品类"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="category3"
              label="三级品类"
              style={{ marginBottom: 12 }}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="mt_category1"
              label="美团一级品类"
              style={{ marginBottom: 12 }}
              tooltip="不用更改"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="mt_category2"
              label="美团二级品类"
              style={{ marginBottom: 12 }}
              tooltip="不用更改"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="mt_category3"
              label="美团三级品类"
              style={{ marginBottom: 12 }}
              tooltip="不用更改"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="product_role"
              label="产品角色"
              style={{ marginBottom: 12 }}
              tooltip="商品在产品线中的定位"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="product_line"
              label="产品线"
              style={{ marginBottom: 12 }}
              tooltip="商品所属的产品线"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="is_main_push_product"
              label="是否主推品"
              style={{ marginBottom: 12 }}
              tooltip="是或否"
            >
              <Select
                placeholder="请选择"
                options={[
                  { label: '是', value: '是' },
                  { label: '否', value: '否' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="is_red_line_product"
              label="是否红线品"
              style={{ marginBottom: 12 }}
              tooltip="是或否"
            >
              <Select
                placeholder="请选择"
                options={[
                  { label: '是', value: '是' },
                  { label: '否', value: '否' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="product_type"
              label="商品类型"
              style={{ marginBottom: 12 }}
              tooltip="品牌个性化的其他分类标准"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="flavor" label="口味" style={{ marginBottom: 12 }}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="size"
              label="规格"
              style={{ marginBottom: 12 }}
              tooltip="分别是如70G*2/包、330ml*6罐等"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="mt_size"
              label="美团规格"
              style={{ marginBottom: 12 }}
              tooltip="不用更改"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="packaging"
              label="包装"
              style={{ marginBottom: 12 }}
              tooltip="如盒、瓶、组包等"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="brand_price"
              label="建议零售价"
              style={{ marginBottom: 12 }}
              tooltip="精确到小数点后两位"
              rules={[
                {
                  pattern: /^\d+(\.\d{0,2})?$/,
                  message: '建议零售价只能输入数字，小数点后最多2位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="lowest_price"
              label="最低价"
              style={{ marginBottom: 12 }}
              tooltip="精确到小数点后两位"
              rules={[
                {
                  pattern: /^\d+(\.\d{0,2})?$/,
                  message: '最低价只能输入数字，小数点后最多2位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="highest_price"
              label="最高价"
              style={{ marginBottom: 12 }}
              tooltip="精确到小数点后两位"
              rules={[
                {
                  pattern: /^\d+(\.\d{0,2})?$/,
                  message: '最高价只能输入数字，小数点后最多2位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="promotion_price"
              label="活动价"
              style={{ marginBottom: 12 }}
              tooltip="精确到小数点后两位"
              rules={[
                {
                  pattern: /^\d+(\.\d{0,2})?$/,
                  message: '活动价只能输入数字，小数点后最多2位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="product_discount_tag"
              label="商品折扣标签"
              style={{ marginBottom: 12 }}
              tooltip="只能选择指定折扣选项"
            >
              <Select
                placeholder="请选择"
                options={[
                  { label: '所有折扣', value: '所有折扣' },
                  { label: '6折以上', value: '6折以上' },
                  { label: '7折以上', value: '7折以上' },
                  { label: '75折以上', value: '75折以上' },
                  { label: '8折以上', value: '8折以上' },
                  { label: '85折以上', value: '85折以上' },
                  { label: '9折以上', value: '9折以上' },
                  { label: '不参与任何折扣', value: '不参与任何折扣' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="rsp"
              label="科赴标准零售价"
              style={{ marginBottom: 12 }}
              required={
                initialState?.currentBrandInfo?.brandNameCn === '科赴' ||
                initialState?.currentBrandInfo?.brandNameCn === '通用磨坊'
              }
              tooltip="科赴商品 rsp"
              rules={[
                {
                  required:
                    initialState?.currentBrandInfo?.brandNameCn === '科赴' ||
                    initialState?.currentBrandInfo?.brandNameCn === '通用磨坊',
                  message: '请输入零售价',
                },
                {
                  pattern: /^\d+(\.\d{0,2})?$/,
                  message: '零售价只能输入数字，小数点后最多2位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="product_gts"
              label="科赴商品GTS"
              style={{ marginBottom: 12 }}
              tooltip="科赴商品 gts"
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="Qli"
              label="费列罗重量"
              style={{ marginBottom: 12 }}
              required={
                initialState?.currentBrandInfo?.brandNameCn === '费列罗'
              }
              tooltip="费列罗重量"
              rules={[
                {
                  required:
                    initialState?.currentBrandInfo?.brandNameCn === '费列罗',
                  message: '请输入费列罗重量',
                },
                {
                  pattern: /^\d*\.?\d{0,18}$/,
                  message: '费列罗重量只能输入数字，小数点后最多18位',
                },
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title="维表审批申请"
        open={approvalModalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人姓名" />
          </Form.Item>

          <Form.Item
            name="upload_info_type"
            label="上传类型"
            rules={[{ required: true, message: '请选择上传类型' }]}
          >
            <Select placeholder="请选择上传类型">
              <Select.Option value="待维护信息补全">
                待维护信息补全
              </Select.Option>
              <Select.Option value="已维护信息变更">
                已维护信息变更
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="reason"
            label="上传信息说明"
            rules={[{ required: true, message: '请输入上传信息说明' }]}
          >
            <Input.TextArea placeholder="请输入上传信息说明" rows={4} />
          </Form.Item>

          <div
            style={{
              marginTop: 16,
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
            }}
          >
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              请详细描述本次上传维表的原因，新增或者改动。若涉及改动明确描述改动内容。
              <br />
              其余备注：维表默认T+1日刷新，其他需求提需数仓。
            </Typography.Text>
          </div>
        </Form>
      </Modal>
    </Card>
  );
}
