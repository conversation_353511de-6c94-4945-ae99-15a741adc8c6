export interface FullDataUploadRequest {
  file: File;
  platform: 'meituan' | 'eleme' | 'jingdong' | 'duodian' | 'taoxianda';
  data_type: 'add' | 'replace';
  reason: 'no_download_permission' | 'cannot_share' | 'other_add' | 'platform_refresh' | 'brand_change_source' | 'campaign_settlement' | 'other_replace';
  user: string;
  brand: string;
}

export interface FullDataUploadResponse {
  status: string;
  message?: string;
  data?: {
    upload_id: string;
    file_name: string;
    total_records?: number;
    success_records?: number;
    failed_records?: number;
  };
}

export interface GetFullDataHistoryResponse {
  status: string;
  message?: string;
  data: {
    list: FullDataUploadItem[];
    total: number;
  };
} 