import { useState } from 'react';
import { useEffect, useRef } from 'react';

import {
  AuditOutlined,
  DownloadOutlined,
  InboxOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Progress,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  Typography,
  Upload,
  message,
} from 'antd';
import type { UploadProps } from 'antd';

import './index.less';

const { Dragger } = Upload;
const { Option } = Select;

export default function FullDataUpload() {
  const { initialState } = useModel('@@initialState');
  const [approvalForm] = Form.useForm();

  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [canUpload, setCanUpload] = useState(false);
  const [approvalMessage, setApprovalMessage] = useState('');
  const [refreshingApproval, setRefreshingApproval] = useState(false);

  // 获取平台对应的表格配置
  const getPlatformTableConfig = () => {
    if (!selectedPlatform) {
      return { columns: [], sampleData: [] };
    }

    switch (selectedPlatform) {
      case 'meituan':
        return {
          columns: [
            {
              title: '*时间',
              dataIndex: 'time',
              key: 'time',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*UPC码',
              dataIndex: 'upc',
              key: 'upc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌商',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌',
              dataIndex: 'brand',
              key: 'brand',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*一级类目',
              dataIndex: 'category1',
              key: 'category1',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*二级类目',
              dataIndex: 'category2',
              key: 'category2',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*渠道类型',
              dataIndex: 'channelType',
              key: 'channelType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*渠道名称',
              dataIndex: 'channelName',
              key: 'channelName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*销售额',
              dataIndex: 'sales',
              key: 'sales',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*销量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 80,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              time: '20250623',
              productName: '示例商品名称',
              upc: '6900000000000',
              brandName: '示例品牌商',
              brand: '示例子品牌',
              category1: '粮油调味干货',
              category2: '调味料',
              province: '上海市',
              city: '上海',
              channelType: '超市',
              channelName: "Ole'超市",
              sales: '58.80',
              quantity: '1',
            },
          ],
        };

      case 'eleme':
        return {
          columns: [
            {
              title: '*二级作业实体名称',
              dataIndex: 'entityName',
              key: 'entityName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*日期',
              dataIndex: 'date',
              key: 'date',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*UPC',
              dataIndex: 'upc',
              key: 'upc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*品牌名称',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*一级类目',
              dataIndex: 'category1',
              key: 'category1',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*二级类目',
              dataIndex: 'category2',
              key: 'category2',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*三级类目',
              dataIndex: 'category3',
              key: 'category3',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*城市名称',
              dataIndex: 'cityName',
              key: 'cityName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*渠道名称',
              dataIndex: 'channelName',
              key: 'channelName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*行业名称',
              dataIndex: 'industryName',
              key: 'industryName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*销售额',
              dataIndex: 'sales',
              key: 'sales',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*销量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*订单量',
              dataIndex: 'orderCount',
              key: 'orderCount',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*笔单价',
              dataIndex: 'avgOrderPrice',
              key: 'avgOrderPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*下单用户数',
              dataIndex: 'userCount',
              key: 'userCount',
              width: 100,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              entityName: '示例品牌商',
              date: '20250623',
              upc: '6900000000000',
              productName: '示例商品名称',
              brandName: '示例子品牌',
              category1: '肉禽蛋类',
              category2: '鸡肉',
              category3: '鸡胸肉',
              cityName: '深圳',
              channelName: '叮咚买菜',
              industryName: '零售',
              sales: '379.99',
              quantity: '37',
              orderCount: '32',
              avgOrderPrice: '11.9',
              userCount: '32',
            },
          ],
        };

      case 'jingdong':
        return {
          columns: [
            {
              title: '*日期',
              dataIndex: 'date',
              key: 'date',
              width: 100,
              align: 'center' as const,
            },
            {
              title: (
                <span>
                  *订单编号
                  <Tooltip title="若无填NULL">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              ),
              dataIndex: 'orderNo',
              key: 'orderNo',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: (
                <span>
                  *K1
                  <Tooltip title="若填写中文请钉钉告知第三审批人">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              ),
              dataIndex: 'k1',
              key: 'k1',
              width: 200,
              align: 'center' as const,
            },
            {
              title: (
                <span>
                  *K2
                  <Tooltip title="若填写中文请钉钉告知第三审批人">
                    <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              ),
              dataIndex: 'k2',
              key: 'k2',
              width: 200,
              align: 'center' as const,
            },
            {
              title: '*子品牌',
              dataIndex: 'subBrand',
              key: 'subBrand',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*一级品类',
              dataIndex: 'category1',
              key: 'category1',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*二级品类',
              dataIndex: 'category2',
              key: 'category2',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*UPC',
              dataIndex: 'upc',
              key: 'upc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品销量（件）',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*销售金额（元）',
              dataIndex: 'salesAmount',
              key: 'salesAmount',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*GMV（元）',
              dataIndex: 'gmv',
              key: 'gmv',
              width: 100,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              date: '2025-06-23',
              orderNo:
                'tqjbFpwQDsNyDW+ykrxWaioPHPxWfCTglXrBJiFoUYdDHXYs7zZGjrDkwbpIEQhM',
              city: '杭州',
              k1: 'B0122B4733441B23C81AE1032E451394',
              k2: '697AF972E83D369869372A66D8890A7F',
              subBrand: '示例子品牌',
              category1: '肉品',
              category2: '禽类',
              productName: '示例商品名称',
              upc: '6900000000000',
              quantity: '2',
              salesAmount: '21.78',
              gmv: '21.80',
            },
          ],
        };

      case 'duodian':
        return {
          columns: [
            {
              title: '*月份',
              dataIndex: 'month',
              key: 'month',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*日期',
              dataIndex: 'date',
              key: 'date',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌',
              dataIndex: 'brand',
              key: 'brand',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*分类',
              dataIndex: 'category',
              key: 'category',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*子分类',
              dataIndex: 'subCategory',
              key: 'subCategory',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*到店到家',
              dataIndex: 'serviceType',
              key: 'serviceType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*业态',
              dataIndex: 'businessType',
              key: 'businessType',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*国条码',
              dataIndex: 'barcode',
              key: 'barcode',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商家',
              dataIndex: 'merchant',
              key: 'merchant',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*物料码',
              dataIndex: 'materialCode',
              key: 'materialCode',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*sku_id',
              dataIndex: 'skuId',
              key: 'skuId',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品类型',
              dataIndex: 'productType',
              key: 'productType',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*gmv',
              dataIndex: 'gmv',
              key: 'gmv',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*组合分摊GMV',
              dataIndex: 'combinedGmv',
              key: 'combinedGmv',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*订单量',
              dataIndex: 'orderCount',
              key: 'orderCount',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*销售数量',
              dataIndex: 'salesQuantity',
              key: 'salesQuantity',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*成交用户数',
              dataIndex: 'userCount',
              key: 'userCount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*客单价',
              dataIndex: 'avgOrderPrice',
              key: 'avgOrderPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*件均价',
              dataIndex: 'avgItemPrice',
              key: 'avgItemPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*促销优惠金额',
              dataIndex: 'promotionDiscount',
              key: 'promotionDiscount',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*优惠券优惠金额',
              dataIndex: 'couponDiscount',
              key: 'couponDiscount',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*实付金额',
              dataIndex: 'actualAmount',
              key: 'actualAmount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*销售金额',
              dataIndex: 'salesAmount',
              key: 'salesAmount',
              width: 100,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              month: '202506',
              date: '20250623',
              brand: '示例子品牌',
              category: '未定义',
              subCategory: '未定义',
              serviceType: '到家',
              businessType: 'O2O',
              province: '湖南省',
              city: '长沙市',
              barcode: '6900000000000',
              productName: '示例商品名称',
              merchant: '西格蕾',
              materialCode: '100000',
              skuId: '1070450000',
              productType: '标准国条',
              gmv: '18156.60',
              combinedGmv: '0.00',
              orderCount: '97',
              salesQuantity: '1834',
              userCount: '87',
              avgOrderPrice: '187.18',
              avgItemPrice: '9.90',
              promotionDiscount: '0.00',
              couponDiscount: '6065.86',
              actualAmount: '12090.74',
              salesAmount: '18156.60',
            },
          ],
        };

      case 'taoxianda':
        return {
          columns: [
            {
              title: '*二级实体组',
              dataIndex: 'entityGroup',
              key: 'entityGroup',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*月份/日期',
              dataIndex: 'date',
              key: 'date',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*省份',
              dataIndex: 'province',
              key: 'province',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*城市',
              dataIndex: 'city',
              key: 'city',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*商户名称',
              dataIndex: 'merchantName',
              key: 'merchantName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*门店名称',
              dataIndex: 'storeName',
              key: 'storeName',
              width: 150,
              align: 'center' as const,
            },
            {
              title: '*一级类目',
              dataIndex: 'category1',
              key: 'category1',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*二级类目',
              dataIndex: 'category2',
              key: 'category2',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*三级类目',
              dataIndex: 'category3',
              key: 'category3',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*品牌名称',
              dataIndex: 'brandName',
              key: 'brandName',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*商品UPC码',
              dataIndex: 'upc',
              key: 'upc',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*商品名称',
              dataIndex: 'productName',
              key: 'productName',
              width: 120,
              align: 'center' as const,
            },
            {
              title: '*销售额',
              dataIndex: 'sales',
              key: 'sales',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*购买用户数',
              dataIndex: 'userCount',
              key: 'userCount',
              width: 100,
              align: 'center' as const,
            },
            {
              title: '*客单价',
              dataIndex: 'avgOrderPrice',
              key: 'avgOrderPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*订单数',
              dataIndex: 'orderCount',
              key: 'orderCount',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*笔单价',
              dataIndex: 'avgItemPrice',
              key: 'avgItemPrice',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*销量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: 80,
              align: 'center' as const,
            },
            {
              title: '*单笔件数',
              dataIndex: 'itemsPerOrder',
              key: 'itemsPerOrder',
              width: 100,
              align: 'center' as const,
            },
          ],
          sampleData: [
            {
              key: '1',
              entityGroup: '示例组织名称',
              date: '20250623',
              province: '江苏省',
              city: '苏州市',
              merchantName: '大润发',
              storeName: '大润发前进西路店',
              category1: '肉类禽蛋',
              category2: '禽肉类',
              category3: '鸡肉类',
              brandName: '示例品牌商',
              upc: '6924766600000',
              productName: '示例商品名称',
              sales: '16.90',
              userCount: '1',
              avgOrderPrice: '16.90',
              orderCount: '1',
              avgItemPrice: '16.90',
              quantity: '1',
              itemsPerOrder: '1.00',
            },
          ],
        };

      default:
        return { columns: [], sampleData: [] };
    }
  };

  const { columns, sampleData } = getPlatformTableConfig() as {
    columns: any[];
    sampleData: any[];
  };

  // 平台选项
  const platformOptions = [
    { label: '美团', value: 'meituan' },
    { label: '饿了么', value: 'eleme' },
    { label: '京东到家', value: 'jingdong' },
    { label: '多点', value: 'duodian' },
    { label: '淘鲜达', value: 'taoxianda' },
  ];

  // 数据源类型选项
  const dataTypeOptions = [
    { label: '新增数据源', value: 'add' },
    { label: '替换数据源', value: 'replace' },
  ];

  // 获取原因选项
  const getReasonOptions = () => {
    if (selectedDataType === 'add') {
      return [
        { label: '平台无下载权限', value: 'no_download_permission' },
        { label: '有下载权限，无法分享', value: 'cannot_share' },
        { label: '其他原因', value: 'other_add' },
      ];
    } else if (selectedDataType === 'replace') {
      return [
        { label: '平台刷数', value: 'platform_refresh' },
        { label: '品牌方更改数据源', value: 'brand_change_source' },
        { label: '其他原因', value: 'other_replace' },
      ];
    }
    return [];
  };

  // 添加通用请求配置
  const fetchConfig = {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    mode: 'cors' as RequestMode,
  };

  // 构建请求参数
  const buildRequestParams = (additionalParams: Record<string, any> = {}) => {
    const params = {
      user: initialState?.userInfo?.username || '',
      role: initialState?.userInfo?.type || '',
      brand: initialState?.currentBrandInfo?.brandNameCn || '',
      upload_type: 'full_data',
      ...additionalParams,
    };

    return Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
      .join('&');
  };

  // 检查用户审批状态
  const checkUserApproval = async () => {
    setRefreshingApproval(true);
    try {
      const params = buildRequestParams({
        platform:
          platformOptions.find((p) => p.value === selectedPlatform)?.label ||
          '',
      });

      const response = await fetch(`/bims/check_user_approval?${params}`, {
        method: 'GET',
        ...fetchConfig,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.status === 'success') {
        const canUpload = result.data?.can_upload || false;
        setCanUpload(canUpload);
        setApprovalMessage(result.data?.message || result.message || '');

        if (canUpload) {
          message.success('审批状态已更新');
        } else {
          message.info(
            result.data?.message || result.message || '审批状态已更新',
          );
        }
      }
    } catch (error) {
      console.error('检查审批状态失败:', error);
      message.error('获取审批状态失败');
      setCanUpload(false);
    } finally {
      setRefreshingApproval(false);
    }
  };

  useEffect(() => {
    // 首次检查审批状态
    checkUserApproval();
  }, []);

  // 上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file) => {
      if (!selectedPlatform) {
        message.error('请先选择平台！');
        return false;
      }
      if (!selectedDataType) {
        message.error('请先选择数据源类型！');
        return false;
      }
      if (!selectedReason) {
        message.error('请先选择原因！');
        return false;
      }

      const isValidType =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel' ||
        file.type === 'text/csv';
      if (!isValidType) {
        message.error('只支持上传 Excel 文件 (.xlsx, .xls) 或 CSV 文件！');
        return false;
      }
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('文件大小不能超过 100MB！');
        return false;
      }
      return true;
    },
    customRequest: async ({ file, onProgress, onSuccess, onError }) => {
      try {
        setUploading(true);
        const formData = new FormData();
        formData.append('file', file as File);
        formData.append('upload_type', 'full_data');

        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(percent);
            onProgress?.({ percent });
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.status === 'success') {
              // 如果有message且包含错误信息，显示message；否则显示成功消息
              if (
                response.message &&
                (response.message.includes('数据有误') ||
                  response.message.includes('失败'))
              ) {
                message.warning(response.message);
              } else {
                message.success('文件上传成功！');
              }
              onSuccess?.(response);
              // 上传成功后清空下拉选择框
              setTimeout(() => {
                setSelectedPlatform('');
                setSelectedDataType('');
                setSelectedReason('');
              }, 1500);
            } else {
              message.error(response.message || '上传失败');
              onError?.(new Error(response.message));
            }
          } else {
            message.error('上传失败');
            onError?.(new Error('Upload failed'));
          }
          setUploading(false);
          setUploadProgress(0);
          setUploadModalVisible(false);
        });

        xhr.addEventListener('error', () => {
          message.error('上传失败');
          onError?.(new Error('Upload failed'));
          setUploading(false);
          setUploadProgress(0);
          setUploadModalVisible(false);
        });

        const platformLabel =
          platformOptions.find((p) => p.value === selectedPlatform)?.label ||
          '';
        const dataTypeLabel =
          dataTypeOptions.find((d) => d.value === selectedDataType)?.label ||
          '';
        const reasonLabel =
          getReasonOptions().find((r) => r.value === selectedReason)?.label ||
          '';
        const params = new URLSearchParams({
          user: initialState?.userInfo?.username || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          platform: platformLabel,
          data_type: dataTypeLabel,
          reason: reasonLabel,
        });

        xhr.open('POST', `/bims/upload_full_data?${params.toString()}`);
        xhr.send(formData);
      } catch (error) {
        message.error('上传失败');
        onError?.(error as Error);
        setUploading(false);
        setUploadProgress(0);
        setUploadModalVisible(false);
      }
    },
    showUploadList: false,
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    if (!selectedPlatform) {
      message.error('请先选择平台！');
      return;
    }

    try {
      const platformLabel =
        platformOptions.find((p) => p.value === selectedPlatform)?.label || '';
      const params = new URLSearchParams({
        platform: platformLabel,
      });

      const response = await fetch(
        `/bims/download_full_data_template?${params}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        `全量数据上传模板_${platformOptions.find((p) => p.value === selectedPlatform)?.label}.xlsx`,
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('模板下载成功');
    } catch (error) {
      message.error('模板下载失败');
    }
  };

  // 审批处理
  const handleApprovalSubmit = async () => {
    try {
      const values = await approvalForm.validateFields();

      const response = await fetch('/bims/submit_approval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user: initialState?.userInfo?.username || '',
          role: initialState?.userInfo?.type || '',
          brand: initialState?.currentBrandInfo?.brandNameCn || '',
          upload_type: 'full_data',
          platform:
            platformOptions.find((p) => p.value === selectedPlatform)?.label ||
            '',
          data_type:
            dataTypeOptions.find((d) => d.value === selectedDataType)?.label ||
            '',
          reason:
            getReasonOptions().find((r) => r.value === selectedReason)?.label ||
            '',
          applicant: values.applicant,
          upload_info_type: values.upload_info_type,
          reason_description: values.reason,
        }),
      });

      const result = await response.json();
      if (result.status === 'success') {
        message.success('审批提交成功');
        setApprovalModalVisible(false);
        approvalForm.resetFields();
      } else {
        message.error(result.message || '审批提交失败');
      }
    } catch (error) {
      message.error('审批提交失败');
    }
  };

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
        <Form layout="inline" style={{ marginBottom: 16 }}>
          <Form.Item label="平台" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择平台"
              value={selectedPlatform}
              onChange={(value) => setSelectedPlatform(value)}
              style={{ width: 200 }}
              allowClear
            >
              {platformOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="数据源类型" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择数据源类型"
              value={selectedDataType}
              onChange={(value) => {
                setSelectedDataType(value);
                setSelectedReason(''); // 重置原因选择
              }}
              style={{ width: 200 }}
              allowClear
            >
              {dataTypeOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="原因" style={{ marginBottom: 8 }}>
            <Select
              placeholder="请选择原因"
              value={selectedReason}
              onChange={(value) => setSelectedReason(value)}
              style={{ width: 250 }}
              disabled={!selectedDataType}
              allowClear
            >
              {getReasonOptions().map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Space>

      {/* Excel数据展示区域 */}
      <Card
        title="Excel导入示例"
        style={{ marginTop: 16 }}
        extra={
          <Space>
            <Button
              type="primary"
              icon={<AuditOutlined />}
              onClick={() => {
                const reasonLabel =
                  getReasonOptions().find((r) => r.value === selectedReason)
                    ?.label || '';
                const dataTypeLabel =
                  dataTypeOptions.find((d) => d.value === selectedDataType)
                    ?.label || '';
                approvalForm.setFieldsValue({
                  reason: `${reasonLabel}，`,
                  upload_info_type: `全量数据${dataTypeLabel}`,
                });
                setApprovalModalVisible(true);
              }}
              disabled={
                !selectedPlatform || !selectedDataType || !selectedReason
              }
            >
              审批
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={checkUserApproval}
              loading={refreshingApproval}
              title="刷新审批状态"
            >
              刷新状态
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
              disabled={!selectedPlatform}
              title={!selectedPlatform ? '请先选择平台' : ''}
            >
              下载模板
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              disabled={
                !canUpload ||
                !selectedPlatform ||
                !selectedDataType ||
                !selectedReason
              }
              title={!canUpload ? approvalMessage : ''}
            >
              导入
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Typography.Text type="secondary">
              *每次导入的数据不能超过100万行，超过部分格被截断，请缩小筛选范国后导入
            </Typography.Text>
            <Typography.Text type="secondary">*必填</Typography.Text>
            <Typography.Text type="secondary">
              *导入前检查格式，平台下载格式导入
            </Typography.Text>
          </Space>
        </div>

        {selectedPlatform ? (
          <Table
            columns={columns}
            dataSource={sampleData}
            pagination={false}
            scroll={{ x: 'max-content' }}
            size="small"
            bordered
            style={{
              backgroundColor: '#ffffff',
            }}
            rowClassName={(record: any) => {
              return record.__isTipRow ? 'tip-row-style' : '';
            }}
          />
        ) : (
          <div
            style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
          >
            请先选择平台查看相应的数据格式
          </div>
        )}
      </Card>

      <Modal
        title="导入全量数据"
        open={uploadModalVisible}
        onCancel={() => {
          if (!uploading) {
            setUploadModalVisible(false);
            setUploadProgress(0);
          }
        }}
        footer={null}
        closable={!uploading}
        maskClosable={!uploading}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {uploading ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin tip="正在导入..." />
              <Progress
                percent={uploadProgress}
                status="active"
                style={{ marginTop: 16 }}
              />
            </div>
          ) : (
            <Upload.Dragger {...uploadProps} accept=".xls,.xlsx,.csv">
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">将文件拖到此处，或点击选择文件</p>
              <p className="ant-upload-hint">
                请上传小于等于100M的.xls, .xlsx, .csv格式文件
              </p>
            </Upload.Dragger>
          )}
        </Space>
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title="维表审批申请"
        open={approvalModalVisible}
        onOk={handleApprovalSubmit}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        width={600}
        okText="确定"
        cancelText="取消"
      >
        <Form form={approvalForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="applicant"
            label="申请人"
            rules={[{ required: true, message: '请输入申请人' }]}
          >
            <Input placeholder="请输入申请人姓名" />
          </Form.Item>

          <Form.Item
            name="upload_info_type"
            label="上传类型"
            initialValue={
              dataTypeOptions.find((d) => d.value === selectedDataType)?.label
                ? `全量数据${dataTypeOptions.find((d) => d.value === selectedDataType)?.label}`
                : ''
            }
          >
            <Input disabled style={{ backgroundColor: '#f5f5f5' }} />
          </Form.Item>

          <Form.Item
            name="reason"
            label="上传信息说明"
            rules={[{ required: true, message: '请输入上传信息说明' }]}
          >
            <Input.TextArea placeholder="请输入上传信息说明" rows={4} />
          </Form.Item>

          <div
            style={{
              marginTop: 16,
              padding: '12px',
              backgroundColor: '#f6f8fa',
              borderRadius: '6px',
            }}
          >
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              请详细描述本次上传全量数据的原因，新增或者替换。若涉及替换明确描述替换内容。
              <br />
              其余备注：维表上传后，默认回刷 2024 年 1 月 1
              日至今的数据。若需回刷其他日期范围的数据，请在此备注说明
            </Typography.Text>
          </div>
        </Form>
      </Modal>
    </Card>
  );
}
