import { useRef, useState } from 'react';

import { AudioOutlined, XFilled } from '@ant-design/icons';
import { RecordRTCPromisesHandler } from 'recordrtc';

type SpeechProps = {
  onResult: (result: string) => void;
  onLoading?: (loading: boolean) => void;
};

const Speech = (props: SpeechProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const recordRTCRef = useRef<RecordRTCPromisesHandler | null>(null);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      recordRTCRef.current = new RecordRTCPromisesHandler(stream, {
        type: 'audio',
        mimeType: 'audio/webm;codecs=pcm',
        sampleRate: 8000,
        numberOfAudioChannels: 1,
      });
      recordRTCRef.current.startRecording();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const stopRecording = async () => {
    await recordRTCRef.current?.stopRecording();
    setIsRecording(false);
    const blob = await recordRTCRef.current?.getBlob();
    await sendAudioToServer(blob!);
  };

  const sendAudioToServer = async (audioBlob: Blob) => {
    props.onLoading?.(true);
    const formData = new FormData();
    formData.append('file', audioBlob, 'recording.wav');

    try {
      const response = await fetch('/analyst/v2/upload_audio', {
        method: 'POST',
        body: formData,
      });
      if (!response.ok) {
        throw new Error('Upload failed');
      }
      const res = await response.json();
      props.onResult(res.text);
    } catch (error) {
      console.error('Error uploading audio:', error);
    } finally {
      props.onLoading?.(false);
    }
  };

  if (!MediaRecorder.isTypeSupported('audio/webm;codecs=pcm')) {
    return <></>;
  }

  return (
    <>
      {isRecording ? (
        // <div
        //   className="animate__animated animate__heartBeat animate__infinite h-[20px] w-[20px] cursor-pointer rounded-full bg-red-600"
        //   onClick={stopRecording}
        // />
        <XFilled
          // className="animate__animated animate__heartBeat animate__infinite cursor-pointer"
          className="cursor-pointer"
          style={{ fontSize: 20 }}
          onClick={stopRecording}
        />
      ) : (
        <AudioOutlined
          style={{ fontSize: 20, cursor: 'pointer' }}
          onClick={startRecording}
        />
      )}
    </>
  );
};

export default Speech;
