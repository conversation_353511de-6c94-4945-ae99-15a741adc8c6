import { useEffect, useRef, useState } from 'react';

import { DownOutlined } from '@ant-design/icons';
import autoAnimate from '@formkit/auto-animate';
import { Spin } from 'antd';

import { api } from '../model';

type Props = {
  value?: string;
  onClickHistory?: (id: string) => void;
  onClickNew?: () => void;
};

export default ({ onClickHistory, value, onClickNew }: Props) => {
  const [historys, setHistorys] = useState<
    {
      create_time: string;
      dialog_id: string;
      question: string;
    }[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const parent = useRef(null);

  useEffect(() => {
    parent.current && autoAnimate(parent.current);
  }, [parent]);

  const getHistory = () => {
    setLoading(true);
    api
      .getHistory()
      .then((res) => {
        if (res.result) {
          setHistorys(res.result);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getHistory();
  }, []);
  return (
    <div className="mr-[18px] flex h-full w-[229px] flex-col rounded-[16px] bg-white p-[16px]">
      <div
        className="flex h-[40px] flex-shrink-0 cursor-pointer items-center rounded-[6px] pl-[15px] text-[14px] text-[#0095FF]"
        style={{
          background: 'rgba(0, 149, 255, 0.2)',
        }}
        onClick={() => {
          onClickNew?.();
          getHistory();
        }}
      >
        + 新建对话分析
      </div>
      <div className="mt-[10px] flex flex-1 flex-col overflow-hidden">
        <div
          className="flex h-[40px] cursor-pointer items-center justify-between rounded-[6px] bg-[#E4EDF2] px-[15px] text-[14px] text-[#8291A9]"
          onClick={() => {
            setCollapsed((prev) => !prev);
          }}
        >
          网页历史对话
          <DownOutlined
            className="transition-transform"
            style={{
              transform: collapsed ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
          />
        </div>

        <div
          ref={parent}
          className="nogundong flex-1 overflow-auto px-[15px] py-[10px]"
          // style={{
          //   height: collapsed ? 0 : 'unset',
          //   overflow: collapsed ? 'hidden' : 'auto',
          //   flex: collapsed ? 0 : 1,
          //   padding: collapsed ? 0 : '15px 10px',
          // }}
        >
          <Spin spinning={loading} delay={500} />
          {!loading &&
            !collapsed &&
            historys.map((item) => (
              <div
                key={item.dialog_id}
                className="nogundong mb-[10px] w-full cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap rounded-md px-[8px] py-[4px] text-[12px] font-light text-[#8291A9] last:mb-0"
                style={{
                  background:
                    value === item.dialog_id ? 'rgba(0,149,255,0.08)' : '',
                }}
                onClick={() => onClickHistory?.(item.dialog_id)}
              >
                {item.question}
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};
