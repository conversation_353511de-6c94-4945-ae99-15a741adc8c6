import { BRAND_INFO_STORAGE_KEY, USER_ID_STORAGE_KEY } from '@/constants';

// 发送 POST 请求
async function postData(url = '', data = {}) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort(); // 超时后中止请求
    }, 30000);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        user_id: localStorage.getItem(USER_ID_STORAGE_KEY),
        // brand: '百事食品',
        brand: JSON.parse(localStorage.getItem(BRAND_INFO_STORAGE_KEY) || '{}')
          .brandNameCn,
      }),
      signal: controller.signal, // 传递 signal 来支持中止请求
    });

    clearTimeout(timeoutId); // 请求成功时清除超时定时器

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('timeout');
    } else {
      throw error;
    }
  }
}

export const api = {
  send(data: { input_data: string; dialog_id: string }) {
    return postData('/analyst/api/process', data);
  },
  getDialogById(data: { dialog_id: string }) {
    return postData('/analyst/api/history', {
      limit_num: 100,
      ...data,
    });
  },
  getHistory() {
    return postData('/analyst/api/dialog_list');
  },
};
