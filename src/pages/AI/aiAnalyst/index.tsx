import { useRef, useState } from 'react';

import {
  LoadingOutlined,
  PauseCircleOutlined,
  SoundOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Input, Spin } from 'antd';
import { v4 as uuidv4 } from 'uuid';

import Sider from './components/Sider';
import Speech from './components/Speech';
import './index.less';
import { api } from './model';

const { TextArea } = Input;

export default function AIAnalyst() {
  const [inputValue, setInputValue] = useState('');
  const [dialogId, setDialogId] = useState(uuidv4());
  const loadingRef = useRef(false);
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const [loadingTransAudio, setLoadingTransAudio] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  // 判断是不是中文输入法, 输入中时，不触发onPressEnter事件
  const isComposingRef = useRef(false);
  const [suggests, setSuggests] = useState([
    '美好的一天，我心情不错，你呢？',
    '9月12日的销量是多少?',
    '最近一周的活动核销GMV一共有多少?',
    '最近一周，广告投放共带来了多少曝光和点击?',
  ]);
  const [playingAudioSrc, setPlayingAudioSrc] = useState<string | null>(null);
  const [conversations, setConversations] = useState<
    {
      answer?: string;
      answer_time?: string;
      question: string;
      question_time?: string;
      audioSrc?: string;
    }[]
  >([]);
  const audioRef = useRef(new Audio());

  const send = (question?: string) => {
    loadingRef.current = true;
    setConversations((prev) => [
      ...prev,
      {
        question: question || inputValue,
      },
    ]);
    setTimeout(() => {
      contentRef.current?.scrollTo({
        top: contentRef.current?.scrollHeight,
        behavior: 'smooth',
      });
    });
    api
      .send({
        input_data: question || inputValue,
        dialog_id: dialogId,
      })
      .then((res) => {
        if (res.result) {
          fetchAnswerAudio(res.result)
            .then((src) => {
              setConversations((prev) => [
                ...prev.slice(0, prev.length - 1),
                {
                  answer: res.result,
                  question: question || inputValue,
                  audioSrc: src,
                },
              ]);
            })
            .catch(() => {
              setConversations((prev) => [
                ...prev.slice(0, prev.length - 1),
                {
                  answer: res.result,
                  question: question || inputValue,
                },
              ]);
            });

          setTimeout(() => {
            contentRef.current?.scrollTo({
              top: contentRef.current?.scrollHeight,
              behavior: 'smooth',
            });
          });
        }
      })
      .catch((err) => {
        if (err.message === 'timeout') {
          setConversations((prev) => [
            ...prev.slice(0, prev.length - 1),
            {
              answer: '这个问题小易还在学习中，您可以换一个问题试试',
              question: question || inputValue,
            },
          ]);
        } else {
          setConversations((prev) => [
            ...prev.slice(0, prev.length - 1),
            {
              answer: '网络错误, 稍后再试',
              question: question || inputValue,
            },
          ]);
        }
        setTimeout(() => {
          contentRef.current?.scrollTo({
            top: contentRef.current?.scrollHeight,
            behavior: 'smooth',
          });
        });
      })
      .finally(() => {
        loadingRef.current = false;
      });
  };

  const fetchAnswerAudio = async (text?: string) => {
    if (!text) {
      return;
    }
    try {
      const response = await fetch('/analyst/v2/use_audio', {
        method: 'POST',
        body: JSON.stringify({
          answer: text,
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Upload failed');
      }
      const res = await response.json();
      return res.text;
    } catch (error) {
      console.error('Error uploading audio:', error);
    }
  };

  const handleReadAnswer = async (text: string) => {
    audioRef.current.pause();
    audioRef.current.src = text;
    audioRef.current.play();
  };

  const handleStopReadAnswer = () => {
    audioRef.current.pause();
    setPlayingAudioSrc(null);
  };

  // if (
  //   !['百事食品'].includes(initialState?.currentBrandInfo?.brandNameCn || '')
  // ) {
  //   return (
  //     <img
  //       src={require('@/assets/images/bg-AI.png')}
  //       className="block h-auto w-full"
  //       alt=""
  //     />
  //   );
  // }

  return (
    <iframe
      // src={`http://111.229.254.147:8080/?brand=${initialState?.currentBrandInfo?.brandNameCn}`}
      src={`https://ai-data-analysis.yidigi.com?brand=${initialState?.currentBrandInfo?.brandNameCn}`}
    />
  );

  // return (
  //   <div className="flex h-[70vh] w-full">
  //     <Sider
  //       value={dialogId}
  //       onClickNew={() => {
  //         setDialogId(uuidv4());
  //         setConversations([]);
  //       }}
  //       onClickHistory={(id) => {
  //         setDialogId(id);
  //         setLoading(true);
  //         api
  //           .getDialogById({
  //             dialog_id: id,
  //           })
  //           .then((res) => {
  //             if (res.result) {
  //               setConversations(res.result.reverse());
  //               setTimeout(() => {
  //                 contentRef.current?.scrollTo({
  //                   top: contentRef.current?.scrollHeight,
  //                   behavior: 'smooth',
  //                 });
  //               });
  //             }
  //           })
  //           .finally(() => {
  //             setLoading(false);
  //           });
  //       }}
  //     />
  //     <div
  //       className="flex flex-1 flex-col gap-8 overflow-hidden rounded-[16px] bg-[#F5FAFF] bg-no-repeat p-[40px]"
  //       style={{
  //         backgroundImage: `url(${require('@/assets/images/ai/bg.png')})`,
  //         backgroundPosition: 'top',
  //         backgroundSize: '100% auto',
  //       }}
  //     >
  //       <div
  //         className="nogundong relative flex flex-1 flex-col gap-[20px] overflow-auto p-[20px]"
  //         ref={contentRef}
  //       >
  //         <Spin spinning={loading} delay={500} className="flex-1" />
  //         {!loading &&
  //           conversations.map((item) => (
  //             <>
  //               <div className="w-fit max-w-[80%] self-end rounded-[10px] bg-[#2C7CFF] p-[14px] text-[16px] text-white">
  //                 {item.question}
  //               </div>
  //               <div className="relative flex w-fit max-w-[80%] gap-2">
  //                 <pre className="m-0 whitespace-pre-wrap break-words rounded-[10px] bg-white p-[14px] text-[16px] text-[#333333] outline-0">
  //                   {item.answer || <LoadingOutlined />}
  //                 </pre>
  //                 {item.audioSrc && (
  //                   <>
  //                     {item.audioSrc && playingAudioSrc !== item.audioSrc && (
  //                       <SoundOutlined
  //                         style={{
  //                           fontSize: 20,
  //                           position: 'absolute',
  //                           right: -30,
  //                           bottom: 10,
  //                           cursor: 'pointer',
  //                         }}
  //                         onClick={() => {
  //                           if (!item.audioSrc) {
  //                             return;
  //                           }
  //                           setPlayingAudioSrc(item.audioSrc);
  //                           handleReadAnswer(item.audioSrc);
  //                         }}
  //                       />
  //                     )}
  //                     {item.audioSrc && playingAudioSrc === item.audioSrc && (
  //                       <PauseCircleOutlined
  //                         style={{
  //                           fontSize: 20,
  //                           position: 'absolute',
  //                           right: -30,
  //                           bottom: 10,
  //                           cursor: 'pointer',
  //                         }}
  //                         onClick={handleStopReadAnswer}
  //                       />
  //                     )}
  //                   </>
  //                 )}
  //               </div>
  //             </>
  //           ))}
  //         {!loading && conversations.length === 0 && (
  //           <div className="mt-auto grid w-full grid-cols-2 grid-rows-2 gap-[10px]">
  //             {suggests.map((item) => (
  //               <div
  //                 key={item}
  //                 className="cursor-pointer overflow-hidden text-ellipsis whitespace-nowrap rounded-md border border-solid border-gray-200 bg-white p-[10px]"
  //                 onClick={() => {
  //                   send(item);
  //                 }}
  //               >
  //                 {item}
  //               </div>
  //             ))}
  //           </div>
  //         )}
  //       </div>
  //       <div className="flex gap-3 rounded-[8px] border border-solid border-[#0095FF] bg-white p-[10px]">
  //         {loadingTransAudio ? (
  //           <div className="flex w-full items-center">
  //             <LoadingOutlined />
  //           </div>
  //         ) : (
  //           <TextArea
  //             style={{
  //               border: 'none',
  //               boxShadow: 'none',
  //             }}
  //             disabled={loadingTransAudio}
  //             autoSize={{ minRows: 1, maxRows: 6 }}
  //             value={inputValue}
  //             onChange={(e) => {
  //               setInputValue(e.currentTarget.value);
  //             }}
  //             onCompositionStart={() => {
  //               isComposingRef.current = true;
  //             }}
  //             onCompositionEnd={() => {
  //               isComposingRef.current = false;
  //             }}
  //             onPressEnter={(event) => {
  //               if (isComposingRef.current) {
  //                 return;
  //               }
  //               event.preventDefault();
  //               if (loadingRef.current || !inputValue) {
  //                 return;
  //               }
  //               send();
  //               setInputValue('');
  //             }}
  //           />
  //         )}
  //         <div className="flex flex-shrink-0 items-center gap-4 self-end">
  //           <Speech
  //             onResult={(value) => {
  //               setInputValue(value);
  //             }}
  //             onLoading={setLoadingTransAudio}
  //           />
  //           <img
  //             src={require('@/assets/images/ai/send.png')}
  //             width={32}
  //             height={32}
  //             alt=""
  //             onClick={() => {
  //               if (loadingRef.current || !inputValue) {
  //                 return;
  //               }
  //               send();
  //               setInputValue('');
  //             }}
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   </div>
  // );
}
