import { useEffect } from 'react';

import { findFirstValidRoute } from '@/utils';
import { history, useModel } from '@umijs/max';

import routes from '../../../config/routes';

const Welcome = () => {
  const { access } = useModel('@@initialState', (model) => ({
    access: model.initialState?.access,
  }));

  useEffect(() => {
    if (access) {
      const firstValidRoute = findFirstValidRoute(routes || [], access);
      if (firstValidRoute) {
        history.replace(firstValidRoute.path);
        return;
      }
      history.replace('/fullAmountBI/dataOverview');
    }
  }, [access]);

  return <></>;
};

export default Welcome;
