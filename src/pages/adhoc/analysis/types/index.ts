export type Aggregator =
  | 'SUM'
  | 'AVG'
  | 'COUNT'
  | 'DISTINCT'
  | 'COUNT_DISTINCT'
  | 'BY_YEAR'
  | 'BY_MONTH'
  | 'BY_DAY';
type ValueType = 'STRING' | 'NUMBER' | 'DATE' | 'BOOLEAN';

export type Groups = {
  titleName: string;
  valueType: ValueType;
  alias?: string;
  index?: number;
}[];

export type Orders = {
  titleName: string;
  operator: 'ASC' | 'DESC' | 'DEFAULT';
  valueType: ValueType;
}[];

export type Aggregators = {
  titleName: string;
  operator: Aggregator;
  valueType: ValueType;
  alias?: string;
}[];

export type QueryParams = {
  brandId: string;
  brandName: string;
  dataTypeId: number;
  groups: Groups;
  orders?: Orders;
  aggregators: Aggregators;
  params?: {
    alias?: string;
    titleName: string;
    value: string[];
    valueType: ValueType;
    dataDimensionsType: string;
  }[];
};

export type DownloadParams = QueryParams & {
  rows: Groups;
  columns: Groups;
};

export type FormatPercent = {
  type?: 'percent';
  suffix?: string;
  prefix?: string;
  decimal?: number;
  thousandSeparator?: boolean;
};

export type FormatNumerial = {
  type?: 'numerical';
  suffix?: string;
  prefix?: string;
  unit?: '千' | '万' | '百万' | '千万' | '亿' | 'K' | 'M';
  decimal?: number;
  thousandSeparator?: boolean;
};

export type ZoneType = 'row' | 'column' | 'metric';

// 核心中的核心
export interface Field {
  id: string;
  index: number;
  titleName: string;
  // 维度类型
  dataDimensionsType: string;
  // 是否排除模式
  excludeMode?: boolean;
  type: 'dimension' | 'metric';
  alias?: string;
  dataType: ValueType;
  aggregator?: Aggregator;
  sort?: 'ASC' | 'DESC';
  // 被拖拽到的区域
  zoneType?: ZoneType;
  format?: FormatPercent | FormatNumerial;
  tableName?: string;
}

export interface DataView {
  dataTypeId: number;
  dataTypeName: string;
  tableName: string;
  fields: Field[];
}

export interface DropZone {
  fields: Field[];
  type: ZoneType;
}

export interface PivotTableData {
  [key: string]: string | number;
}
