import { useEffect, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import type { SpreadSheet } from '@antv/s2';
import { asyncGetAllPlainData, download } from '@antv/s2';
import {
  request,
  useLocation,
  useModel,
  useRequest,
  useSearchParams,
} from '@umijs/max';
import { Button, Layout, Space, message } from 'antd';
import dayjs from 'dayjs';
import update from 'immutability-helper';
import { throttle } from 'lodash';

import DataViewSelector from './components/DataViewSelector';
import DownloadButton from './components/DownloadButton';
import DropZone from './components/DropZone';
import FieldList from './components/FieldList';
import PivotTable from './components/PivotTable';
import QueryForm from './components/QueryForm';
import SaveButton from './components/SaveButton';
import {
  DEFAULT_DATE_FIELD,
  DEFAULT_DATE_PARAMS,
  DEFAULT_FORMAT_DIMENSION,
  DEFAULT_FORMAT_METRIC,
} from './const';
import { formatQueryParams } from './helpers';
import type { Field, PivotTableData, QueryParams, ZoneType } from './types';

const { Header, Sider, Content } = Layout;

export default function ADHoc() {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const editId = searchParams.get('id');

  const s2Ref = useRef<SpreadSheet>(null);
  const [columnFields, setColumnFields] = useState<Field[]>([]);
  const [rowFields, setRowFields] = useState<Field[]>([]);
  const [metricFields, setMetricFields] = useState<Field[]>([]);
  // 数据视图字段
  const [dataTypeId, setDataTypeId] = useState<number>();
  const [dataViewFields, setDataViewFields] = useState<Field[]>([]);

  const [selfAnalysisName, setSelfAnalysisName] = useState<string>();
  // 查询字段
  const [queryFields, setQueryFields] = useState<Field[]>([DEFAULT_DATE_FIELD]);
  // 查询参数
  const [queryParams, setQueryParams] = useState<QueryParams['params']>([
    DEFAULT_DATE_PARAMS,
  ]);
  const { userInfo, currentBrand } = useModel('@@initialState', (model) => ({
    userInfo: model.initialState?.userInfo,
    currentBrand: model.initialState?.currentBrandInfo,
  }));

  // 处理回显数据
  useEffect(() => {
    if (location.state) {
      const state = location.state as {
        dataTypeId: number;
        columnFields: Field[];
        rowFields: Field[];
        metricFields: Field[];
        queryFields: Field[];
        queryParams: QueryParams['params'];
        selfAnalysisName: string;
      };

      if (state.dataTypeId) setDataTypeId(state.dataTypeId);
      if (state.columnFields) setColumnFields(state.columnFields);
      if (state.rowFields) setRowFields(state.rowFields);
      if (state.metricFields) setMetricFields(state.metricFields);
      if (state.queryFields) setQueryFields(state.queryFields);
      if (state.queryParams) setQueryParams(state.queryParams);
      if (state.selfAnalysisName) setSelfAnalysisName(state.selfAnalysisName);
    }
  }, [location.state]);

  const {
    run: queryTableData,
    loading,
    data: pivotTableData,
  } = useRequest<{ data: { rows?: PivotTableData[] } }>(
    (params: QueryParams) =>
      request('/omnipilot/api/self-analysis/execute', {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
    },
  );

  const handleDrop = (type: ZoneType, field: Field) => {
    const setFields = {
      row: setRowFields,
      column: setColumnFields,
      metric: setMetricFields,
    }[type];

    setFields((prev) => {
      if (prev.find((f) => f.id === field.id)) {
        return prev;
      }
      return [
        ...prev,
        {
          ...field,
          aggregator: field.type === 'metric' ? 'SUM' : 'COUNT',
          zoneType: type,
          format:
            field.type === 'metric'
              ? { ...DEFAULT_FORMAT_METRIC, ...field.format }
              : { ...DEFAULT_FORMAT_DIMENSION, ...field.format },
        },
      ].map((field, index) => ({ ...field, index })) as Field[];
    });
  };

  const handleFieldUpdate = (
    type: ZoneType,
    fieldId: string,
    updates: Partial<Field>,
  ) => {
    const setFields = {
      row: setRowFields,
      column: setColumnFields,
      metric: setMetricFields,
    }[type];

    setFields((prev) =>
      prev.map((field) =>
        field.id === fieldId ? { ...field, ...updates } : field,
      ),
    );
  };

  const handleQueryFieldDrop = (field: Field) => {
    setQueryFields((prev) => {
      if (prev.some((f) => f.id === field.id)) {
        return prev;
      }
      return [...prev, field];
    });
  };

  const handleRemove = (type: ZoneType, fieldId: string) => {
    const setFields = {
      row: setRowFields,
      column: setColumnFields,
      metric: setMetricFields,
    }[type];

    setFields((prev) => prev.filter((f) => f.id !== fieldId));
  };

  const handleRemoveQueryField = (fieldId: string) => {
    setQueryFields((prev) => prev.filter((field) => field.id !== fieldId));
  };

  const handleUpdateQueryField = (field: Field) => {
    setQueryFields((prev) => prev.map((f) => (f.id === field.id ? field : f)));
  };

  const handleQuery = (values: any) => {
    // 如果维度或者指标字段为空，则弹窗提示
    if (columnFields.length === 0 || metricFields.length === 0) {
      message.warning('请至少拖拽1个维度字段和1个指标字段');
    }
    setQueryParams(values);
  };

  // 使用useEffect监听字段变化触发查询
  useEffect(() => {
    const throttledQuery = throttle(
      () => {
        console.log(
          '%c [ 监听字段变化触发查询 ]-134',
          'font-size:13px; background:pink; color:#bf2c9f;',
          columnFields,
          rowFields,
          metricFields,
          queryParams,
          dataTypeId,
        );
        if (
          columnFields.length === 0 ||
          metricFields.length === 0 ||
          !dataTypeId
        ) {
          return;
        }

        const groups = formatQueryParams(columnFields, rowFields, metricFields);
        queryTableData({
          ...groups,
          params: queryParams,
          userId: userInfo?.id,
          userName: userInfo?.username,
          brandId: currentBrand?.id,
          brandName: currentBrand?.brandNameCn,
          dataTypeId,
        });
      },
      1000,
      { trailing: true, leading: false },
    );
    throttledQuery();
    return () => {
      throttledQuery.cancel();
    };
  }, [
    columnFields,
    rowFields,
    metricFields,
    queryParams,
    dataTypeId,
    currentBrand?.id,
    currentBrand?.brandNameCn,
    queryTableData,
    userInfo?.id,
    userInfo?.username,
  ]);

  const handleFieldsReorder = (
    type: ZoneType,
    dragIndex: number,
    hoverIndex: number,
  ) => {
    const setFields = {
      row: setRowFields,
      column: setColumnFields,
      metric: setMetricFields,
    }[type];

    setFields((prev) =>
      update(prev, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prev[dragIndex]],
        ],
      }).filter(Boolean),
    );
  };

  const resetFields = () => {
    setColumnFields([]);
    setRowFields([]);
    setMetricFields([]);
    setQueryFields([DEFAULT_DATE_FIELD]);
    setQueryParams([DEFAULT_DATE_PARAMS]);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Layout style={{ height: '100vh' }}>
        <Header className="mb-2 flex items-center justify-between rounded-lg bg-white px-4">
          <DataViewSelector
            value={dataTypeId}
            onSelect={resetFields}
            onChange={(dataView) => {
              setDataViewFields(
                dataView?.fields?.map((item) => ({
                  ...item,
                  tableName: dataView.tableName || '',
                })) || [],
              );
              setDataTypeId(dataView?.dataTypeId);
            }}
          />
          <Space>
            <div className="mr-2">
              <h2 className="mb-1 p-0 text-sm leading-tight">
                数据最近更新时间：
                {dayjs().subtract(1, 'day').format('YYYY-MM-DD')}
              </h2>
              <p className="m-0 p-0 leading-tight text-gray-400">
                默认查询前7天数据
              </p>
            </div>
            {/* <Button
              onClick={async () => {
                const data = await asyncGetAllPlainData({
                  sheetInstance: s2Ref.current,
                  split: ',',
                  formatOptions: true,
                  // formatOptions: {
                  //   formatHeader: true,
                  //   formatData: true
                  // },

                  // 同步导出
                  // async: false
                });

                // 导出数据 (csv)
                download(data, 'filename'); // filename.csv
              }}
            >
              导出CSV
            </Button> */}
            <SaveButton
              columnFields={columnFields}
              rowFields={rowFields}
              metricFields={metricFields}
              queryFields={queryFields}
              queryParams={queryParams}
              dataTypeId={dataTypeId}
              editId={editId}
              disabled={!pivotTableData?.rows || loading}
              selfAnalysisName={selfAnalysisName}
            />
            <DownloadButton
              columnFields={columnFields}
              rowFields={rowFields}
              metricFields={metricFields}
              queryParams={queryParams}
              dataTypeId={dataTypeId}
              s2Ref={s2Ref}
              disabled={!pivotTableData?.rows || loading}
              data={pivotTableData?.rows || []}
            />
          </Space>
        </Header>
        <Layout>
          <Sider width={200} theme="light" style={{ borderRadius: '8px' }}>
            <FieldList fields={dataViewFields} />
          </Sider>
          <Content
            style={{
              marginLeft: '8px',
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
            }}
          >
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
                gap: '8px',
                position: 'sticky',
                top: 80,
                zIndex: 1,
              }}
            >
              <DropZone
                emptyText="请至少拖拽1个维度字段到这里"
                type="column"
                fields={columnFields}
                onDrop={(field) => handleDrop('column', field)}
                onRemove={(fieldId) => handleRemove('column', fieldId)}
                onFieldUpdate={(fieldId, updates) =>
                  handleFieldUpdate('column', fieldId, updates)
                }
                acceptFieldTypes={['dimension']}
                onFieldsReorder={(dragIndex, hoverIndex) => {
                  handleFieldsReorder('column', dragIndex, hoverIndex);
                }}
              />
              <DropZone
                type="row"
                fields={rowFields}
                onDrop={(field) => handleDrop('row', field)}
                onRemove={(fieldId) => handleRemove('row', fieldId)}
                onFieldUpdate={(fieldId, updates) =>
                  handleFieldUpdate('row', fieldId, updates)
                }
                acceptFieldTypes={['dimension']}
                onFieldsReorder={(dragIndex, hoverIndex) => {
                  handleFieldsReorder('row', dragIndex, hoverIndex);
                }}
              />
              <DropZone
                emptyText="请至少拖拽1个指标字段到这里"
                type="metric"
                fields={metricFields}
                onDrop={(field) => handleDrop('metric', field)}
                onRemove={(fieldId) => handleRemove('metric', fieldId)}
                onFieldUpdate={(fieldId, updates) =>
                  handleFieldUpdate('metric', fieldId, updates)
                }
                acceptFieldTypes={['metric', 'dimension']}
                onFieldsReorder={(dragIndex, hoverIndex) => {
                  handleFieldsReorder('metric', dragIndex, hoverIndex);
                }}
              />
            </div>

            <QueryForm
              fields={queryFields}
              onDrop={(field) => handleQueryFieldDrop(field)}
              onRemove={(fieldId) => handleRemoveQueryField(fieldId)}
              onUpdate={(field) => handleUpdateQueryField(field)}
              onQuery={handleQuery}
              loading={loading}
              value={queryParams}
              disabled={
                columnFields.length === 0 ||
                metricFields.length === 0 ||
                !dataTypeId
              }
            />

            <PivotTable
              ref={s2Ref}
              data={pivotTableData?.rows || []}
              rowFields={rowFields}
              columnFields={columnFields}
              metricFields={metricFields}
            />
          </Content>
        </Layout>
      </Layout>
    </DndProvider>
  );
}
