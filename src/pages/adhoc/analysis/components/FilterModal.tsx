import React from 'react';

import { Form, Input, Modal, Select } from 'antd';

import type { Field } from '../types';

interface FilterModalProps {
  field: Field;
  open: boolean;
  onOk: (values: { operator: string; value: string }) => void;
  onCancel: () => void;
}

export default function FilterModal({
  field,
  open,
  onOk,
  onCancel,
}: FilterModalProps) {
  const [form] = Form.useForm();

  const handleOk = () => {
    form.validateFields().then((values) => {
      onOk(values);
      form.resetFields();
    });
  };

  return (
    <Modal
      title={`设置筛选条件: ${field.titleName}`}
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      destroyOnClose
    >
      <Form form={form} layout="vertical">
        <Form.Item name="operator" label="运算符" rules={[{ required: true }]}>
          <Select>
            <Select.Option value="equals">等于</Select.Option>
            <Select.Option value="contains">包含</Select.Option>
            <Select.Option value="startsWith">开头是</Select.Option>
            <Select.Option value="endsWith">结尾是</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="value" label="值" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
}
