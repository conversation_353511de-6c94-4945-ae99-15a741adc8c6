import { useEffect, useState } from 'react';

import { request, useModel, useRequest } from '@umijs/max';
import { Button, Form, Input, Modal, message } from 'antd';
import type { ButtonProps } from 'antd';

import { formatQueryParams } from '../helpers';
import type { Field, QueryParams } from '../types';

type SaveButtonProps = {
  columnFields: Field[];
  rowFields: Field[];
  metricFields: Field[];
  queryFields: Field[];
  queryParams: QueryParams['params'];
  dataTypeId?: number;
  editId?: string | null;
  selfAnalysisName?: string;
} & ButtonProps;

export default function SaveButton({
  columnFields,
  rowFields,
  metricFields,
  queryFields,
  queryParams,
  dataTypeId,
  editId,
  selfAnalysisName,
  ...buttonProps
}: SaveButtonProps) {
  const { run, loading } = useRequest(
    (params) =>
      request(
        editId
          ? '/omnipilot/api/self-analysis/update'
          : '/omnipilot/api/self-analysis/create',
        {
          method: 'POST',
          data: params,
        },
      ),
    {
      manual: true,
    },
  );
  const { userInfo, currentBrand } = useModel('@@initialState', (model) => ({
    userInfo: model.initialState?.userInfo,
    currentBrand: model.initialState?.currentBrandInfo,
  }));
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  const handleClick = () => {
    setModalVisible(true);
  };

  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  const handleOk = async () => {
    const values = await form.validateFields();
    const groups = formatQueryParams(columnFields, rowFields, metricFields);
    await run({
      id: editId,
      selfAnalysisName: values.selfAnalysisName,
      brandId: currentBrand?.id,
      brandName: currentBrand?.brandNameCn,
      userId: userInfo?.id,
      userName: userInfo?.username,
      config: JSON.stringify({
        dataTypeId: dataTypeId,
        columnFields,
        rowFields,
        metricFields,
        queryFields,
        queryParams,
      }),
      requestParam: {
        ...groups,
        params: queryParams,
        userId: userInfo?.id,
        userName: userInfo?.username,
        brandId: currentBrand?.id,
        brandName: currentBrand?.brandNameCn,
        dataTypeId: dataTypeId,
      },
    });
    message.success(editId ? '更新成功' : '保存成功');
    setModalVisible(false);
    form.resetFields();
  };

  useEffect(() => {
    if (selfAnalysisName) {
      form.setFieldValue('selfAnalysisName', selfAnalysisName);
    }
  }, [selfAnalysisName, form]);

  return (
    <>
      <Button type="primary" onClick={handleClick} {...buttonProps}>
        {editId ? '更新' : '保存'}
      </Button>
      <Modal
        title={editId ? '更新视图' : '保存视图'}
        open={modalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okButtonProps={{ loading }}
        destroyOnClose={false}
      >
        <Form form={form}>
          <Form.Item
            name="selfAnalysisName"
            label="视图名称"
            rules={[{ required: true, message: '请输入视图名称' }]}
          >
            <Input placeholder="请输入视图名称" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
