import type { ComponentProps } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDrop } from 'react-dnd';

import { DeleteOutlined } from '@ant-design/icons';
import { request, useModel, useRequest } from '@umijs/max';
import { useUpdate } from 'ahooks';
import {
  Button,
  Card,
  Checkbox,
  DatePicker,
  Divider,
  Form,
  Select,
  Space,
  Switch,
} from 'antd';
import dayjs from 'dayjs';

import { DEFAULT_DATE_PARAMS, DRAG_ELEMENT_TYPE } from '../const';
import type { Field, QueryParams } from '../types';

const { RangePicker } = DatePicker;

interface QueryFormValues {
  [key: string]: any;
}

interface QueryFormProps {
  value?: QueryParams['params'];
  onQuery?: (values: QueryParams['params']) => void;
  loading?: boolean;
  fields: Field[];
  onDrop: (field: Field) => void;
  onRemove: (fieldId: string) => void;
  onUpdate?: (field: Field) => void;
  disabled?: boolean;
}

export default function QueryForm({
  value,
  onQuery,
  loading,
  fields,
  onDrop,
  onRemove,
  onUpdate,
  disabled,
}: QueryFormProps) {
  const [form] = Form.useForm();

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: [DRAG_ELEMENT_TYPE.FIELD],
    drop: (item: Field) => onDrop(item),
    canDrop: (item: Field) =>
      item.type === 'dimension' && item.dataType !== 'DATE',
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const update = useUpdate();

  const formatFormValues = (values: QueryFormValues) => {
    return Object.entries(values)
      .filter(([_, value]) => {
        return value !== undefined && value !== null;
      })
      .map(([key, value]) => {
        const field = fields.find((f) => f.titleName === key);
        if (!field) return null;

        let formattedValue: string[] = [];

        switch (field.dataType) {
          case 'STRING':
            formattedValue = Array.isArray(value) ? value : [value];
            break;
          case 'DATE':
            if (Array.isArray(value) && value.length === 2) {
              // const dates: Record<string, string[]> = {
              //   DAY: [
              //     dayjs(value[0]).format('YYYY-MM-DD'),
              //     dayjs(value[1]).format('YYYY-MM-DD'),
              //   ],
              //   MONTH: [
              //     dayjs(value[0]).startOf('month').format('YYYY-MM-DD'),
              //     dayjs(value[1]).endOf('month').format('YYYY-MM-DD'),
              //   ],
              //   YEAR: [
              //     dayjs(value[0]).startOf('year').format('YYYY-MM-DD'),
              //     dayjs(value[1]).endOf('year').format('YYYY-MM-DD'),
              //   ],
              // };
              formattedValue = [
                dayjs(value[0]).format('YYYY-MM-DD'),
                dayjs(value[1]).format('YYYY-MM-DD'),
              ];
            }
            break;
          case 'BOOLEAN':
            formattedValue = [value];
            break;
          default:
            formattedValue = [String(value)];
        }

        return {
          titleName: field.dataType === 'DATE' ? 'date' : field.titleName,
          value: formattedValue,
          valueType: field.dataType,
          dataDimensionsType: field.dataDimensionsType,
          alias: field.alias,
        };
      })
      .filter((item) => item !== null);
  };

  const handleReset = () => {
    form.resetFields();
    onQuery?.([DEFAULT_DATE_PARAMS]);
  };

  const handleFinish = (values: QueryFormValues) => {
    const formattedValues = formatFormValues(values);
    onQuery?.(formattedValues);
  };

  const renderFormItem = (field: Field) => {
    const formControl = (() => {
      switch (field.dataType) {
        case 'STRING':
          return (
            <QueryFormSelectItem
              field={field}
              onExcludeModeChange={(checked) => {
                onUpdate?.({
                  ...field,
                  excludeMode: checked,
                });
              }}
            />
          );
        case 'DATE':
          return (
            <QueryFormDatePickerItem
              field={field}
              onUpdate={() => {
                update();
              }}
            />
          );
        case 'BOOLEAN':
          return <Switch />;
        default:
          return null;
      }
    })();

    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          gap: 8,
        }}
      >
        <Form.Item
          key={field.id}
          name={field.titleName}
          label={field.alias || field.titleName}
          style={{ marginBottom: 0, width: '100%' }}
        >
          {formControl}
        </Form.Item>
        {field.id !== 'default_date' && (
          <DeleteOutlined
            style={{
              flexShrink: 0,
              color: '#999',
              cursor: 'pointer',
              fontSize: 14,
            }}
            onClick={() => {
              onRemove(field.id);
              form.setFieldValue(field.titleName, undefined);
              const currentValues = form.getFieldsValue();
              const formattedValues = formatFormValues(currentValues);
              onQuery?.(formattedValues);
            }}
          />
        )}
      </div>
    );
  };

  // 复制 & 编辑模式回
  useEffect(() => {
    if (value) {
      // value转成form的value
      const formValues = value.reduce((acc: Record<string, any>, item) => {
        if (item.valueType === 'DATE') {
          acc[item.titleName] = [dayjs(item.value[0]), dayjs(item.value[1])];
        } else {
          acc[item.titleName] = item.value;
        }
        return acc;
      }, {});
      form.setFieldsValue(formValues);
    }
  }, [value]);

  return (
    <div ref={drop}>
      <Card
        size="small"
        title="筛选"
        style={{
          backgroundColor: isOver && canDrop ? '#f0f5ff' : '#fff',
          borderColor: isOver && canDrop ? '#1890ff' : '#d9d9d9',
        }}
      >
        <Form
          form={form}
          onFinish={handleFinish}
          onValuesChange={(_, allValues) => {
            const formattedValues = formatFormValues(allValues);
            onQuery?.(formattedValues);
          }}
        >
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '24px 32px',
              marginBottom: 16,
            }}
          >
            {fields.map((field) => renderFormItem(field))}
          </div>
          <Form.Item
            style={{
              display: 'flex',
              justifyContent: 'flex-end',
              marginBottom: 0,
              marginTop: 16,
            }}
          >
            <Space>
              <Button onClick={handleReset}>重置</Button>
              <Button
                disabled={disabled}
                loading={loading}
                type="primary"
                htmlType="submit"
              >
                查询
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

const QueryFormSelectItem = ({
  value,
  field,
  onChange,
  onExcludeModeChange,
}: {
  value?: string[],
  field: Field;
  onChange?: (values: string[]) => void;
  onExcludeModeChange?: (checked: boolean) => void;
}) => {
  const { currentBrand } = useModel('@@initialState', (model) => ({
    currentBrand: model.initialState?.currentBrandInfo,
  }));
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const form = Form.useFormInstance();
  const [selectAll, setSelectAll] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<any[]>([]);

  const { run, data, loading } = useRequest(
    () =>
      request('/omnipilot/api/base/info/get', {
        method: 'POST',
        data: {
          brandId: currentBrand?.id,
          brandName: currentBrand?.brandNameCn,
          dataDimensionsType: field.dataDimensionsType,
          titleName: field.titleName,
          tableName: field.tableName,
          startDate:
            form.getFieldValue('date')?.[0]?.format('YYYY-MM-DD') || '',
          endDate: form.getFieldValue('date')?.[1]?.format('YYYY-MM-DD') || '',
          titleId: field.id,
        },
      }),
    {
      formatResult(res) {
        return res?.data?.values?.map((item: any) => ({
          label: item.value,
          value: item.value,
        }));
      },
      manual: true,
    },
  );

  const allValues = useMemo(
    () => (data || []).map((item: any) => item.value),
    [data],
  );

  // 根据搜索值过滤选项
  useEffect(() => {
    if (data) {
      const filtered = data.filter((item: any) =>
        item.label.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setFilteredOptions(filtered);
    }
  }, [data, searchValue, selectAll]);

  // 获取当前过滤后的选项值
  const filteredValues = useMemo(
    () => filteredOptions.map((item: any) => item.value),
    [filteredOptions],
  );

  const date = form.getFieldValue('date');
  useEffect(() => {
    if (date) {
      run();
    }
  }, [date]);

  useEffect(() => {
    if (selectedValues.length !== allValues.length) {
      setSelectAll(false);
    }
    if (selectedValues.length === allValues.length) {
      setSelectAll(true);
    }
  }, [selectedValues, allValues]);

  const isInitialled = useRef(false);
  useEffect(() => {
    if (value && data && !isInitialled.current) {
      isInitialled.current = true;
      if (field.excludeMode) {
        const newValues = allValues.filter(
          (v: string) =>!value?.includes(v),
        );
        setSelectedValues(newValues);
      } else {
        setSelectedValues(value);
      }
    }
  }, [value, data, field.excludeMode]);

  return (
    <Select
      mode="multiple"
      showSearch
      placeholder={`请选择${field.alias || field.titleName}`}
      allowClear
      autoClearSearchValue={false}
      style={{ width: '100%' }}
      loading={loading}
      options={data}
      value={selectedValues}
      maxTagCount="responsive"
      searchValue={searchValue}
      onSearch={(value) => setSearchValue(value)}
      onInputKeyDown={(e) => {
        if (e.code === 'Enter' || e.key === 'Enter') {
          setSearchValue('')
        }
      }}
      onChange={(values) => {
        setSelectedValues(values);
        if (field.excludeMode) {
          const newValues = allValues.filter(
            (v: string) => !values?.includes(v),
          );
          onChange?.(newValues);
        } else {
          onChange?.(values);
        }
      }}
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider style={{ margin: '8px 0' }} />
          <Space style={{ padding: '0 8px 4px' }}>
            <Checkbox
              checked={field.excludeMode}
              onChange={(e) => {
                if (e.target.checked) {
                  const newValues = allValues.filter(
                    (v: string) => !selectedValues?.includes(v),
                  );
                  onChange?.(newValues);
                } else {
                  onChange?.(selectedValues);
                }
                onExcludeModeChange?.(e.target.checked);
              }}
            >
              排除模式
            </Checkbox>
            <Checkbox
              // checked={field.excludeMode}
              checked={selectAll}
              onChange={(e) => {
                setSearchValue('');
                setSelectAll(e.target.checked);
                if (e.target.checked) {
                  // 只全选搜索结果中的选项
                  const valuesToSelect = searchValue
                    ? [...new Set([...selectedValues, ...filteredValues])]
                    : allValues;
                  setSelectedValues(valuesToSelect);
                  onChange?.(valuesToSelect);
                } else {
                  setSelectedValues([]);
                  onChange?.([]);
                }
              }}
            >
              全选
            </Checkbox>
          </Space>
        </>
      )}
    />
  );
};

const QueryFormDatePickerItem = ({
  field,
  onUpdate,
  ...rest
}: { field: Field; onUpdate?: () => void } & ComponentProps<
  typeof RangePicker
>) => {
  const [picker, setPicker] = useState<'date' | 'month' | 'year'>('date');

  return (
    <RangePicker
      {...rest}
      style={{ width: '100%' }}
      format="YYYY-MM-DD"
      picker={picker}
      onChange={(dates, dateStrings) => {
        if (picker === 'month' || picker === 'year') {
          const [start, end] = dates || [];
          if (!start || !end) {
            return;
          }
          rest.onChange?.(
            [start?.startOf(picker), end?.endOf(picker)],
            dateStrings,
          );
        } else {
          rest.onChange?.(dates, dateStrings);
        }
        onUpdate?.();
      }}
      disabledDate={(current) => {
        if (picker === 'date') {
          return current > dayjs().add(-1, 'day');
        }
        if (picker === 'month') {
          return current > dayjs().add(0, 'month');
        }
        if (picker === 'year') {
          return current > dayjs().add(0, 'year');
        }
        return true;
      }}
      renderExtraFooter={() => {
        // 日 周 月 年
        return (
          <Space>
            {[
              { value: 'year', label: '年' },
              { value: 'month', label: '月' },
              { value: 'date', label: '日' },
            ].map((item) => (
              <Button
                key={item.value}
                type={picker === item.value ? 'primary' : 'default'}
                onClick={() =>
                  setPicker(item.value as 'date' | 'month' | 'year')
                }
              >
                {item.label}
              </Button>
            ))}
          </Space>
        );
      }}
    />
  );
};
