import { useDrag } from 'react-dnd';

import { Tag } from 'antd';

import { DRAG_ELEMENT_TYPE } from '../const';
import type { Field } from '../types';

interface DraggableFieldProps {
  field: Field;
}

export default function DraggableField({ field }: DraggableFieldProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: DRAG_ELEMENT_TYPE.FIELD,
    item: field,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      style={{ opacity: isDragging ? 0.5 : 1, cursor: 'move', marginBottom: 8 }}
    >
      <Tag
        color={field.type === 'dimension' ? 'blue' : 'green'}
        style={{
          width: '100%',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {field.alias || field.titleName}
      </Tag>
    </div>
  );
}
