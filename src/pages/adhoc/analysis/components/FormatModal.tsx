import { useEffect } from 'react';

import { Form, Input, InputNumber, Modal, Radio, Select, Switch } from 'antd';

import type { Field, FormatNumerial, FormatPercent } from '../types';

interface FormatModalProps {
  visible: boolean;
  field: Field | null;
  onOk: (values: any) => void;
  onCancel: () => void;
}

const UNIT_OPTIONS = [
  { label: '千', value: '千' },
  { label: '万', value: '万' },
  { label: '百万', value: '百万' },
  { label: '千万', value: '千万' },
  { label: '亿', value: '亿' },
  { label: 'K', value: 'K' },
  { label: 'M', value: 'M' },
];

export default function FormatModal({
  visible,
  field,
  onOk,
  onCancel,
}: FormatModalProps) {
  const [form] = Form.useForm();

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onOk(values);
      form.resetFields();
    });
  };

  const format = field?.format as FormatNumerial | FormatPercent | undefined;

  useEffect(() => {
    if (visible && field) {
      form.setFieldsValue({
        type: format?.type || 'numerical',
        decimal: format?.decimal,
        thousandSeparator: format?.thousandSeparator,
        prefix:
          format?.type === 'numerical' ? (format as FormatNumerial).prefix : '',
        suffix:
          format?.type === 'numerical' ? (format as FormatNumerial).suffix : '',
        unit:
          format?.type === 'numerical'
            ? (format as FormatNumerial).unit
            : undefined,
      });
    }
  }, [visible, field, format]);

  return (
    <Modal
      title="数据格式化配置"
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
    >
      <Form form={form} layout="vertical">
        <Form.Item name="type" label="格式类型">
          <Radio.Group optionType="button" buttonStyle="solid">
            <Radio.Button value="numerical">数值</Radio.Button>
            <Radio.Button value="percent">百分比</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="decimal" label="小数位数">
          <InputNumber min={0} max={10} />
        </Form.Item>
        <Form.Item
          name="thousandSeparator"
          label="千分位"
          valuePropName="checked"
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.type !== currentValues.type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('type') === 'numerical' ? (
              <>
                <Form.Item name="unit" label="单位">
                  <Select
                    allowClear
                    placeholder="请选择单位"
                    options={UNIT_OPTIONS}
                  />
                </Form.Item>
                <Form.Item name="prefix" label="前缀">
                  <Input placeholder="如：¥" />
                </Form.Item>
                <Form.Item name="suffix" label="后缀">
                  <Input placeholder="如：元" />
                </Form.Item>
              </>
            ) : null
          }
        </Form.Item>
      </Form>
    </Modal>
  );
}
