import { useEffect } from 'react';

import { request, useModel, useRequest } from '@umijs/max';
import { Select } from 'antd';

import type { DataView } from '../types';

interface DataViewSelectorProps {
  value?: number;
  onChange?: (dataView?: DataView) => void;
  onSelect?: (dataView?: DataView) => void;
}

const DataViewSelector: React.FC<DataViewSelectorProps> = ({
  onChange,
  onSelect,
  value,
}) => {
  const { currentBrand, userInfo } = useModel('@@initialState', (model) => ({
    currentBrand: model.initialState?.currentBrandInfo,
    userInfo: model.initialState?.userInfo,
  }));
  const { run, data, loading } = useRequest<{ data: DataView[] }>(
    () =>
      request('/omnipilot/api/base/info/get/data-types', {
        method: 'POST',
        params: {
          brandId: currentBrand?.id,
          userId: userInfo?.id,
        },
      }),
    {
      onSuccess(res) {
        const defaultView = res?.find(
          (item) => item.dataTypeName === '全量数据表',
        );
        if (defaultView && !value) {
          onChange?.(defaultView);
        }
      },
    },
  );

  const handleChange = (value?: number) => {
    const dataView = data?.find((item) => item.dataTypeId === value);
    onChange?.(dataView);
  };

  useEffect(() => {
    console.log('dataView effect');
    if (value && data) {
      handleChange(value);
    }
  }, [data, value]);

  return (
    <Select
      style={{ width: 200 }}
      placeholder="请选择数据表"
      loading={loading}
      value={value}
      onSelect={() => {
        onSelect?.();
      }}
      onChange={handleChange}
      options={data?.map((view) => ({
        label: view.dataTypeName,
        value: view.dataTypeId,
      }))}
    />
  );
};

export default DataViewSelector;
