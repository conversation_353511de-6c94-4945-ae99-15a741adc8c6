import React from 'react';

import { Divider, Typography } from 'antd';

import type { Field } from '../types';
import DraggableField from './DraggableField';

const { Title } = Typography;

interface FieldListProps {
  fields: Field[];
}

export default function FieldList({ fields }: FieldListProps) {
  const dimensions = fields.filter((f) => f.type === 'dimension');
  const metrics = fields.filter((f) => f.type === 'metric');

  return (
    <div className="flex h-[100%] flex-col overflow-hidden p-[16px]">
      <div
        className="flex-1"
        style={{
          overflowY: 'auto',
          overflowX: 'hidden',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#d9d9d9',
            borderRadius: '3px',
          },
        }}
      >
        <Title level={5}>维度</Title>
        {dimensions.map((field) => (
          <DraggableField key={field.id} field={field} />
        ))}
      </div>

      <Divider />

      <div
        className="flex-1"
        style={{
          overflowY: 'auto',
          overflowX: 'hidden',
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#d9d9d9',
            borderRadius: '3px',
          },
        }}
      >
        <Title level={5}>指标</Title>
        {metrics.map((field) => (
          <DraggableField key={field.id} field={field} />
        ))}
      </div>
    </div>
  );
}
