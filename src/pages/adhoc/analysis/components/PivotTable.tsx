import React, {
  forwardRef,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import type { SpreadSheet } from '@antv/s2';
import { SheetComponent } from '@antv/s2-react';
import '@antv/s2-react/dist/s2-react.min.css';
import { Empty, Menu } from 'antd';

import { fieldsToMeta } from '../helpers';
import type { Field, PivotTableData } from '../types';

interface PivotTableProps {
  data: PivotTableData[];
  rowFields: Field[];
  columnFields: Field[];
  metricFields: Field[];
}

export default forwardRef<SpreadSheet, PivotTableProps>(function PivotTable(
  { data, rowFields, columnFields, metricFields },
  ref,
) {
  const wrapRef = useRef<HTMLDivElement>(null);
  const [rect, setRect] = useState<{ width: number; height: number } | null>(
    null,
  );

  const s2DataConfig = useMemo(() => {
    if (columnFields.length === 0 || metricFields.length === 0) {
      return null;
    }

    // 如果没有rows就把values合并到columns里
    if (rowFields.length === 0) {
      return {
        fields: {
          columns: columnFields
            .map((f) => f.titleName)
            .concat(metricFields.map((f) => f.titleName)),
        },
        meta: fieldsToMeta([...columnFields, ...metricFields]),
        data: data,
      };
    }

    return {
      fields: {
        columns: columnFields.map((f) => f.titleName),
        rows: rowFields.map((f) => f.titleName),
        values: metricFields.map((f) => f.titleName),
        valueInCols: true,
      },
      meta: fieldsToMeta([...columnFields, ...rowFields, ...metricFields]),
      data: data,
    };
  }, [data, rowFields, columnFields, metricFields]);

  const s2Options: Required<
    React.ComponentProps<typeof SheetComponent>
  >['options'] = useMemo(
    () => ({
      width: rect?.width,
      height: rect?.height,
      showDefaultHeaderActionIcon: true,
      tooltip: {
        enable: true,
        style: {
          backgroundColor: '#fff',
        },
        operation: {
          // 开启组内排序
          sort: true,
          menu: {
            render: (props) => {
              return <Menu {...props} />;
            },
          },
        },
      },
      interaction: {
        copy: {
          enable: true,
          withHeader: true,
          withFormat: true,
        },
      },
    }),
    [rect],
  );

  useLayoutEffect(() => {
    if (!s2DataConfig) {
      return;
    }

    if (wrapRef.current) {
      setRect({
        width: wrapRef.current.clientWidth - 16,
        height: wrapRef.current.clientHeight - 16,
      });
    }
  }, [s2DataConfig]);

  if (!s2DataConfig) {
    return <Empty description="请拖入维度和指标以生成透视表" />;
  }

  return (
    <div
      style={{
        overflow: 'auto',
        backgroundColor: '#fff',
        padding: '8px',
        borderRadius: '8px',
        flex: 1,
      }}
      ref={wrapRef}
    >
      {/* <button onClick={() => dw(s2DataConfig)}>导出</button> */}
      {rect && (
        <SheetComponent
          ref={ref}
          adaptive
          sheetType={rowFields.length === 0 ? 'table' : 'pivot'}
          dataCfg={s2DataConfig}
          options={s2Options}
        />
      )}
    </div>
  );
});
