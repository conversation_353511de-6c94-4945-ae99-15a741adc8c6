import { useEffect, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';

import { Dropdown, Tag } from 'antd';
import type { MenuProps } from 'antd';
import type { Identifier, XYCoord } from 'dnd-core';

import { DRAG_ELEMENT_TYPE } from '../const';
import type { Field, ZoneType } from '../types';

interface FieldTagProps {
  field: Field;
  menu: MenuProps;
  onRemove: (fieldId: string) => void;
  index: number;
  onMove?: (dragIndex: number, hoverIndex: number) => void;
  zoneType: ZoneType;
}

export default function FieldTag({
  field,
  menu,
  onRemove,
  index,
  zoneType,
  onMove,
}: FieldTagProps) {
  const ref = useRef<HTMLSpanElement>(null);
  const [{ isDragging }, drag] = useDrag(() => ({
    type: DRAG_ELEMENT_TYPE.FIELD,
    item: {
      ...field,
      index,
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const [{ handlerId }, drop] = useDrop<
    Field,
    void,
    { handlerId: Identifier | null }
  >({
    accept: DRAG_ELEMENT_TYPE.FIELD,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: Field, monitor) {
      if (!ref.current || item.zoneType !== zoneType) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleX =
        (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return;
      }

      // Time to actually perform the action
      onMove?.(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));
  return (
    <Dropdown menu={menu} trigger={['click']}>
      <Tag
        ref={ref}
        color={field.type === 'dimension' ? 'blue' : 'green'}
        closable
        onClose={(e) => {
          e.stopPropagation();
          onRemove(field.id);
        }}
        style={{
          cursor: 'pointer',
          opacity: isDragging ? 0.5 : 1,
        }}
        data-handler-id={handlerId}
      >
        {field.alias || field.titleName}
      </Tag>
    </Dropdown>
  );
}
