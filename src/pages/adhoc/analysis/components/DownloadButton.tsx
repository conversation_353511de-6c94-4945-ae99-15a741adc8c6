import { useRef, useState } from 'react';

import { download } from '@/utils';
import type { SpreadSheet } from '@antv/s2';
import { PivotSheet, asyncGetAllPlainData } from '@antv/s2';
import { SheetComponent } from '@antv/s2-react';
import { request, useModel, useRequest } from '@umijs/max';
import { Button, Modal, message } from 'antd';
import type { ButtonProps } from 'antd';
import * as XLSX from 'xlsx';

import {
  fieldsToMeta,
  formatDownloadParams,
  formatQueryParams,
} from '../helpers';
import type { Field, PivotTableData, QueryParams } from '../types';

type DownloadButtonProps = {
  columnFields: Field[];
  rowFields: Field[];
  metricFields: Field[];
  queryParams: QueryParams['params'];
  dataTypeId?: number;
  s2Ref?: React.RefObject<SpreadSheet>;
  data: PivotTableData[];
} & ButtonProps;

export default function DownloadButton({
  columnFields,
  rowFields,
  metricFields,
  queryParams,
  dataTypeId,
  s2Ref,
  data: tableData,
  ...buttonProps
}: DownloadButtonProps) {
  const { userInfo, currentBrand } = useModel('@@initialState', (model) => ({
    userInfo: model.initialState?.userInfo,
    currentBrand: model.initialState?.currentBrandInfo,
  }));
  const [modalVisible, setModalVisible] = useState(false);
  const [pivotDownloadLoading, setPivotDownloadLoading] = useState(false);
  const [originDownloadLoading, setOriginDownloadLoading] = useState(false);
  const ref = useRef<SpreadSheet>(null);

  const handleClick = () => {
    setModalVisible(true);
  };

  const handleCancel = () => {
    setModalVisible(false);
  };

  const handleDownload = async (type: 'pivot' | 'origin') => {
    if (!s2Ref?.current) return;
    const setLoading = {
      pivot: setPivotDownloadLoading,
      origin: setOriginDownloadLoading,
    }[type];
    setLoading(true);
    try {
      // // 拿到复制数据 (text/plain)
      // const tableHTML = await asyncGetAllHtmlData({
      //   sheetInstance: s2Ref.current,
      //   split: ',',
      //   formatOptions: true,
      // });
      // const tableDom = document.createElement('div');
      // tableDom.innerHTML = tableHTML;

      // const wb = XLSX.utils.table_to_book(tableDom.querySelector('table'), {
      //   sheet: '透视表',
      // });

      // XLSX.writeFile(wb, '透视表数据.xlsx');
      // setModalVisible(false);
      let data: string;
      if (type === 'origin') {
        data = await asyncGetAllPlainData({
          sheetInstance: ref.current!,
          split: '|||||',
          formatOptions: {
            formatHeader: true,
            formatData: true,
          },
          // 同步导出
          async: false,
        });
      } else {
        data = await asyncGetAllPlainData({
          sheetInstance: s2Ref.current,
          split: '|||||',
          formatOptions: {
            formatHeader: true,
            formatData: true,
          },
          // 同步导出
          async: false,
        });
      }

      const groups = formatDownloadParams(
        columnFields,
        rowFields,
        metricFields,
      );

      const selfAnalysisRequestParam = {
        ...formatQueryParams(columnFields, rowFields, metricFields),
        params: queryParams,
        userId: userInfo?.id,
        userName: userInfo?.username,
        brandId: currentBrand?.id,
        brandName: currentBrand?.brandNameCn,
        dataTypeId: dataTypeId!,
      };

      const paramsCsvContent =
        selfAnalysisRequestParam.params
          ?.map((item) => {
            return `${item.alias}：${item.value.join(item.valueType === 'DATE' ? ' ~ ' : '、')}`;
          })
          .join('\r\n') + '\r\n\r\n' || '';

      const url =
        type === 'pivot'
          ? '/omnipilot/api/self-analysis/download/self-analysis/v2'
          : '/omnipilot/api/self-analysis/download/self-analysis/v2'; // : '/omnipilot/api/self-analysis/download/base';
      await download(
        url,
        {
          ...groups,
          selfAnalysisRequestParam,
          // eslint-disable-next-line no-useless-escape
          csvContent: paramsCsvContent + data.replace(/\"/g, ''),
        },
        {
          filename: type === 'pivot' ? '透视表.xlsx' : '明细表.xlsx',
        },
      );
    } catch (error) {
      message.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // const handleDownloadOriginalTable = async () => {
  //   if (
  //     !s2Ref?.current ||
  //     !s2Ref.current.dataCfg.data ||
  //     s2Ref.current.dataCfg.data.length === 0
  //   )
  //     return;

  //   setLoading(true);
  //   try {
  //     const originalData = s2Ref.current.dataSet.getDisplayDataSet();
  //     const meta = s2Ref.current.dataCfg.meta!;

  //     // 转换数据为二维数组
  //     const headers = meta!.map((m) => m.name);
  //     const arrayData = originalData.map((item) =>
  //       meta!.map((m) => item[m.field as string]),
  //     );

  //     // 创建工作簿
  //     const ws = XLSX.utils.aoa_to_sheet([headers, ...arrayData]);
  //     const wb = XLSX.utils.book_new();
  //     XLSX.utils.book_append_sheet(wb, ws, '明细表');

  //     // 下载文件
  //     XLSX.writeFile(wb, '明细数据.xlsx');
  //     setModalVisible(false);
  //   } catch (error) {
  //     console.error('导出失败:', error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  return (
    <>
      <SheetComponent
        ref={ref}
        adaptive
        sheetType={'table'}
        dataCfg={{
          fields: {
            columns: [...columnFields, ...rowFields, ...metricFields].map(
              (item) => item.titleName,
            ),
          },
          meta: fieldsToMeta([...columnFields, ...rowFields, ...metricFields]),
          data: tableData,
        }}
        options={{
          width: 0,
          height: 0,
        }}
      />
      <Button type="primary" onClick={handleClick} {...buttonProps}>
        下载
      </Button>
      <Modal
        width={300}
        title="选择下载内容"
        open={modalVisible}
        onCancel={handleCancel}
        footer={null}
      >
        <div className="flex flex-col gap-4 py-4">
          <Button
            type="primary"
            loading={pivotDownloadLoading}
            disabled={originDownloadLoading}
            onClick={() => handleDownload('pivot')}
          >
            下载透视表
          </Button>
          <Button
            type="primary"
            loading={originDownloadLoading}
            disabled={pivotDownloadLoading}
            onClick={() => handleDownload('origin')}
          >
            下载明细表
          </Button>
        </div>
      </Modal>
    </>
  );
}
