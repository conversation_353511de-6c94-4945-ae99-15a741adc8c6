import { useState } from 'react';
import { useDrop } from 'react-dnd';

import {
  CheckOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
} from '@ant-design/icons';
import { Card } from 'antd';
import type { MenuProps } from 'antd';

import { DRAG_ELEMENT_TYPE } from '../const';
import useFieldOperations from '../hooks/useFieldOperations';
import type { Field, FormatNumerial, FormatPercent, ZoneType } from '../types';
import FieldTag from './FieldTag';
import FormatModal from './FormatModal';

interface DropZoneProps {
  type: ZoneType;
  fields: Field[];
  onDrop: (field: Field) => void;
  onRemove: (fieldId: string) => void;
  onFieldUpdate: (fieldId: string, updates: Partial<Field>) => void;
  acceptFieldTypes?: ('dimension' | 'metric')[];
  onFieldsReorder?: (dragIndex: number, hoverIndex: number) => void;
  // 没数据时显示的文案
  emptyText?: string;
}

export default function DropZone({
  type,
  fields,
  onDrop,
  onRemove,
  onFieldUpdate,
  acceptFieldTypes,
  onFieldsReorder,
  emptyText,
}: DropZoneProps) {
  const { handleSortClick, handleAggregatorClick } =
    useFieldOperations(onFieldUpdate);
  const [formatModalVisible, setFormatModalVisible] = useState(false);
  const [currentField, setCurrentField] = useState<Field | null>(null);

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: [DRAG_ELEMENT_TYPE.FIELD],
    drop: (item: Field) => onDrop(item),
    canDrop: (item: Field) =>
      !acceptFieldTypes || acceptFieldTypes.includes(item.type),
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const handleFormatSubmit = (values: FormatNumerial | FormatPercent) => {
    if (currentField) {
      onFieldUpdate(currentField.id, {
        format: values,
      });
      setFormatModalVisible(false);
    }
  };

  const getMenu = (field: Field): MenuProps => {
    const items: MenuProps['items'] = [];

    // 添加排序菜单
    items.push({
      key: 'sort',
      label: '排序',
      children: [
        {
          key: 'asc',
          label: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>升序</span>
              {field?.sort === 'ASC' && <CheckOutlined />}
            </div>
          ),
          icon: <SortAscendingOutlined />,
          onClick: () => handleSortClick(field, 'ASC'),
        },
        {
          key: 'desc',
          label: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>降序</span>
              {field?.sort === 'DESC' && <CheckOutlined />}
            </div>
          ),
          icon: <SortDescendingOutlined />,
          onClick: () => handleSortClick(field, 'DESC'),
        },
      ],
    });

    // 如果是指标类型，添加聚合菜单
    if (type === 'metric') {
      const aggregatorChildren = [];

      // 如果是指标字段，添加总计和平均数选项
      if (field.type === 'metric') {
        aggregatorChildren.push(
          {
            key: 'SUM',
            label: (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <span>总计</span>
                {field.aggregator === 'SUM' && <CheckOutlined />}
              </div>
            ),
            onClick: () => handleAggregatorClick(field, 'SUM'),
          },
          {
            key: 'AVG',
            label: (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <span>平均数</span>
                {field.aggregator === 'AVG' && <CheckOutlined />}
              </div>
            ),
            onClick: () => handleAggregatorClick(field, 'AVG'),
          },
        );
      }

      // 添加计数和去重计数选项
      aggregatorChildren.push(
        {
          key: 'COUNT',
          label: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>计数</span>
              {field.aggregator === 'COUNT' && <CheckOutlined />}
            </div>
          ),
          onClick: () => handleAggregatorClick(field, 'COUNT'),
        },
        {
          key: 'DISTINCT',
          label: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>去重</span>
              {field.aggregator === 'DISTINCT' && <CheckOutlined />}
            </div>
          ),
          onClick: () => handleAggregatorClick(field, 'DISTINCT'),
        },
        {
          key: 'COUNT_DISTINCT',
          label: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <span>去重计数</span>
              {field.aggregator === 'COUNT_DISTINCT' && <CheckOutlined />}
            </div>
          ),
          onClick: () => handleAggregatorClick(field, 'COUNT_DISTINCT'),
        },
      );

      items.push({
        key: 'aggregator',
        label: '聚合',
        children: aggregatorChildren,
      });

      // 添加格式菜单
      items.push({
        key: 'format',
        label: '格式',
        onClick: () => {
          setCurrentField(field);
          setFormatModalVisible(true);
        },
      });
    }

    return { items };
  };

  const handleMove = (dragIndex: number, hoverIndex: number) => {
    onFieldsReorder?.(dragIndex, hoverIndex);
  };

  return (
    <>
      <div ref={drop}>
        <Card
          title={type === 'row' ? '行' : type === 'column' ? '列' : '指标'}
          size="small"
          style={{
            backgroundColor: isOver && canDrop ? '#f0f5ff' : '#fff',
            borderColor: isOver && canDrop ? '#1890ff' : '#d9d9d9',
            borderStyle: !acceptFieldTypes && !canDrop ? 'dashed' : 'solid',
          }}
        >
          {!fields || fields.length === 0 ? (
            <div
              style={{
                textAlign: 'center',
                height: '20px',
                lineHeight: '20px',
              }}
            >
              {emptyText}
            </div>
          ) : (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {fields.map((field, index) => (
                <FieldTag
                  index={index}
                  key={field.id}
                  field={field}
                  menu={getMenu(field)}
                  onRemove={onRemove}
                  onMove={handleMove}
                  zoneType={type}
                />
              ))}
            </div>
          )}
        </Card>
      </div>
      <FormatModal
        visible={formatModalVisible}
        field={currentField}
        onOk={handleFormatSubmit}
        onCancel={() => setFormatModalVisible(false)}
      />
    </>
  );
}
