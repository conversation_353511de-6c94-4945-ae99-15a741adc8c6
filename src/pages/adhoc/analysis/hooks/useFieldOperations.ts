import type { Aggregator, Field } from '../types';

export default function useFieldOperations(
  onFieldUpdate: (fieldId: string, updates: Partial<Field>) => void,
) {
  const handleSortClick = (field: Field, direction: 'ASC' | 'DESC') => {
    onFieldUpdate(field.id, { sort: direction });
  };

  const handleAggregatorClick = (field: Field, aggregator: Aggregator) => {
    onFieldUpdate(field.id, { aggregator });
  };

  return {
    handleSortClick,
    handleAggregatorClick,
  };
}
