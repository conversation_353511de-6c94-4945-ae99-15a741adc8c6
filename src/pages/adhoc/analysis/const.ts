import dayjs from 'dayjs';

import type { FormatNumerial, FormatPercent } from './types';

export const DEFAULT_DECIMAL = 2;

export const DEFAULT_FORMAT_DIMENSION: FormatNumerial = {
  type: 'numerical',
  decimal: DEFAULT_DECIMAL,
  thousandSeparator: true,
};

export const DEFAULT_FORMAT_METRIC: FormatNumerial = {
  type: 'numerical',
  decimal: DEFAULT_DECIMAL,
  thousandSeparator: true,
};

export const DRAG_ELEMENT_TYPE = {
  FIELD: 'FIELD',
  FIELD_SORT: 'FIELD_SORT',
};

export const DEFAULT_DATE_FIELD = {
  index: 0,
  alias: '⽇期',
  dataDimensionsType: 'DAY',
  dataType: 'DATE',
  titleName: 'date',
  type: 'dimension',
  id: 'default_date',
} as const;

export const DEFAULT_DATE_PARAMS = {
  titleName: 'date',
  alias: '⽇期',
  // 过去7天（不包含今天）
  value: [
    dayjs().subtract(8, 'day').format('YYYY-MM-DD'),
    dayjs().subtract(2, 'day').format('YYYY-MM-DD'),
  ] as [string, string],
  valueType: 'DATE',
  dataDimensionsType: 'DAY',
} as const;
