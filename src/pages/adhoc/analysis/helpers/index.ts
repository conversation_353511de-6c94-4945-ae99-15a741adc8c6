import type { Formatter, S2DataConfig } from '@antv/s2';
import numeral from 'numeral';

import type {
  Aggregators,
  Field,
  FormatNumerial,
  FormatPercent,
  Groups,
  Orders,
  QueryParams,
} from '../types';
import { DEFAULT_DECIMAL } from '../const';

function formatValue(
  value: Parameters<Formatter>[0],
  format?: FormatNumerial | FormatPercent,
): string {
  if (!format || typeof value !== 'number') {
    return String(value);
  }

  const { type, decimal = DEFAULT_DECIMAL, thousandSeparator = true } = format;

  // 设置千分位分隔符
  const formatStr = thousandSeparator
    ? `0,0.${'0'.repeat(decimal)}`
    : `0.${'0'.repeat(decimal)}`;

  if (type === 'percent') {
    return numeral(value).format(formatStr + '%');
  }

  // 数值类型
  const { prefix = '', suffix = '', unit } = format as FormatNumerial;

  // 处理单位转换
  let convertedNum = value;

  if (unit) {
    const unitMap = {
      千: 1000,
      万: 10000,
      百万: 1000000,
      千万: 10000000,
      亿: 100000000,
      K: 1000,
      M: 1000000,
    };

    if (unit in unitMap) {
      convertedNum = value / unitMap[unit];
    }
  }

  return `${prefix}${numeral(convertedNum).format(formatStr)}${unit || ''}${suffix}`;
}

export function fieldsToMeta(fields: Field[]): S2DataConfig['meta'] {
  return fields.map((field) => ({
    field: field.titleName,
    name: field.alias || field.titleName,
    formatter:
      field.zoneType === 'metric' && field.format
        ? (value) => formatValue(value, field.format)
        : undefined,
  }));
}

export function formatDownloadParams(
  columnFields: Field[],
  rowFields: Field[],
  metricFields: Field[],
) {
  const columns: Groups = columnFields.map((field, index) => ({
    id: field.id,
    titleName: field.titleName,
    valueType: field.dataType,
    alias: field.alias || field.titleName,
    dataDimensionsType: field.dataDimensionsType,
    index,
  }));

  const rows: Groups = rowFields.map((field, index) => ({
    id: field.id,
    titleName: field.titleName,
    valueType: field.dataType,
    alias: field.alias || field.titleName,
    dataDimensionsType: field.dataDimensionsType,
    index,
  }));

  const orders: Orders = [...columnFields, ...rowFields, ...metricFields]
    .filter((field) => field.sort)
    .map((field) => ({
      id: field.id,
      titleName: field.titleName,
      operator: field.sort!,
      valueType: field.dataType,
      alias: field.alias || field.titleName,
      dataDimensionsType: field.dataDimensionsType,
    }));

  const aggregators: Aggregators = metricFields.map((field) => ({
    id: field.id,
    titleName: field.titleName,
    operator: field.aggregator || 'SUM',
    valueType: field.dataType,
    alias: field.alias || field.titleName,
    dataDimensionsType: field.dataDimensionsType,
  }));

  return {
    columns,
    rows,
    aggregators,
    orders,
  };
}

export function formatQueryParams(
  columnFields: Field[],
  rowFields: Field[],
  metricFields: Field[],
): Pick<QueryParams, 'groups' | 'orders' | 'aggregators'> {
  const groups: Groups = [...columnFields, ...rowFields].map((field) => ({
    id: field.id,
    titleName: field.titleName,
    valueType: field.dataType,
    alias: field.alias,
    dataDimensionsType: field.dataDimensionsType,
  }));

  const orders: Orders = [...columnFields, ...rowFields, ...metricFields]
    .filter((field) => field.sort)
    .map((field) => ({
      id: field.id,
      titleName: field.titleName,
      operator: field.sort!,
      valueType: field.dataType,
      alias: field.alias,
      dataDimensionsType: field.dataDimensionsType,
    }));

  const aggregators: Aggregators = metricFields.map((field) => ({
    id: field.id,
    titleName: field.titleName,
    operator: field.aggregator || 'SUM',
    valueType: field.dataType,
    dataDimensionsType: field.dataDimensionsType,
  }));

  return {
    groups,
    orders,
    aggregators,
  };
}
