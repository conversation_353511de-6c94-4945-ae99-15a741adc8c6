import { useRef, useState } from 'react';

import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history, request, useModel } from '@umijs/max';
import { Button, Space } from 'antd';

import DeleteButton from './components/ButtonDelete';
import type { TColumn, TRequestParams } from './types';

export default function SelfAnalysisList() {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const { userInfo, currentBrand } = useModel('@@initialState', (model) => ({
    userInfo: model.initialState?.userInfo,
    currentBrand: model.initialState?.currentBrandInfo,
  }));

  const columns: ProColumns<TColumn>[] = [
    {
      title: '视图名称',
      dataIndex: 'selfAnalysisName',
      copyable: true,
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      fieldProps: {
        showTime: false,
        format: 'YYYY-MM-DD',
      },
      hideInTable: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      align: 'center',
      render: (text, record, _, action) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              const config = {
                ...JSON.parse(record.config),
                selfAnalysisName: record.selfAnalysisName + '_copy',
              };
              history.push(`/adhoc/analysis?copyId=${record.id}`, config);
            }}
          >
            复制
          </Button>
          <Button
            type="link"
            onClick={() => {
              const config = {
                ...JSON.parse(record.config),
                selfAnalysisName: record.selfAnalysisName + '_edit',
              };
              history.push(`/adhoc/analysis?id=${record.id}`, config);
            }}
          >
            编辑
          </Button>
          <DeleteButton
            api={() =>
              request('/omnipilot/api/self-analysis/delete', {
                method: 'POST',
                data: {
                  brandId: currentBrand?.id,
                  brandName: currentBrand?.brandNameCn,
                  userId: userInfo?.id,
                  selfAnalysisId: [record.id],
                },
              })
            }
            onSuccess={action?.reload}
          />
        </Space>
      ),
    },
  ];

  return (
    <ProTable<TColumn, TRequestParams>
      columns={columns}
      actionRef={actionRef}
      cardBordered
      rowSelection={{
        selectedRowKeys,
        onChange: (keys) => setSelectedRowKeys(keys),
      }}
      request={async (params) => {
        const newParams = { ...params };
        newParams.startDate = params.createTime?.[0];
        newParams.endDate = params.createTime?.[1];
        delete newParams.createTime;
        const res = await request('/omnipilot/api/self-analysis/list', {
          method: 'POST',
          data: {
            ...newParams,
            brandId: currentBrand?.id,
            brandName: currentBrand?.brandNameCn,
            userId: userInfo?.id,
            pageNum: params.current,
          },
        });
        return {
          data: res?.data?.list,
          total: res?.data?.total,
        };
      }}
      rowKey="id"
      pagination={{
        pageSize: 10,
      }}
      search={{
        labelWidth: 'auto',
      }}
      dateFormatter="string"
      headerTitle="自助分析列表"
      toolBarRender={(action) => [
        <Button
          key="create"
          type="primary"
          onClick={() => history.push('/adhoc/analysis')}
        >
          新建自助分析
        </Button>,
        <DeleteButton
          key="multiDelete"
          api={() =>
            request('/omnipilot/api/self-analysis/delete', {
              method: 'POST',
              data: {
                brandId: currentBrand?.id,
                brandName: currentBrand?.brandNameCn,
                selfAnalysisId: selectedRowKeys,
                userId: userInfo?.id,
              },
            })
          }
          onSuccess={() => {
            setSelectedRowKeys([]);
            action?.reload?.();
          }}
          buttonProps={{
            disabled: selectedRowKeys.length === 0,
            type: 'primary',
            size: 'middle',
            danger: true,
          }}
        />,
      ]}
    />
  );
}
