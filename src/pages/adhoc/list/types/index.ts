export interface TColumn {
  brand: string;
  brandId: number;
  config: string;
  createBy: string;
  createTime: string;
  id: number;
  selfAnalysisName: string;
  param: string;
  updateBy: string;
  updateTime: string;
}

export interface TRequestParams {
  brandId?: string;
  brandName?: string;
  endDate?: string;
  pageNum?: number;
  pageSize?: number;
  selfAnalysisName?: string;
  createTime?: string;
  startDate?: string;
  userId?: string;
}

export interface EditRecord {
  id: number;
  name: string;
}
