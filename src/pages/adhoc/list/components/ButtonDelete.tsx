import { ExclamationCircleFilled } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import type { ButtonProps } from 'antd';
import { Button, Modal, message } from 'antd';

const { confirm } = Modal;

interface DeleteButtonProps {
  api: () => Promise<any>;
  onSuccess?: () => void;
  buttonProps?: ButtonProps;
}

export default function DeleteButton({
  api,
  onSuccess,
  buttonProps,
}: DeleteButtonProps) {
  const showDeleteConfirm = () => {
    confirm({
      title: '确定删除吗？',
      icon: <ExclamationCircleFilled />,
      content: '删除后无法恢复',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          await api();
          message.success('删除成功');
          onSuccess?.();
        } catch (error) {
          // 错误已经被统一处理
        }
      },
    });
  };

  return (
    <Button type="link" danger onClick={showDeleteConfirm} {...buttonProps}>
      删除
    </Button>
  );
}
