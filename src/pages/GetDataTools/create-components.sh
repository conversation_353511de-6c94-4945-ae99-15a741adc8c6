#!/bin/bash

TEMPLATE='import React from '"'"'react'"'"';
import EmbedIframe from '"'"'@/components/EmbedIframe'"'"';

const Component: React.FC = () => {
  return <EmbedIframe />;
};

export default Component;'

# 销售投资表现子组件
for dir in Meituan/salesInvestment/{industryIndex,marketShareChange,fullActivityData,cityOrderData,rtbProductData}; do
  echo "$TEMPLATE" > "$dir/index.tsx"
done

# 交易用户表现子组件
for dir in Meituan/transactionUsers/{brandTransactionUsers,brandProvinceUsers,customerProductPreference,betterUserDistribution,betterFlowAnalysis,taInsightCustomersDaily,taInsightCustomersMonthly,repurchaseFrequency}; do
  echo "$TEMPLATE" > "$dir/index.tsx"
done

# 人群表现子组件
for dir in Meituan/userGroups/{topTenGroups,topTenGroupsMetrics,topTenGroupsCustomers}; do
  echo "$TEMPLATE" > "$dir/index.tsx"
done

# 场景表现子组件
for dir in Meituan/scenarios/{sixteenScenarios,sixteenScenariosMetrics,sixteenScenariosCustomers,topTenGroupsSixteenScenarios}; do
  echo "$TEMPLATE" > "$dir/index.tsx"
done

# 供给表现子组件
for dir in Meituan/supply/{upcRetailerCity,subBrandRetailerCity}; do
  echo "$TEMPLATE" > "$dir/index.tsx"
done

echo "所有组件已创建完成" 