import sensors from 'sa-sdk-javascript';
import { UAParser } from 'ua-parser-js';

let prevPgeLeaveTime = performance.now();

type TValue =
  | string
  | number
  | boolean
  | (string | number)[]
  | undefined
  | null
  | (() => any);

type TInitOptions = {
  server_url: string;
};

class Trace {
  version = '1.26.18';

  // 单例
  instanceCreated = false;

  init(options: TInitOptions) {
    try {
      if (this.instanceCreated) return;
      this.instanceCreated = true;
      const parser = new UAParser(window.navigator.userAgent).getResult();
      this.registerPage({
        $$_model: parser.device.model,
        $$_os: parser.os.name,
        $$_os_version: parser.os.version,
        $$_browser: parser.browser.name,
        $$_browser_version: parser.browser.version,
        $$_ua: window.navigator.userAgent,
        $$_hostname: () => window.location.hostname,
        $$_pathname: () => window.location.pathname,
      });
      sensors.use('PageLeave', {
        // heartbeat_interval_time: 5,
        max_duration: 1 * 24 * 60 * 60,
        isCollectUrl: (url: string) => {
          // 防止route redirect时候触发两次，这个1000哪来的？pageview那throttle了两秒
          if (performance.now() - prevPgeLeaveTime < 1000) {
            return false;
          }
          prevPgeLeaveTime = performance.now();
          return true;
        },
      });
      sensors.init({
        server_url: options.server_url,
        is_track_single_page: false,
        send_type: 'ajax',
        cross_subdomain: true,
        heatmap: {
          // 自动采集点击事件
          clickmap: 'not_collect',
          // 自动采集停留事件
          scroll_notice_map: 'not_collect',
        },
        // show_log: process.env.NODE_ENV !== 'production' ? true : false,
        show_log: true,
      });
    } catch (error) {}

    // sensors.quick('autoTrack');
  }

  // 登录事件, 注册用户id
  login(userId: string) {
    sensors.login(userId);
  }

  // 登出
  logout() {
    // sensors.logout();
  }

  // 设置用户属性
  setProfile(properties: Record<string, TValue>) {
    // TODO: 先不用login设置用户属性, 这个神策有一套完整的用户体系逻辑, 自研的匹配不了
    // sensors.setProfile(properties);
    try {
      this.registerPage(properties);
    } catch (error) {}
  }

  // 设置公共属性(_开头, 避免和预置属性冲突)
  registerPage(properties: Record<string, TValue>) {
    try {
      sensors.registerPage(properties);
    } catch (error) {}
  }

  // 自定义事件
  track(eventName: string, properties?: Record<string, TValue>) {
    try {
      sensors.track(eventName, properties);
    } catch (error) {}
  }

  // 点击事件
  click() {}
}

export const trace = new Trace();
