import ExcelJS from 'exceljs';

export function getFileName(src: string) {
  const url = new URL(src);
  const pathname = url.pathname;
  const segments = pathname.split('/');
  const fileName = segments.pop();
  return fileName;
}

export function mergeProps<A, B>(a: A, b: B): B & A;
export function mergeProps<A, B, C>(a: A, b: B, c: C): C & B & A;
export function mergeProps(...items: any[]) {
  const ret: any = {};
  items.forEach((item) => {
    Object.keys(item).forEach((key) => {
      if (item[key] !== undefined) {
        ret[key] = item[key];
      }
    });
  });
  return ret;
}

export async function download(
  downloadUrl: string,
  data: any,
  options?: {
    filename?: string;
  },
) {
  const response = await fetch(downloadUrl, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json', // 表示请求返回的是二进制文件
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('网络请求失败');
  }

  const blob = await response.blob(); // 将响应数据转换为二进制Blob对象
  const url = window.URL.createObjectURL(blob); // 创建一个对象URL
  const a = document.createElement('a'); // 创建一个隐藏的a元素，用于触发下载

  a.download = options?.filename || '下载.xlsx';
  a.style.display = 'none';
  a.href = url; // 将生成的URL设置为a.href属性
  // 设置文件名，可以根据需要修改
  document.body.appendChild(a); // 将a元素添加到页面中
  a.click(); // 模拟点击a元素，触发下载

  URL.revokeObjectURL(a.href); // 释放对象URL
  a.remove(); // 下载完成后移除a元素
}

export async function exportS2PivotTable(data: {
  data: any[];
  fields: {
    rows: string[];
    columns: string[];
    values: string[];
  };
  meta: Array<{ field: string; name: string }>;
}) {
  // 辅助函数：生成所有可能的组合
  function generateCombinations(arrays: any[][]): any[][] {
    if (arrays.length === 0) return [[]];
    const result: any[][] = [];
    const otherCombinations = generateCombinations(arrays.slice(1));
    arrays[0].forEach((item) => {
      otherCombinations.forEach((combination) => {
        result.push([item, ...combination]);
      });
    });
    return result;
  }
  // 确保fields存在默认值
  const fields = {
    rows: data.fields?.rows || [],
    columns: data.fields?.columns || [],
    values: data.fields?.values || [],
  };

  // 如果没有必要的字段，直接返回
  if (!fields.values.length) {
    console.error('没有指定度量值字段');
    return;
  }

  // 1. 收集唯一的行和列值
  const uniqueRowValues: { [field: string]: Set<string> } = {};
  const uniqueColValues: { [field: string]: Set<string> } = {};

  fields.rows.forEach((field) => {
    uniqueRowValues[field] = new Set();
  });
  fields.columns.forEach((field) => {
    uniqueColValues[field] = new Set();
  });

  // 收集所有唯一值
  data.data.forEach((row) => {
    fields.rows.forEach((field) => {
      uniqueRowValues[field].add(String(row[field] || ''));
    });
    fields.columns.forEach((field) => {
      uniqueColValues[field].add(String(row[field] || ''));
    });
  });

  // 2. 创建数据透视表
  const pivotData: { [key: string]: { [key: string]: number } } = {};

  // 生成所有可能的行组合
  const rowCombinations =
    fields.rows.length > 0
      ? generateCombinations(
          fields.rows.map((field) => Array.from(uniqueRowValues[field])),
        )
      : [[]];

  // 生成所有可能的列组合
  const colCombinations =
    fields.columns.length > 0
      ? generateCombinations(
          fields.columns.map((field) => Array.from(uniqueColValues[field])),
        )
      : [[]];

  // 初始化透视表数据结构并记录实际数据
  const validRowKeys = new Set<string>();
  const validColKeys = new Set<string>();

  // 填充数据并记录有效的行列
  data.data.forEach((row) => {
    const rowKey = fields.rows
      .map((field) => String(row[field] || ''))
      .join('-');
    const colKey = fields.columns
      .map((field) => String(row[field] || ''))
      .join('-');

    if (!pivotData[rowKey]) {
      pivotData[rowKey] = {};
    }
    if (!pivotData[rowKey][colKey]) {
      pivotData[rowKey][colKey] = 0;
    }

    const value = Number(row[fields.values[0]]) || 0;
    if (value !== 0) {
      pivotData[rowKey][colKey] += value;
      validRowKeys.add(rowKey);
      validColKeys.add(colKey);
    }
  });

  // 过滤掉没有数据的行和列组合
  const filteredRowCombinations = rowCombinations.filter((combo) =>
    validRowKeys.has(combo.join('-')),
  );
  const filteredColCombinations = colCombinations.filter((combo) =>
    validColKeys.has(combo.join('-')),
  );

  // 3. 创建Excel工作簿
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Pivot Table');

  // 4. 构建表头
  // 获取字段显示名称
  const getFieldName = (field: string) =>
    data.meta?.find((m) => m.field === field)?.name || field;

  // 构建列标题
  const colHeaderRows: string[][] = [];
  if (fields.columns.length > 0) {
    // 为每个列维度创建一行标题
    fields.columns.forEach((_, index) => {
      const headerRow: string[] = [];
      // 添加行维度的空白单元格
      headerRow.push(...Array(fields.rows.length).fill(''));

      filteredColCombinations.forEach((combo) => {
        headerRow.push(combo[index] || '');
      });
      colHeaderRows.push(headerRow);
    });
  }

  // 添加度量值标题行
  const measureRow = [];
  measureRow.push(...fields.rows.map(getFieldName));
  if (filteredColCombinations.length > 0) {
    measureRow.push(
      ...Array(filteredColCombinations.length).fill(
        getFieldName(fields.values[0]),
      ),
    );
  } else {
    measureRow.push(getFieldName(fields.values[0]));
  }
  colHeaderRows.push(measureRow);

  // 写入列标题
  colHeaderRows.forEach((row) => {
    worksheet.addRow(row);
  });

  // 5. 写入数据行
  filteredRowCombinations.forEach((rowCombo) => {
    const rowKey = rowCombo.join('-');
    const dataRow = [...rowCombo];

    if (filteredColCombinations.length > 0) {
      filteredColCombinations.forEach((colCombo) => {
        const colKey = colCombo.join('-');
        dataRow.push(pivotData[rowKey][colKey] || 0);
      });
    } else {
      dataRow.push(pivotData[rowKey][''] || 0);
    }

    worksheet.addRow(dataRow);
  });

  // 6. 设置样式
  const lastRow = worksheet.rowCount;
  const lastCol = worksheet.columnCount;

  // 设置边框和对齐
  for (let row = 1; row <= lastRow; row++) {
    for (let col = 1; col <= lastCol; col++) {
      const cell = worksheet.getCell(row, col);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    }
  }

  // 合并列标题单元格
  if (fields.columns.length > 0) {
    for (let colIdx = 0; colIdx < fields.columns.length; colIdx++) {
      const startCol = fields.rows.length + 1;
      let currentValue = worksheet.getCell(colIdx + 1, startCol).value;
      let span = 1;

      for (let col = startCol + 1; col <= lastCol; col++) {
        const cell = worksheet.getCell(colIdx + 1, col);
        if (cell.value === currentValue) {
          span++;
        } else {
          if (span > 1) {
            worksheet.mergeCells(colIdx + 1, col - span, colIdx + 1, col - 1);
          }
          currentValue = cell.value;
          span = 1;
        }
      }
      // 处理最后一组
      if (span > 1) {
        worksheet.mergeCells(
          colIdx + 1,
          lastCol - span + 1,
          colIdx + 1,
          lastCol,
        );
      }
    }
  }

  // 合并行标题单元格
  if (fields.rows.length > 0) {
    for (let col = 1; col <= fields.rows.length; col++) {
      const startRow = fields.columns.length + 1;
      let currentValue = worksheet.getCell(startRow, col).value;
      let span = 1;

      for (let row = startRow + 1; row <= lastRow; row++) {
        const cell = worksheet.getCell(row, col);
        if (cell.value === currentValue) {
          span++;
        } else {
          if (span > 1) {
            worksheet.mergeCells(row - span, col, row - 1, col);
          }
          currentValue = cell.value;
          span = 1;
        }
      }
      // 处理最后一组
      if (span > 1) {
        worksheet.mergeCells(lastRow - span + 1, col, lastRow, col);
      }
    }
  }

  try {
    // 生成并下载文件
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'pivot_table.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('导出失败:', error);
  }
}

export const findFirstValidRoute = (routeList: any[], access: any[]): any => {
  for (const route of routeList) {
    // 检查当前路由是否满足条件:
    // 1. 有 path、component、access
    // 2. 没有子路由或子路由为空数组
    // 3. 有访问权限
    if (
      route.path &&
      route.component &&
      route.access &&
      (!route.routes || route.routes.length === 0) &&
      access?.some((res: any) => res.resPath?.includes(route.path))
    ) {
      return route;
    }

    // 如果当前路由有子路由,递归查找
    if (route.routes && route.routes.length > 0) {
      const found = findFirstValidRoute(route.routes, access);
      if (found) return found;
    }
  }
  return null;
};
