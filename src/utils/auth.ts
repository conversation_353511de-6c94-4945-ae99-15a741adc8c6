import {
  AUTHORI<PERSON>AT<PERSON>ON_TOKEN_NAME,
  DOMAIN,
  LOGIN_PAGE_PATH,
  USER_ID_STORAGE_KEY,
  USER_INFO_STORAGE_KEY,
} from '@/constants';
import { history } from '@umijs/max';
import Cookies from 'js-cookie';
import qs from 'qs';

export function logout(params?: { beforeLogout?: () => void }) {
  params?.beforeLogout?.();
  const redirect = window.location.pathname.includes(LOGIN_PAGE_PATH)
    ? '/'
    : window.location.pathname;
  Cookies.remove(AUTHORIZATUION_TOKEN_NAME, {
    domain: DOMAIN,
  });
  localStorage.removeItem(USER_INFO_STORAGE_KEY);
  localStorage.removeItem(USER_ID_STORAGE_KEY);
  history.replace({
    pathname: LOGIN_PAGE_PATH,
    search: qs.stringify({
      redirect: redirect,
    }),
  });
}
