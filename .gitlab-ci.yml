stages:
  - deploy

variables:
  SERVER_IP: '**************'
  SERVER_USER: 'root'
  SERVER_PASSWORD: 'Yishu_server2024@'

before_script:
  - apt-get update -qq && apt-get install -qqy sshpass

deploy:
  stage: deploy
  only:
    - release
  script:
    - |
      sshpass -p $SERVER_PASSWORD ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP << 'EOF'
      cd /data/github/airtb-admin-web
      git pull
      pnpm install
      pnpm run build

      rm -rf /data/server/bak/airtb_admin_web
      mv /data/server/airtb_admin_web /data/server/bak

      mv /data/github/airtb-admin-web/dist /data/server/airtb_admin_web
      # ln -s /data/server/datart/files/resources /data/server/datart_web/resources

      echo "SUCCESS"
      EOF
